# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

java.lang.Object.wait()
java.lang.Object.wait(long)
java.lang.Object.wait(long,int)
java.lang.Object.notify()
java.lang.Object.notifyAll()

java.lang.Class.getClassLoader()
java.lang.Class.newInstance()
java.lang.Class.forName(java.lang.String)
java.lang.Class.forName(java.lang.String,boolean,java.lang.ClassLoader)

java.lang.reflect.Constructor.newInstance([Ljava.lang.Object;)

java.lang.reflect.Method.invoke(java.lang.Object,[Ljava.lang.Object;)

java.lang.reflect.Field.set(java.lang.Object,java.lang.Object)
java.lang.reflect.Field.setBoolean(java.lang.Object,boolean)
java.lang.reflect.Field.setByte(java.lang.Object,byte)
java.lang.reflect.Field.setChar(java.lang.Object,char)
java.lang.reflect.Field.setDouble(java.lang.Object,double)
java.lang.reflect.Field.setFloat(java.lang.Object,float)
java.lang.reflect.Field.setInt(java.lang.Object,int)
java.lang.reflect.Field.setLong(java.lang.Object,long)
java.lang.reflect.Field.setShort(java.lang.Object,short)

java.lang.reflect.AccessibleObject.setAccessible([Ljava.lang.reflect.AccessibleObject;,boolean)
java.lang.reflect.AccessibleObject.setAccessible(boolean)

java.lang.Thread.destroy()
java.lang.Thread.getContextClassLoader()
java.lang.Thread.interrupt()
java.lang.Thread.join()
java.lang.Thread.join(long)
java.lang.Thread.join(long,int)
java.lang.Thread.resume()
java.lang.Thread.run()
java.lang.Thread.setContextClassLoader(java.lang.ClassLoader)
java.lang.Thread.setDaemon(boolean)
java.lang.Thread.setName(java.lang.String)
java.lang.Thread.setPriority(int)
java.lang.Thread.sleep(long)
java.lang.Thread.sleep(long,int)
java.lang.Thread.start()
java.lang.Thread.stop()
java.lang.Thread.stop(java.lang.Throwable)
java.lang.Thread.suspend()

java.lang.ThreadGroup.allowThreadSuspension(boolean)
java.lang.ThreadGroup.destroy()
java.lang.ThreadGroup.interrupt()
java.lang.ThreadGroup.resume()
java.lang.ThreadGroup.setDaemon(boolean)
java.lang.ThreadGroup.setMaxPriority(int)
java.lang.ThreadGroup.stop()
java.lang.Thread.suspend()

java.lang.Runtime.addShutdownHook(java.lang.Thread)
java.lang.Runtime.exec(java.lang.String)
java.lang.Runtime.exec([Ljava.lang.String;)
java.lang.Runtime.exec([Ljava.lang.String;,[Ljava.lang.String;)
java.lang.Runtime.exec([Ljava.lang.String;,[Ljava.lang.String;,java.io.File)
java.lang.Runtime.exec(java.lang.String,[Ljava.lang.String;)
java.lang.Runtime.exec(java.lang.String,[Ljava.lang.String;,java.io.File)
java.lang.Runtime.exit(int)
java.lang.Runtime.halt(int)
java.lang.Runtime.load(java.lang.String)
java.lang.Runtime.loadLibrary(java.lang.String)
java.lang.Runtime.removeShutdownHook(java.lang.Thread)
java.lang.Runtime.traceInstructions(boolean)
java.lang.Runtime.traceMethodCalls(boolean)

java.lang.System.exit(int)
java.lang.System.load(java.lang.String)
java.lang.System.loadLibrary(java.lang.String)
java.lang.System.runFinalizersOnExit(boolean)
java.lang.System.setErr(java.io.PrintStream)
java.lang.System.setIn(java.io.InputStream)
java.lang.System.setOut(java.io.PrintStream)
java.lang.System.setProperties(java.util.Properties)
java.lang.System.setProperty(java.lang.String,java.lang.String)
java.lang.System.setSecurityManager(java.lang.SecurityManager)
