 (function(){
   if (window.NEBULALOGHASADDEDSTAYEVENT) {
     return;
   };
   function onDOMReady(callback){
     var readyRE = /complete|loaded|interactive/;
     if(readyRE.test(document.readyState)) {
       setTimeout(function() {
         callback();
       }, 1);
     } else {
       document.defaultView.addEventListener('DOMContentLoaded', function () {
         callback();
       }, false);
     }
   }

   
   function getPageParam(duration, scollY) {
    // console.log('report scoll Top', scollY)
     return {
       viewport_width: window.innerWidth,
       viewport_position: scollY,
       viewport_height: window.innerHeight,
       url: window.location.href,
       title: sessionStorage.getItem('H5_PAGE_TITLE') || document.title,
       url_path: window.location.pathname,
       eventDuration: duration / 1000
     };
   }

   onDOMReady(function(){
     var window_stay_timestamp = new Date().valueOf();
     var timer = null;
     const scrollFunc = function(e) {
      if(timer !== null) {
          clearTimeout(timer);
      }
      timer = setTimeout(function() {
        const currentTime = new Date().valueOf();
        var duration = currentTime - window_stay_timestamp;
       // console.log('scrollstop', duration)
        if (currentTime - window_stay_timestamp > 4000) {
          var duration = currentTime - window_stay_timestamp;
          const scollY = container.scrollTop || window.scrollY;
         // console.log('report', duration,getPageParam(duration, scollY))
          AlipayJSBridge.call("h5PageScroll", getPageParam(duration, scollY));
        }
        window_stay_timestamp = currentTime;
      }, 150);
    };

     const container = document.getElementById('main') || window;
     container.addEventListener('scroll', scrollFunc);

     document.defaultView.addEventListener('onbeforeunload', function () {
       var duration = new Date().valueOf() - window_stay_timestamp;
       const scollY = document.getElementById('mainBox').scrollTop || window.scrollY;
       AlipayJSBridge.call("h5PageScroll", getPageParam(duration, scollY));
       container.removeEventListener('scroll', scrollFunc);
     }, false);
   });
   window.NEBULALOGHASADDEDSTAYEVENT = true;
 })();
