window.componentsManager||function(){function e(e){if(e)return e.replace(/^\s*|\s*$/,"")}function t(e,t){for(var n=e.className.split(/\s+/),r=0;r<n.length;r++)if(n[r]===t)return!0;return!1}var n={renderV2Internal:function(n,r){var o={frame:{},data:{},props:{}};o.id=e(n.getAttribute("id"));if(n.parentElement&&t(n.parentElement,'nbcomponent')){var a=e(n.parentElement.getAttribute("id"));a&&(o.parentId=a)}o.type=e(n.getAttribute("nbcomponent-type"));var i,c=document.querySelector("meta[name=viewport]");c&&(i=c.getAttribute("content"));var d="0.33";window.devicePixelRatio&&window.devicePixelRatio>2&&(d="0.4235033");if(i){var m=i.split(",");for(var l in m){var p=m[l].split("=");if(p&&p.length>1){var s=p[0];if(/initial-scale/.test(s)){d=p[1];break}}}}o.scale=d;var u=n.getBoundingClientRect();o.frame.width=u.width;o.frame.height=u.height;var f=0;document.documentElement&&document.documentElement.scrollLeft?f=document.documentElement.scrollLeft:document.body&&(f=document.body.scrollLeft);o.frame.realX=u.left+f;var v=0;document.documentElement&&document.documentElement.scrollTop?v=document.documentElement.scrollTop:document.body&&(v=document.body.scrollTop);o.frame.realY=u.top+v;""===n.style.zIndex?o.frame.zindex="-9998":o.frame.zindex=n.style.zIndex;""===n.style.zIndex&&""!=n.parentElement.style.zIndex&&(o.frame.zindex=(parseInt(n.parentElement.style.zIndex)+1).toString());var b=e(n.getAttribute("nbcomponent-data"));if(b)try{y=JSON.parse(b);for(var g in y)o.data[g]=y[g]}catch(e){}var h=e(n.getAttribute("nbcomponent-props"));if(h)try{var y=JSON.parse(h);for(var g in y)o.props[g]=y[g]}catch(e){}r.push(o)},renderV2:function(t,n,r){var o=[],a=document.getElementById(t);if(a){console.log('renderV2',a,n,r);if(r)try{var i=a.hasAttribute('nbcomponent-props')?JSON.parse(e(a.getAttribute('nbcomponent-props'))):{};for(var c in r)i[c]=r[c];a.setAttribute('nbcomponent-props',JSON.stringify(i))}catch(e){}if(n)try{var d=a.hasAttribute('nbcomponent-data')?JSON.parse(e(a.getAttribute('nbcomponent-data'))):{};for(var m in n)d[m]=n[m];a.setAttribute('nbcomponent-data',JSON.stringify(d))}catch(e){}this.traverseTree(a,o)}return o},traverseTree:function(n,r){t(n,'nbcomponent')&&this.renderV2Internal(n,r);var o=n.getAttribute("nbcomponent-type");o&&"string"==typeof o&&(o=e(o));if(n.children&&!(n.children.length<=0)&&("string"!=typeof o||"image"!==o&&"text"!==o))for(var a=0;a<n.children.length;a++)this.traverseTree(n.children[a],r)}};window.componentsManager=n;var r=document.createEvent('Event');r.initEvent('componentsManagerReady',!0,!1);document.dispatchEvent(r);console.log("dispatchEvent componentsManagerReady")}();
// do not modify
