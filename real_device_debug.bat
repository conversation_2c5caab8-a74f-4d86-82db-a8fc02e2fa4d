@echo off
chcp 65001 >nul
echo 📱 翼支付真机调试启动脚本
echo ================================

REM 设置ADB路径
set "ADB_PATH=.\android-tools\platform-tools\adb.exe"

echo 📋 第1步: 检查Frida安装...
frida --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Frida未安装，正在安装...
    pip install frida-tools
    if %errorlevel% neq 0 (
        echo ❌ Frida安装失败
        pause
        exit /b 1
    )
)
echo ✅ Frida已安装

echo.
echo 📋 第2步: 检查ADB工具...
if exist "%ADB_PATH%" (
    echo ✅ 便携版ADB已存在
) else (
    echo ❌ ADB工具不存在
    echo 💡 请按照以下步骤手动配置ADB:
    echo    1. 访问: https://developer.android.com/studio/releases/platform-tools
    echo    2. 下载 "SDK Platform-Tools for Windows"
    echo    3. 解压到当前目录的 android-tools 文件夹
    echo    4. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo.
echo 📋 第3步: 检查设备连接...
echo 💡 请确保:
echo    1. 手机已通过USB连接
echo    2. 已开启USB调试
echo    3. 已授权此计算机
echo.
pause

echo 检查连接的设备...
"%ADB_PATH%" devices
echo.

echo ❓ 能看到您的设备显示为 "device" 状态吗?
echo    - 如果是 "unauthorized"，请在手机上点击"始终允许"
echo    - 如果没有设备，请检查USB连接和驱动
echo    - 如果显示 "device"，说明连接成功！
echo.
set /p device_ok=设备连接正常吗? (y/n): 
if /i not "%device_ok%"=="y" (
    echo 请解决设备连接问题后重新运行脚本
    pause
    exit /b 1
)

echo.
echo 📋 第4步: 检查翼支付应用...
"%ADB_PATH%" shell pm list packages | findstr bestpay
if %errorlevel% neq 0 (
    echo ❌ 翼支付应用未安装
    echo 💡 请在手机上安装翼支付应用后重新运行
    pause
    exit /b 1
)
echo ✅ 翼支付应用已安装

echo.
echo 📋 第5步: 设置端口转发...
"%ADB_PATH%" forward tcp:27042 tcp:27042
echo ✅ 端口转发已设置

echo.
echo 📋 第6步: 测试Frida连接...
echo 正在测试Frida连接...
frida-ps -U >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frida连接成功
) else (
    echo ⚠️ Frida连接测试失败，但这可能是正常的
    echo 💡 我们将尝试直接启动Hook
)

echo.
echo 📋 第7步: 选择调试模式...
echo.
echo 🎯 请选择调试模式:
echo    1. 启动应用并Hook (推荐)
echo    2. 附加到运行中的应用
echo    3. 使用反调试绕过模式
echo.
set /p mode=请选择模式 (1-3): 

echo.
echo 🚀 启动Hook调试...
echo.
echo 📱 重要提醒:
echo    1. 保持此窗口打开
echo    2. 在手机上操作翼支付应用
echo    3. 进入权益商城并刷新页面
echo    4. 观察此窗口的输出
echo    5. 当看到包含AES密钥的输出时，立即复制保存！
echo.
echo 🔍 Hook脚本正在启动...
echo ================================
echo.

if "%mode%"=="1" (
    echo 🚀 模式1: 启动应用并Hook
    frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause
) else if "%mode%"=="2" (
    echo 📎 模式2: 附加到运行中的应用
    echo 💡 请先在手机上打开翼支付应用
    pause
    frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js
) else if "%mode%"=="3" (
    echo 🛡️ 模式3: 反调试绕过模式
    frida -U -f com.chinatelecom.bestpayclient -l bypass_detection.js -l bestpay_crypto_hook.js --no-pause
) else (
    echo ❌ 无效选择，使用默认模式1
    frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause
)

echo.
echo ================================
echo 🎉 Hook会话已结束
echo.
echo 📊 如果成功捕获到数据，请运行:
echo    python data_collector.py
echo.
echo 🔄 要重新开始调试，请重新运行此脚本
echo ================================
pause
