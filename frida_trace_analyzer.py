#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
frida-trace系统调用跟踪分析工具
使用frida-trace深度分析翼支付的函数调用
"""

import subprocess
import time
import os
import json
from datetime import datetime

class FridaTraceAnalyzer:
    def __init__(self):
        self.target_package = "com.chinatelecom.bestpayclient"
        self.output_dir = "frida_trace_analysis"
        
        print("🔍 frida-trace系统调用跟踪分析工具")
        print("🎯 使用frida-trace深度分析翼支付的函数调用")
        print("=" * 60)
    
    def run_command(self, cmd, timeout=30):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='ignore')
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def check_frida_trace_availability(self):
        """检查frida-trace是否可用"""
        print("\n🔧 检查frida-trace可用性...")
        
        success, stdout, stderr = self.run_command("frida-trace --help")
        if success:
            print("   ✅ frida-trace可用")
            return True
        else:
            print(f"   ❌ frida-trace不可用: {stderr}")
            return False
    
    def create_trace_scripts(self):
        """创建跟踪脚本"""
        print("\n📝 创建frida-trace跟踪脚本...")
        
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 1. 系统调用跟踪脚本
        syscall_script = '''
// 系统调用跟踪脚本
console.log("🔍 系统调用跟踪启动");

// 跟踪关键系统调用
var syscalls = [
    "open", "openat", "read", "write", "close",
    "connect", "sendto", "recvfrom", "socket",
    "ptrace", "kill", "access", "stat", "readlink",
    "mmap", "mprotect", "munmap"
];

syscalls.forEach(function(syscall) {
    try {
        var funcPtr = Module.findExportByName(null, syscall);
        if (funcPtr) {
            Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                    console.log("[" + syscall + "] 调用");
                    
                    // 特殊处理某些系统调用
                    if (syscall === "open" || syscall === "openat") {
                        var path = syscall === "open" ? args[0] : args[1];
                        var pathStr = path.readCString();
                        if (pathStr) {
                            console.log("  路径: " + pathStr);
                        }
                    }
                },
                onLeave: function(retval) {
                    // 记录返回值
                }
            });
            console.log("✅ " + syscall + " 跟踪已设置");
        }
    } catch(e) {
        console.log("❌ " + syscall + " 跟踪失败: " + e);
    }
});
'''
        
        with open(f"{self.output_dir}/syscall_trace.js", "w", encoding="utf-8") as f:
            f.write(syscall_script)
        
        # 2. 加密函数跟踪脚本
        crypto_script = '''
// 加密函数跟踪脚本
console.log("🔐 加密函数跟踪启动");

// 跟踪OpenSSL函数
var openssl_funcs = [
    "AES_encrypt", "AES_decrypt", "AES_set_encrypt_key", "AES_set_decrypt_key",
    "RSA_public_encrypt", "RSA_private_decrypt",
    "EVP_EncryptInit", "EVP_DecryptInit", "EVP_CipherUpdate", "EVP_CipherFinal",
    "MD5_Init", "MD5_Update", "MD5_Final",
    "SHA1_Init", "SHA1_Update", "SHA1_Final"
];

openssl_funcs.forEach(function(func) {
    try {
        var funcPtr = Module.findExportByName("libssl.so", func) || 
                     Module.findExportByName("libcrypto.so", func) ||
                     Module.findExportByName("libopenssl.so", func);
        
        if (funcPtr) {
            Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                    console.log("🔑 [" + func + "] 调用");
                    
                    // 记录密钥和数据
                    if (func.includes("encrypt") || func.includes("decrypt")) {
                        try {
                            var data = Memory.readByteArray(args[1], Math.min(32, 16));
                            console.log("  数据: " + hexdump(data, {ansi: true}));
                        } catch(e) {}
                    }
                },
                onLeave: function(retval) {
                    console.log("  返回值: " + retval);
                }
            });
            console.log("✅ " + func + " 跟踪已设置");
        }
    } catch(e) {
        console.log("❌ " + func + " 跟踪失败: " + e);
    }
});

// 跟踪国密算法
var sm_funcs = ["sm2_encrypt", "sm2_decrypt", "sm3_hash", "sm4_encrypt", "sm4_decrypt"];

sm_funcs.forEach(function(func) {
    try {
        var funcPtr = Module.findExportByName("libantssm.so", func) ||
                     Module.findExportByName("libTencentSM.so", func);
        
        if (funcPtr) {
            Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                    console.log("🇨🇳 [" + func + "] 国密算法调用");
                },
                onLeave: function(retval) {
                    console.log("  返回值: " + retval);
                }
            });
            console.log("✅ " + func + " 国密跟踪已设置");
        }
    } catch(e) {
        console.log("❌ " + func + " 国密跟踪失败: " + e);
    }
});
'''
        
        with open(f"{self.output_dir}/crypto_trace.js", "w", encoding="utf-8") as f:
            f.write(crypto_script)
        
        # 3. 网络函数跟踪脚本
        network_script = '''
// 网络函数跟踪脚本
console.log("🌐 网络函数跟踪启动");

Java.perform(function() {
    // Hook OkHttp
    try {
        var RequestBody = Java.use("okhttp3.RequestBody");
        var originalCreate = RequestBody.create.overload('okhttp3.MediaType', 'java.lang.String');
        
        originalCreate.implementation = function(mediaType, content) {
            console.log("\\n🌐 [OkHttp请求]");
            console.log("Content-Type: " + mediaType);
            
            if (content.includes('"data":') || content.includes('"key":') || content.includes('"sign":')) {
                console.log("🎯 发现加密请求!");
                console.log("请求内容: " + content);
            } else if (content.length < 500) {
                console.log("请求内容: " + content);
            } else {
                console.log("请求长度: " + content.length + " 字符");
            }
            
            return originalCreate.call(this, mediaType, content);
        };
        
        console.log("✅ OkHttp跟踪已设置");
    } catch(e) {
        console.log("❌ OkHttp跟踪失败: " + e);
    }
    
    // Hook HttpURLConnection
    try {
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        var originalGetOutputStream = HttpURLConnection.getOutputStream;
        
        originalGetOutputStream.implementation = function() {
            var url = this.getURL().toString();
            console.log("\\n🔗 [HttpURLConnection] " + url);
            
            return originalGetOutputStream.call(this);
        };
        
        console.log("✅ HttpURLConnection跟踪已设置");
    } catch(e) {
        console.log("❌ HttpURLConnection跟踪失败: " + e);
    }
});
'''
        
        with open(f"{self.output_dir}/network_trace.js", "w", encoding="utf-8") as f:
            f.write(network_script)
        
        print("   ✅ 跟踪脚本已创建")
        return True
    
    def run_comprehensive_trace(self):
        """运行综合跟踪"""
        print("\n🚀 运行综合frida-trace分析...")
        
        # 创建输出目录
        trace_output = f"{self.output_dir}/trace_output_{int(time.time())}"
        os.makedirs(trace_output, exist_ok=True)
        
        # 构建frida-trace命令
        trace_commands = [
            {
                "name": "系统调用跟踪",
                "cmd": f"frida-trace -U {self.target_package} -i 'open*' -i 'read*' -i 'write*' -i 'connect*' -i 'send*' -i 'recv*' -o {trace_output}/syscalls.log",
                "duration": 30
            },
            {
                "name": "加密函数跟踪", 
                "cmd": f"frida-trace -U {self.target_package} -I 'libssl.so' -I 'libcrypto.so' -I 'libantssm.so' -o {trace_output}/crypto.log",
                "duration": 30
            },
            {
                "name": "Java方法跟踪",
                "cmd": f"frida-trace -U {self.target_package} -j '*!*encrypt*' -j '*!*decrypt*' -j '*!*cipher*' -o {trace_output}/java.log",
                "duration": 30
            }
        ]
        
        results = []
        
        for trace_config in trace_commands:
            print(f"\n🔍 执行{trace_config['name']}...")
            print(f"   命令: {trace_config['cmd']}")
            print(f"   时长: {trace_config['duration']}秒")
            print("   💡 请在手机上操作翼支付应用...")
            
            try:
                # 启动frida-trace
                process = subprocess.Popen(
                    trace_config['cmd'], 
                    shell=True, 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # 等待指定时间
                time.sleep(trace_config['duration'])
                
                # 停止跟踪
                process.terminate()
                stdout, stderr = process.communicate(timeout=5)
                
                results.append({
                    "name": trace_config['name'],
                    "success": True,
                    "output": stdout,
                    "error": stderr
                })
                
                print(f"   ✅ {trace_config['name']}完成")
                
            except Exception as e:
                print(f"   ❌ {trace_config['name']}失败: {e}")
                results.append({
                    "name": trace_config['name'],
                    "success": False,
                    "error": str(e)
                })
        
        return results, trace_output
    
    def run_custom_trace(self):
        """运行自定义跟踪"""
        print("\n🎯 运行自定义frida-trace分析...")
        
        # 使用我们创建的脚本
        scripts = [
            f"{self.output_dir}/syscall_trace.js",
            f"{self.output_dir}/crypto_trace.js", 
            f"{self.output_dir}/network_trace.js"
        ]
        
        for script in scripts:
            if os.path.exists(script):
                script_name = os.path.basename(script)
                print(f"\n🔍 执行{script_name}...")
                
                cmd = f"frida -U {self.target_package} -l {script}"
                print(f"   命令: {cmd}")
                print("   💡 请在手机上操作翼支付应用...")
                print("   ⏰ 30秒后自动停止")
                
                try:
                    process = subprocess.Popen(
                        cmd,
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )
                    
                    # 等待30秒
                    time.sleep(30)
                    
                    # 停止跟踪
                    process.terminate()
                    stdout, stderr = process.communicate(timeout=5)
                    
                    # 保存输出
                    output_file = f"{self.output_dir}/{script_name}.output"
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(f"=== STDOUT ===\n{stdout}\n\n=== STDERR ===\n{stderr}")
                    
                    print(f"   ✅ {script_name}完成，输出已保存")
                    
                except Exception as e:
                    print(f"   ❌ {script_name}失败: {e}")
    
    def analyze_trace_results(self, results, trace_output):
        """分析跟踪结果"""
        print(f"\n📊 分析跟踪结果...")
        
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "target_package": self.target_package,
            "trace_results": results,
            "findings": []
        }
        
        # 分析每个跟踪结果
        for result in results:
            if result["success"]:
                findings = self.extract_findings(result["output"], result["name"])
                analysis["findings"].extend(findings)
        
        # 保存分析结果
        analysis_file = f"{self.output_dir}/frida_trace_analysis.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, indent=2, ensure_ascii=False)
        
        print(f"   💾 分析结果已保存: {analysis_file}")
        
        # 生成总结
        self.generate_trace_summary(analysis)
    
    def extract_findings(self, output, trace_name):
        """从跟踪输出中提取发现"""
        findings = []
        
        if not output:
            return findings
        
        lines = output.split('\n')
        
        for line in lines:
            # 查找有趣的模式
            if any(keyword in line.lower() for keyword in [
                'encrypt', 'decrypt', 'cipher', 'crypto', 'aes', 'rsa', 'sm2',
                'bestpay', 'mapi-h5', 'data', 'key', 'sign'
            ]):
                findings.append({
                    "trace_type": trace_name,
                    "content": line.strip(),
                    "category": self.categorize_finding(line)
                })
        
        return findings
    
    def categorize_finding(self, line):
        """对发现进行分类"""
        line_lower = line.lower()
        
        if any(keyword in line_lower for keyword in ['encrypt', 'decrypt', 'cipher', 'aes', 'rsa']):
            return "加密操作"
        elif any(keyword in line_lower for keyword in ['bestpay', 'mapi-h5', 'chinatelecom']):
            return "网络通信"
        elif any(keyword in line_lower for keyword in ['data', 'key', 'sign']):
            return "数据处理"
        elif any(keyword in line_lower for keyword in ['open', 'read', 'write', 'access']):
            return "文件操作"
        else:
            return "其他"
    
    def generate_trace_summary(self, analysis):
        """生成跟踪总结"""
        summary_file = f"{self.output_dir}/frida_trace_summary.md"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("# frida-trace跟踪分析报告\n\n")
            f.write(f"## 分析时间\n{analysis['timestamp']}\n\n")
            f.write(f"## 目标应用\n{analysis['target_package']}\n\n")
            
            f.write("## 跟踪结果\n")
            for result in analysis['trace_results']:
                status = "✅ 成功" if result['success'] else "❌ 失败"
                f.write(f"- {result['name']}: {status}\n")
            f.write("\n")
            
            f.write("## 关键发现\n")
            findings_by_category = {}
            for finding in analysis['findings']:
                category = finding['category']
                if category not in findings_by_category:
                    findings_by_category[category] = []
                findings_by_category[category].append(finding)
            
            for category, findings in findings_by_category.items():
                f.write(f"### {category} ({len(findings)}个)\n")
                for finding in findings[:10]:  # 显示前10个
                    f.write(f"- {finding['content'][:100]}...\n")
                f.write("\n")
        
        print(f"   💾 跟踪总结已保存: {summary_file}")
    
    def run_analysis(self):
        """运行完整分析"""
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 检查frida-trace可用性
        if not self.check_frida_trace_availability():
            print("❌ frida-trace不可用")
            return False
        
        # 创建跟踪脚本
        if not self.create_trace_scripts():
            print("❌ 跟踪脚本创建失败")
            return False
        
        print("\n🎯 选择跟踪模式:")
        print("   1. 综合跟踪 (使用frida-trace命令)")
        print("   2. 自定义跟踪 (使用自定义脚本)")
        print("   3. 两种模式都执行")
        
        choice = input("\n请选择模式 (1-3): ").strip()
        
        if choice in ['1', '3']:
            # 综合跟踪
            results, trace_output = self.run_comprehensive_trace()
            self.analyze_trace_results(results, trace_output)
        
        if choice in ['2', '3']:
            # 自定义跟踪
            self.run_custom_trace()
        
        print("\n" + "=" * 60)
        print("🎉 frida-trace分析完成!")
        print(f"📁 分析结果目录: {self.output_dir}")
        print("📋 主要文件:")
        print("   - frida_trace_analysis.json (详细结果)")
        print("   - frida_trace_summary.md (总结报告)")
        print("   - *.js (跟踪脚本)")
        print("   - *.output (跟踪输出)")
        print("=" * 60)
        
        return True

def main():
    print("🔍 frida-trace系统调用跟踪分析工具")
    print("💡 此工具将使用frida-trace跟踪翼支付应用")
    print("⚠️ 请确保:")
    print("   - 翼支付应用正在运行")
    print("   - Frida环境正常")
    print("   - 设备已连接")
    
    confirm = input("\n是否开始frida-trace分析? (y/n): ").strip().lower()
    if confirm != 'y':
        print("👋 分析已取消")
        return
    
    analyzer = FridaTraceAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        print("\n💡 下一步建议:")
        print("   1. 查看跟踪结果中的加密函数调用")
        print("   2. 分析网络请求的详细内容")
        print("   3. 结合strace结果进行对比分析")
        print("   4. 使用Charles代理验证网络数据")

if __name__ == "__main__":
    main()
