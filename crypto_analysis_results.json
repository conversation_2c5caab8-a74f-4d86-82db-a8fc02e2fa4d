{"timestamp": "2025-06-05T18:50:30.194720", "mpaas_config": {"public_key_pem": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4R\nAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==\n-----END PUBLIC KEY-----", "algorithm": "SM2+AES", "encryption_type": "C005", "api_endpoint": "/gapi/equitymall/client/Activity/queryActivityInfo", "domain": "mapi-h5.bestpay.com.cn"}, "analysis_summary": {}, "single_request_analysis": {"timestamp": "2025-06-05T18:50:30.194720", "fields_found": ["data", "key", "sign", "encyType"], "field_analysis": {"data": {"length": 44, "type": "str", "is_base64": true, "is_hex": false}, "key": {"length": 53, "type": "str", "is_base64": false, "is_hex": false}, "sign": {"length": 33, "type": "str", "is_base64": false, "is_hex": false}, "encyType": {"length": 4, "type": "str", "is_base64": false, "is_hex": false}}, "encryption_indicators": ["C005加密类型确认", "翼支付应用渠道确认"]}, "decryption_attempt": {"data_analysis": null, "key_analysis": null, "sign_verification": {"length": 33, "is_hex": false, "possible_algorithm": "Unknown"}, "decryption_attempts": []}, "request_template": {"url": "https://mapi-h5.bestpay.com.cn/gapi/equitymall/client/Activity/queryActivityInfo", "method": "POST", "headers": {"Content-Type": "application/json", "User-Agent": "BestPay/11.0 (Android)", "Accept": "application/json"}, "body": {"data": "<BASE64_ENCODED_ENCRYPTED_DATA>", "key": "<BASE64_ENCODED_ENCRYPTED_AES_KEY>", "sign": "<MD5_OR_SM3_SIGNATURE>", "encyType": "C005", "fromChannelId": "yzf_app", "productNo": "<PRODUCT_NUMBER>", "timestamp": 1749120630195}}}