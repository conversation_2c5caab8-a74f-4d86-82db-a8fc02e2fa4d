#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动Frida设置和调试脚本
逐步手动设置Frida环境
"""

import subprocess
import time
import os

def run_command(cmd, capture_output=True):
    """执行命令"""
    try:
        if capture_output:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            return result.returncode == 0, result.stdout, result.stderr
        else:
            result = subprocess.run(cmd, shell=True, timeout=30)
            return result.returncode == 0, "", ""
    except Exception as e:
        return False, "", str(e)

def manual_setup():
    """手动设置"""
    adb_cmd = r".\android-tools\platform-tools\adb.exe"
    
    print("🔧 手动Frida设置和调试")
    print("=" * 50)
    
    # 步骤1: 彻底清理
    print("\n🧹 第1步: 彻底清理环境...")
    
    print("   停止所有frida进程...")
    run_command(f"{adb_cmd} shell su -c 'pkill -9 frida-server'")
    
    print("   重启ADB...")
    run_command(f"{adb_cmd} kill-server")
    time.sleep(2)
    run_command(f"{adb_cmd} start-server")
    time.sleep(2)
    
    # 步骤2: 检查设备
    print("\n📱 第2步: 检查设备连接...")
    success, stdout, stderr = run_command(f"{adb_cmd} devices")
    if "device" in stdout:
        print("   ✅ 设备连接正常")
    else:
        print("   ❌ 设备连接失败")
        return False
    
    # 步骤3: 设置端口转发
    print("\n🔧 第3步: 设置端口转发...")
    success, stdout, stderr = run_command(f"{adb_cmd} forward tcp:27042 tcp:27042")
    if success:
        print("   ✅ 端口转发设置成功")
    else:
        print(f"   ❌ 端口转发失败: {stderr}")
    
    # 步骤4: 手动启动Frida Server
    print("\n🚀 第4步: 手动启动Frida Server...")
    print("   正在启动Frida Server...")
    
    # 使用不同的方法启动
    cmd = f"{adb_cmd} shell su -c 'cd /data/local/tmp && ./frida-server'"
    
    print(f"   执行命令: {cmd}")
    
    # 启动Frida Server (后台运行)
    try:
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print(f"   Frida Server进程ID: {process.pid}")
        
        # 等待几秒让服务启动
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("   ✅ Frida Server正在运行")
        else:
            stdout, stderr = process.communicate()
            print(f"   ❌ Frida Server启动失败: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"   ❌ 启动失败: {e}")
        return False
    
    # 步骤5: 测试连接
    print("\n🔍 第5步: 测试Frida连接...")
    
    for i in range(5):
        print(f"   测试 {i+1}/5...")
        success, stdout, stderr = run_command("frida-ps -U", timeout=10)
        
        if success and stdout.strip():
            lines = stdout.strip().split('\n')
            process_count = len(lines) - 1
            print(f"   ✅ Frida连接成功！检测到 {process_count} 个进程")
            
            # 显示前几个进程
            print("   📱 设备进程列表 (前10个):")
            for line in lines[1:11]:  # 跳过标题，显示前10个
                print(f"      {line}")
            
            return True
        else:
            print(f"   ❌ 连接失败: {stderr}")
            if i < 4:
                print("   ⏳ 等待3秒后重试...")
                time.sleep(3)
    
    print("   ❌ 所有连接尝试都失败了")
    return False

def start_hook():
    """开始Hook"""
    print("\n🎯 第6步: 开始Hook调试...")
    
    print("选择调试模式:")
    print("   1. 启动应用并Hook")
    print("   2. 附加到运行中的应用")
    
    choice = input("\n请选择 (1-2): ").strip()
    
    if choice == '1':
        cmd = "frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause"
    else:
        cmd = "frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js"
        print("\n💡 请先在手机上打开翼支付应用")
        input("按回车继续...")
    
    print(f"\n🚀 执行Hook命令: {cmd}")
    print("\n" + "="*60)
    print("📱 操作指南:")
    print("   1. 在手机上操作翼支付应用")
    print("   2. 进入权益商城并刷新页面")
    print("   3. 观察输出中的加密数据")
    print("   4. 复制保存AES密钥和解密结果")
    print("="*60)
    
    try:
        subprocess.run(cmd, shell=True)
    except KeyboardInterrupt:
        print("\n⏹️ Hook调试已停止")
    
    print("\n🎉 调试完成!")

def main():
    if manual_setup():
        print("\n✅ Frida环境设置成功!")
        
        start_debug = input("\n是否立即开始Hook调试? (y/n): ").strip().lower()
        if start_debug == 'y':
            start_hook()
    else:
        print("\n❌ Frida环境设置失败")
        print("\n💡 可能的解决方案:")
        print("   1. 确保设备已Root")
        print("   2. 检查frida-server文件权限")
        print("   3. 尝试重启手机")
        print("   4. 使用不同版本的frida-server")

if __name__ == "__main__":
    main()
