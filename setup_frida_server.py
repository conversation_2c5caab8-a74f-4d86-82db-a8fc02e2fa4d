#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Frida Server自动设置脚本
为Root设备下载并配置Frida Server
"""

import urllib.request
import os
import subprocess

def download_frida_server():
    """下载Frida Server"""
    print("📦 下载Frida Server for arm64-v8a...")
    
    # Frida Server下载URL
    frida_version = "17.0.7"
    url = f"https://github.com/frida/frida/releases/download/{frida_version}/frida-server-{frida_version}-android-arm64.xz"
    
    filename = f"frida-server-{frida_version}-android-arm64.xz"
    
    try:
        print(f"正在下载: {url}")
        urllib.request.urlretrieve(url, filename)
        print(f"✅ 下载完成: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None

def extract_frida_server(filename):
    """解压Frida Server"""
    print("📂 解压Frida Server...")
    
    try:
        # 使用Python的lzma模块解压xz文件
        import lzma
        
        output_name = "frida-server"
        
        with lzma.open(filename, 'rb') as f_in:
            with open(output_name, 'wb') as f_out:
                f_out.write(f_in.read())
        
        print(f"✅ 解压完成: {output_name}")
        return output_name
    except Exception as e:
        print(f"❌ 解压失败: {e}")
        print("💡 请手动解压xz文件")
        return None

def setup_frida_server():
    """设置Frida Server"""
    adb_cmd = r".\android-tools\platform-tools\adb.exe"
    
    print("🔧 设置Frida Server...")
    
    # 检查设备连接
    result = subprocess.run(f"{adb_cmd} devices", shell=True, capture_output=True, text=True)
    if "device" not in result.stdout:
        print("❌ 设备未连接")
        return False
    
    # 下载Frida Server
    filename = download_frida_server()
    if not filename:
        return False
    
    # 解压Frida Server
    server_file = extract_frida_server(filename)
    if not server_file:
        return False
    
    # 推送到设备
    print("📱 推送Frida Server到设备...")
    result = subprocess.run(f"{adb_cmd} push {server_file} /data/local/tmp/", shell=True)
    if result.returncode != 0:
        print("❌ 推送失败")
        return False
    
    # 设置权限
    print("🔐 设置执行权限...")
    result = subprocess.run(f"{adb_cmd} shell chmod 755 /data/local/tmp/{server_file}", shell=True)
    if result.returncode != 0:
        print("❌ 设置权限失败")
        return False
    
    print("✅ Frida Server设置完成!")
    print("\n🚀 启动Frida Server:")
    print(f"   {adb_cmd} shell su -c '/data/local/tmp/{server_file} &'")
    
    return True

def main():
    print("🔓 Root设备Frida Server自动设置")
    print("=" * 40)
    
    if setup_frida_server():
        print("\n🎉 设置完成!")
        print("💡 下一步:")
        print("   1. 启动Frida Server")
        print("   2. 安装翼支付应用")
        print("   3. 开始Hook调试")
    else:
        print("\n❌ 设置失败")

if __name__ == "__main__":
    main()
