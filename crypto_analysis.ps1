# 加密和安全分析专用脚本
# 深度分析SO文件和加密相关内容

Write-Host "🔐 开始深度加密安全分析..." -ForegroundColor Red
Write-Host "============================================================"

# 1. SO文件详细分析
Write-Host "`n📚 Native库(SO文件)详细分析:" -ForegroundColor Yellow

if (Test-Path "lib\arm64-v8a") {
    $allLibs = Get-ChildItem "lib\arm64-v8a" -Filter "*.so"
    Write-Host "   总SO文件数量: $($allLibs.Count)"
    
    # 按功能分类分析SO文件
    Write-Host "`n🔒 加密安全相关SO文件:"
    $cryptoLibs = $allLibs | Where-Object { 
        $_.Name -match "(crypto|ssl|security|encrypt|cipher|hash|rsa|aes|des|md5|sha|tls|cert|key|sign)" 
    }
    
    foreach ($lib in $cryptoLibs) {
        $size = [math]::Round($lib.Length / 1KB, 2)
        Write-Host "   • $($lib.Name) ($size KB)"
        
        # 分析文件名中的加密算法线索
        if ($lib.Name -match "ssl|tls") {
            Write-Host "     └─ SSL/TLS加密通信"
        }
        if ($lib.Name -match "crypto") {
            Write-Host "     └─ 通用加密算法库"
        }
        if ($lib.Name -match "cipher|encrypt") {
            Write-Host "     └─ 数据加密/解密"
        }
        if ($lib.Name -match "security") {
            Write-Host "     └─ 安全防护模块"
        }
    }
    
    Write-Host "`n🛡️ 安全防护相关SO文件:"
    $securityLibs = $allLibs | Where-Object { 
        $_.Name -match "(shield|guard|protect|detect|anti|safe|secure|armor|defense)" 
    }
    
    foreach ($lib in $securityLibs) {
        $size = [math]::Round($lib.Length / 1KB, 2)
        Write-Host "   • $($lib.Name) ($size KB)"
        
        if ($lib.Name -match "shield|armor") {
            Write-Host "     └─ 代码保护/混淆"
        }
        if ($lib.Name -match "guard|protect") {
            Write-Host "     └─ 运行时保护"
        }
        if ($lib.Name -match "detect|anti") {
            Write-Host "     └─ 反调试/反逆向"
        }
    }
    
    Write-Host "`n🔑 身份认证相关SO文件:"
    $authLibs = $allLibs | Where-Object { 
        $_.Name -match "(auth|login|verify|biometric|finger|face|id|card|ocr)" 
    }
    
    foreach ($lib in $authLibs) {
        $size = [math]::Round($lib.Length / 1KB, 2)
        Write-Host "   • $($lib.Name) ($size KB)"
        
        if ($lib.Name -match "face|liveness") {
            Write-Host "     └─ 人脸识别/活体检测"
        }
        if ($lib.Name -match "finger|biometric") {
            Write-Host "     └─ 指纹/生物识别"
        }
        if ($lib.Name -match "ocr|card") {
            Write-Host "     └─ OCR识别/证件识别"
        }
    }
    
    Write-Host "`n💾 数据库加密相关SO文件:"
    $dbLibs = $allLibs | Where-Object { 
        $_.Name -match "(database|db|sql|sqlite|cipher)" 
    }
    
    foreach ($lib in $dbLibs) {
        $size = [math]::Round($lib.Length / 1KB, 2)
        Write-Host "   • $($lib.Name) ($size KB)"
        Write-Host "     └─ 数据库加密存储"
    }
    
    Write-Host "`n🌐 网络安全相关SO文件:"
    $netLibs = $allLibs | Where-Object { 
        $_.Name -match "(net|http|curl|openssl|qcssl|tls)" 
    }
    
    foreach ($lib in $netLibs) {
        $size = [math]::Round($lib.Length / 1KB, 2)
        Write-Host "   • $($lib.Name) ($size KB)"
        Write-Host "     └─ 网络通信加密"
    }
}

# 2. 加密配置文件分析
Write-Host "`n📄 加密配置文件分析:" -ForegroundColor Yellow

# 分析网络安全配置
if (Test-Path "res\xml\network_security_config.xml") {
    Write-Host "   • 网络安全配置文件存在"
    $netConfig = Get-Content "res\xml\network_security_config.xml" -Raw
    if ($netConfig -match "cleartextTrafficPermitted") {
        Write-Host "     └─ 明文流量配置"
    }
    if ($netConfig -match "trust-anchors") {
        Write-Host "     └─ 证书信任锚点配置"
    }
}

# 分析mPaaS网络配置
if (Test-Path "assets\mpaas_netconfig.properties") {
    Write-Host "   • mPaaS网络加密配置"
    $mpaasConfig = Get-Content "assets\mpaas_netconfig.properties" -Raw
    if ($mpaasConfig -match "Crypt=true") {
        Write-Host "     └─ 启用加密传输"
    }
    if ($mpaasConfig -match "RSA|ECC|SM2") {
        Write-Host "     └─ 支持RSA/ECC/SM2算法"
    }
    if ($mpaasConfig -match "PubKey") {
        Write-Host "     └─ 包含公钥配置"
    }
}

# 3. 密钥和证书分析
Write-Host "`n🔑 密钥和证书分析:" -ForegroundColor Yellow

# 查找证书文件
$certFiles = Get-ChildItem -Recurse -Include "*.bks", "*.cer", "*.crt", "*.pem", "*.p12", "*.jks" -ErrorAction SilentlyContinue
if ($certFiles.Count -gt 0) {
    Write-Host "   证书文件:"
    foreach ($cert in $certFiles) {
        Write-Host "   • $($cert.FullName.Replace($PWD, '.'))"
    }
}

# 查找密钥文件
$keyFiles = Get-ChildItem -Recurse -Include "*.key", "*.pub" -ErrorAction SilentlyContinue
if ($keyFiles.Count -gt 0) {
    Write-Host "   密钥文件:"
    foreach ($key in $keyFiles) {
        Write-Host "   • $($key.FullName.Replace($PWD, '.'))"
    }
}

# 从AndroidManifest.xml中提取加密相关配置
if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    # 查找加密相关的meta-data
    $encryptionKeys = [regex]::Matches($manifest, '<meta-data[^>]*android:name="[^"]*(?:key|encrypt|crypto|security)[^"]*"[^>]*android:value="([^"]*)"') | ForEach-Object { 
        @{
            Name = ([regex]::Match($_.Value, 'android:name="([^"]*)"')).Groups[1].Value
            Value = $_.Groups[1].Value
        }
    }
    
    if ($encryptionKeys.Count -gt 0) {
        Write-Host "   AndroidManifest中的加密配置:"
        foreach ($key in $encryptionKeys) {
            Write-Host "   • $($key.Name)"
            if ($key.Value.Length -gt 50) {
                Write-Host "     └─ 值: $($key.Value.Substring(0, 50))..."
            } else {
                Write-Host "     └─ 值: $($key.Value)"
            }
        }
    }
}

# 4. 安全检测和反调试分析
Write-Host "`n🛡️ 安全检测和反调试分析:" -ForegroundColor Yellow

# 检查安全检测服务
if (Test-Path "META-INF\services") {
    $securityServices = Get-ChildItem "META-INF\services" | Where-Object { $_.Name -match "security|detect|protect" }
    if ($securityServices.Count -gt 0) {
        Write-Host "   安全检测服务:"
        foreach ($service in $securityServices) {
            Write-Host "   • $($service.Name)"
            $content = Get-Content $service.FullName -ErrorAction SilentlyContinue
            if ($content) {
                Write-Host "     └─ 实现类: $content"
            }
        }
    }
}

# 检查混淆和保护
$obfuscatedFiles = Get-ChildItem -Recurse -Include "*.dat", "*.bin" -ErrorAction SilentlyContinue | Where-Object { 
    $_.Name -match "(encrypt|cipher|protect|shield|guard)" 
}

if ($obfuscatedFiles.Count -gt 0) {
    Write-Host "   可能的混淆/保护文件:"
    foreach ($file in $obfuscatedFiles) {
        Write-Host "   • $($file.FullName.Replace($PWD, '.'))"
    }
}

# 5. 第三方安全SDK分析
Write-Host "`n🔌 第三方安全SDK分析:" -ForegroundColor Yellow

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    $securitySDKs = @()
    
    # 检测安全相关的第三方SDK
    if ($manifest -match "tencent.*security|tencent.*sm") { $securitySDKs += "腾讯安全SDK" }
    if ($manifest -match "alipay.*security|antssm") { $securitySDKs += "蚂蚁安全SDK" }
    if ($manifest -match "huawei.*security|hms.*security") { $securitySDKs += "华为安全SDK" }
    if ($manifest -match "turing|turingmfa") { $securitySDKs += "图灵安全SDK" }
    if ($manifest -match "sensetime|shangtang") { $securitySDKs += "商汤科技SDK" }
    if ($manifest -match "megvii|face\+\+") { $securitySDKs += "旷视科技SDK" }
    if ($manifest -match "ijm|ijiami") { $securitySDKs += "爱加密SDK" }
    
    foreach ($sdk in $securitySDKs) {
        Write-Host "   • $sdk"
    }
}

# 6. 加密算法推测
Write-Host "`n🧮 可能使用的加密算法:" -ForegroundColor Yellow

$algorithms = @()

# 从SO文件名推测
if (Test-Path "lib\arm64-v8a") {
    $allLibs = Get-ChildItem "lib\arm64-v8a" -Filter "*.so"
    
    if ($allLibs | Where-Object { $_.Name -match "openssl|ssl" }) {
        $algorithms += "OpenSSL (RSA, AES, DES, SHA, MD5等)"
    }
    if ($allLibs | Where-Object { $_.Name -match "sm|tencentSM" }) {
        $algorithms += "国密算法 (SM2, SM3, SM4)"
    }
    if ($allLibs | Where-Object { $_.Name -match "crypto" }) {
        $algorithms += "通用加密算法库"
    }
}

# 从配置文件推测
if (Test-Path "assets\mpaas_netconfig.properties") {
    $config = Get-Content "assets\mpaas_netconfig.properties" -Raw
    if ($config -match "RSA") { $algorithms += "RSA非对称加密" }
    if ($config -match "ECC") { $algorithms += "椭圆曲线加密(ECC)" }
    if ($config -match "SM2") { $algorithms += "国密SM2算法" }
}

foreach ($algo in $algorithms) {
    Write-Host "   • $algo"
}

Write-Host "`n============================================================"
Write-Host "🔐 加密安全分析完成!" -ForegroundColor Red
Write-Host "============================================================"
