# 翼支付调试环境搭建指南

## 🛠️ 环境准备

### 1. 安装必要工具
```bash
# 安装Frida
pip install frida-tools

# 验证安装
frida --version

# 安装ADB (如果没有)
# 下载Android SDK Platform Tools
```

### 2. 设备准备
```bash
# 连接Android设备并启用USB调试
adb devices

# 确认设备连接
adb shell getprop ro.product.model

# 安装Frida Server (如果是Root设备)
adb push frida-server-16.0.19-android-arm64 /data/local/tmp/
adb shell chmod 755 /data/local/tmp/frida-server-16.0.19-android-arm64
adb shell /data/local/tmp/frida-server-16.0.19-android-arm64 &
```

### 3. 应用准备
```bash
# 确认翼支付应用已安装
adb shell pm list packages | grep bestpay

# 获取应用信息
adb shell dumpsys package com.chinatelecom.bestpayclient | grep version
```

## 🔍 调试步骤

### 步骤1: 启动Hook监控
```bash
# 方法1: 附加到运行中的应用
frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js

# 方法2: 启动应用并Hook
frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause
```

### 步骤2: 触发目标请求
在翼支付应用中执行以下操作：
1. 打开应用并登录
2. 进入"权益商城"或相关页面
3. 触发网络请求（刷新页面、查看活动等）
4. 观察Frida控制台输出

### 步骤3: 收集关键数据
Hook脚本会输出：
```
🔑 [AES_decrypt] 调用
   密钥: [32字节十六进制]
   IV: [16字节十六进制]
   加密数据: [数据十六进制]
   解密结果: [原始JSON数据]
```

### 步骤4: 验证解密
使用捕获的数据验证解密过程：
```python
# 使用我们提供的解密脚本
python verify_decryption.py --key [捕获的密钥] --data [原始data字段]
```

## 📱 实际操作流程

### 准备阶段 (5分钟)
1. 确保手机连接并开启USB调试
2. 启动翼支付应用
3. 准备Frida Hook脚本

### 执行阶段 (10分钟)
1. 运行Hook脚本
2. 在应用中触发网络请求
3. 观察并记录Hook输出
4. 收集至少3-5个请求样本

### 验证阶段 (5分钟)
1. 使用捕获的密钥解密data字段
2. 验证解密结果是否为有效JSON
3. 分析原始请求内容

## ⚠️ 可能遇到的问题

### 问题1: Frida连接失败
```bash
# 解决方案
adb forward tcp:27042 tcp:27042
frida-ps -U  # 测试连接
```

### 问题2: Hook函数未找到
```bash
# 检查SO库是否存在
adb shell ls /data/app/com.chinatelecom.bestpayclient*/lib/arm64/
```

### 问题3: 应用检测到调试
```bash
# 尝试绕过检测
frida -U -f com.chinatelecom.bestpayclient -l bypass_detection.js
```

## 🎯 成功标志

当您看到以下输出时，说明Hook成功：
```
✅ 找到函数: libmpaas_crypto.so!AES_decrypt
🔑 [AES_decrypt] 调用
   密钥: 1234567890abcdef...
   解密结果: {"activityId":"12345",...}
```

## 📋 数据收集清单

请收集以下信息：
- [ ] AES密钥 (32字节)
- [ ] IV向量 (16字节)
- [ ] 原始data字段 (Base64)
- [ ] 解密后的JSON内容
- [ ] 对应的key字段和sign字段
- [ ] 请求的完整URL和参数

## 🚀 下一步预告

成功Hook后，我们将：
1. 分析密钥生成规律
2. 构建自动化解密工具
3. 扩展到其他API接口
4. 建立完整的解密框架
