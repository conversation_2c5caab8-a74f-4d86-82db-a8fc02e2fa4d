7767517
126 149
Input                    images                   0 1 images
Split                    splitncnn_input0         1 4 images images_splitncnn_0 images_splitncnn_1 images_splitncnn_2 images_splitncnn_3
Concat                   Concat_0                 4 1 images_splitncnn_3 images_splitncnn_2 images_splitncnn_1 images_splitncnn_0 151
Convolution              Conv_1                   1 1 151 153 0=16 1=3 4=1 5=1 6=1728 9=2 -23310=1,1.000000e-01
Convolution              Conv_3                   1 1 153 155 0=32 1=3 3=2 4=1 5=1 6=4608 9=2 -23310=1,1.000000e-01
Split                    splitncnn_0              1 2 155 155_splitncnn_0 155_splitncnn_1
Convolution              Conv_5                   1 1 155_splitncnn_1 157 0=16 1=1 5=1 6=512 9=2 -23310=1,1.000000e-01
Split                    splitncnn_1              1 2 157 157_splitncnn_0 157_splitncnn_1
Convolution              Conv_7                   1 1 157_splitncnn_1 159 0=16 1=1 5=1 6=256 9=2 -23310=1,1.000000e-01
Convolution              Conv_9                   1 1 159 161 0=16 1=3 4=1 5=1 6=2304 9=2 -23310=1,1.000000e-01
BinaryOp                 Add_11                   2 1 157_splitncnn_0 161 162
Convolution              Conv_12                  1 1 162 163 0=16 1=1 6=256
Convolution              Conv_13                  1 1 155_splitncnn_0 164 0=16 1=1 6=512
Concat                   Concat_14                2 1 163 164 165
BatchNorm                BatchNormalization_15    1 1 165 166 0=32
ReLU                     LeakyRelu_16             1 1 166 167 0=1.000000e-01
Convolution              Conv_17                  1 1 167 169 0=32 1=1 5=1 6=1024 9=2 -23310=1,1.000000e-01
Convolution              Conv_19                  1 1 169 171 0=56 1=3 3=2 4=1 5=1 6=16128 9=2 -23310=1,1.000000e-01
Split                    splitncnn_2              1 2 171 171_splitncnn_0 171_splitncnn_1
Convolution              Conv_21                  1 1 171_splitncnn_1 173 0=28 1=1 5=1 6=1568 9=2 -23310=1,1.000000e-01
Split                    splitncnn_3              1 2 173 173_splitncnn_0 173_splitncnn_1
Convolution              Conv_23                  1 1 173_splitncnn_1 175 0=28 1=1 5=1 6=784 9=2 -23310=1,1.000000e-01
Convolution              Conv_25                  1 1 175 177 0=28 1=3 4=1 5=1 6=7056 9=2 -23310=1,1.000000e-01
BinaryOp                 Add_27                   2 1 173_splitncnn_0 177 178
Convolution              Conv_28                  1 1 178 179 0=28 1=1 6=784
Convolution              Conv_29                  1 1 171_splitncnn_0 180 0=28 1=1 6=1568
Concat                   Concat_30                2 1 179 180 181
BatchNorm                BatchNormalization_31    1 1 181 182 0=56
ReLU                     LeakyRelu_32             1 1 182 183 0=1.000000e-01
Convolution              Conv_33                  1 1 183 185 0=56 1=1 5=1 6=3136 9=2 -23310=1,1.000000e-01
Split                    splitncnn_4              1 2 185 185_splitncnn_0 185_splitncnn_1
Convolution              Conv_35                  1 1 185_splitncnn_1 187 0=104 1=3 3=2 4=1 5=1 6=52416 9=2 -23310=1,1.000000e-01
Split                    splitncnn_5              1 2 187 187_splitncnn_0 187_splitncnn_1
Convolution              Conv_37                  1 1 187_splitncnn_1 189 0=52 1=1 5=1 6=5408 9=2 -23310=1,1.000000e-01
Split                    splitncnn_6              1 2 189 189_splitncnn_0 189_splitncnn_1
Convolution              Conv_39                  1 1 189_splitncnn_1 191 0=52 1=1 5=1 6=2704 9=2 -23310=1,1.000000e-01
Convolution              Conv_41                  1 1 191 193 0=52 1=3 4=1 5=1 6=24336 9=2 -23310=1,1.000000e-01
BinaryOp                 Add_43                   2 1 189_splitncnn_0 193 194
Convolution              Conv_44                  1 1 194 195 0=52 1=1 6=2704
Convolution              Conv_45                  1 1 187_splitncnn_0 196 0=52 1=1 6=5408
Concat                   Concat_46                2 1 195 196 197
BatchNorm                BatchNormalization_47    1 1 197 198 0=104
ReLU                     LeakyRelu_48             1 1 198 199 0=1.000000e-01
Convolution              Conv_49                  1 1 199 201 0=104 1=1 5=1 6=10816 9=2 -23310=1,1.000000e-01
Split                    splitncnn_7              1 2 201 201_splitncnn_0 201_splitncnn_1
Convolution              Conv_51                  1 1 201_splitncnn_1 203 0=208 1=3 3=2 4=1 5=1 6=194688 9=2 -23310=1,1.000000e-01
Convolution              Conv_53                  1 1 203 205 0=104 1=1 5=1 6=21632 9=2 -23310=1,1.000000e-01
Split                    splitncnn_8              1 4 205 205_splitncnn_0 205_splitncnn_1 205_splitncnn_2 205_splitncnn_3
Pooling                  MaxPool_55               1 1 205_splitncnn_3 206 1=5 3=2 5=1
Pooling                  MaxPool_56               1 1 205_splitncnn_2 207 1=9 3=4 5=1
Pooling                  MaxPool_57               1 1 205_splitncnn_1 208 1=13 3=6 5=1
Concat                   Concat_58                4 1 205_splitncnn_0 206 207 208 209
Convolution              Conv_59                  1 1 209 211 0=208 1=1 5=1 6=86528 9=2 -23310=1,1.000000e-01
Split                    splitncnn_9              1 2 211 211_splitncnn_0 211_splitncnn_1
Convolution              Conv_61                  1 1 211_splitncnn_1 213 0=104 1=1 5=1 6=21632 9=2 -23310=1,1.000000e-01
Convolution              Conv_63                  1 1 213 215 0=104 1=1 5=1 6=10816 9=2 -23310=1,1.000000e-01
Convolution              Conv_65                  1 1 215 217 0=104 1=3 4=1 5=1 6=97344 9=2 -23310=1,1.000000e-01
Convolution              Conv_67                  1 1 217 218 0=104 1=1 6=10816
Convolution              Conv_68                  1 1 211_splitncnn_0 219 0=104 1=1 6=21632
Concat                   Concat_69                2 1 218 219 220
BatchNorm                BatchNormalization_70    1 1 220 221 0=208
ReLU                     LeakyRelu_71             1 1 221 222 0=1.000000e-01
Convolution              Conv_72                  1 1 222 224 0=208 1=1 5=1 6=43264 9=2 -23310=1,1.000000e-01
Convolution              Conv_74                  1 1 224 226 0=104 1=1 5=1 6=21632 9=2 -23310=1,1.000000e-01
Split                    splitncnn_10             1 2 226 226_splitncnn_0 226_splitncnn_1
Interp                   Resize_104               1 1 226_splitncnn_1 255 0=1 3=40 4=40
Concat                   Concat_105               2 1 255 201_splitncnn_0 256
Split                    splitncnn_11             1 2 256 256_splitncnn_0 256_splitncnn_1
Convolution              Conv_106                 1 1 256_splitncnn_1 258 0=52 1=1 5=1 6=10816 9=2 -23310=1,1.000000e-01
Convolution              Conv_108                 1 1 258 260 0=52 1=1 5=1 6=2704 9=2 -23310=1,1.000000e-01
Convolution              Conv_110                 1 1 260 262 0=52 1=3 4=1 5=1 6=24336 9=2 -23310=1,1.000000e-01
Convolution              Conv_112                 1 1 262 263 0=52 1=1 6=2704
Convolution              Conv_113                 1 1 256_splitncnn_0 264 0=52 1=1 6=10816
Concat                   Concat_114               2 1 263 264 265
BatchNorm                BatchNormalization_115   1 1 265 266 0=104
ReLU                     LeakyRelu_116            1 1 266 267 0=1.000000e-01
Convolution              Conv_117                 1 1 267 269 0=104 1=1 5=1 6=10816 9=2 -23310=1,1.000000e-01
Convolution              Conv_119                 1 1 269 271 0=56 1=1 5=1 6=5824 9=2 -23310=1,1.000000e-01
Split                    splitncnn_12             1 2 271 271_splitncnn_0 271_splitncnn_1
Interp                   Resize_149               1 1 271_splitncnn_1 300 0=1 3=80 4=80
Concat                   Concat_150               2 1 300 185_splitncnn_0 301
Split                    splitncnn_13             1 2 301 301_splitncnn_0 301_splitncnn_1
Convolution              Conv_151                 1 1 301_splitncnn_1 303 0=28 1=1 5=1 6=3136 9=2 -23310=1,1.000000e-01
Convolution              Conv_153                 1 1 303 305 0=28 1=1 5=1 6=784 9=2 -23310=1,1.000000e-01
Convolution              Conv_155                 1 1 305 307 0=28 1=3 4=1 5=1 6=7056 9=2 -23310=1,1.000000e-01
Convolution              Conv_157                 1 1 307 308 0=28 1=1 6=784
Convolution              Conv_158                 1 1 301_splitncnn_0 309 0=28 1=1 6=3136
Concat                   Concat_159               2 1 308 309 310
BatchNorm                BatchNormalization_160   1 1 310 311 0=56
ReLU                     LeakyRelu_161            1 1 311 312 0=1.000000e-01
Convolution              Conv_162                 1 1 312 314 0=56 1=1 5=1 6=3136 9=2 -23310=1,1.000000e-01
Split                    splitncnn_14             1 2 314 314_splitncnn_0 314_splitncnn_1
Convolution              Conv_164                 1 1 314_splitncnn_1 315 0=21 1=1 5=1 6=1176
Convolution              Conv_165                 1 1 314_splitncnn_0 317 0=56 1=3 3=2 4=1 5=1 6=28224 9=2 -23310=1,1.000000e-01
Concat                   Concat_167               2 1 317 271_splitncnn_0 318
Split                    splitncnn_15             1 2 318 318_splitncnn_0 318_splitncnn_1
Convolution              Conv_168                 1 1 318_splitncnn_1 320 0=52 1=1 5=1 6=5824 9=2 -23310=1,1.000000e-01
Convolution              Conv_170                 1 1 320 322 0=52 1=1 5=1 6=2704 9=2 -23310=1,1.000000e-01
Convolution              Conv_172                 1 1 322 324 0=52 1=3 4=1 5=1 6=24336 9=2 -23310=1,1.000000e-01
Convolution              Conv_174                 1 1 324 325 0=52 1=1 6=2704
Convolution              Conv_175                 1 1 318_splitncnn_0 326 0=52 1=1 6=5824
Concat                   Concat_176               2 1 325 326 327
BatchNorm                BatchNormalization_177   1 1 327 328 0=104
ReLU                     LeakyRelu_178            1 1 328 329 0=1.000000e-01
Convolution              Conv_179                 1 1 329 331 0=104 1=1 5=1 6=10816 9=2 -23310=1,1.000000e-01
Split                    splitncnn_16             1 2 331 331_splitncnn_0 331_splitncnn_1
Convolution              Conv_181                 1 1 331_splitncnn_1 332 0=21 1=1 5=1 6=2184
Convolution              Conv_182                 1 1 331_splitncnn_0 334 0=104 1=3 3=2 4=1 5=1 6=97344 9=2 -23310=1,1.000000e-01
Concat                   Concat_184               2 1 334 226_splitncnn_0 335
Split                    splitncnn_17             1 2 335 335_splitncnn_0 335_splitncnn_1
Convolution              Conv_185                 1 1 335_splitncnn_1 337 0=104 1=1 5=1 6=21632 9=2 -23310=1,1.000000e-01
Convolution              Conv_187                 1 1 337 339 0=104 1=1 5=1 6=10816 9=2 -23310=1,1.000000e-01
Convolution              Conv_189                 1 1 339 341 0=104 1=3 4=1 5=1 6=97344 9=2 -23310=1,1.000000e-01
Convolution              Conv_191                 1 1 341 342 0=104 1=1 6=10816
Convolution              Conv_192                 1 1 335_splitncnn_0 343 0=104 1=1 6=21632
Concat                   Concat_193               2 1 342 343 344
BatchNorm                BatchNormalization_194   1 1 344 345 0=208
ReLU                     LeakyRelu_195            1 1 345 346 0=1.000000e-01
Convolution              Conv_196                 1 1 346 348 0=208 1=1 5=1 6=43264 9=2 -23310=1,1.000000e-01
Convolution              Conv_198                 1 1 348 349 0=21 1=1 5=1 6=4368
Reshape                  Reshape_212              1 1 349 367 0=400 1=7 2=3
Permute                  Transpose_213            1 1 367 output 0=1
Reshape                  Reshape_227              1 1 332 386 0=1600 1=7 2=3
Permute                  Transpose_228            1 1 386 387 0=1
Reshape                  Reshape_242              1 1 315 405 0=6400 1=7 2=3
Permute                  Transpose_243            1 1 405 406 0=1
