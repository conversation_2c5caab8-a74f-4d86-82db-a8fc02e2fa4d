// 翼支付增强调试Hook脚本 - 包含详细日志分析
console.log("🔐 启动翼支付增强调试Hook...");
console.log("📅 时间: " + new Date().toLocaleString());

// 全局错误处理
Process.setExceptionHandler(function(details) {
    console.log("💥 [全局异常] " + details.type + ": " + details.message);
    console.log("📍 位置: " + details.fileName + ":" + details.lineNumber);
    console.log("📚 堆栈: " + details.stack);
});

// 调试统计
var debugStats = {
    javaAttachAttempts: 0,
    javaAttachSuccess: false,
    hookAttempts: 0,
    hookSuccesses: 0,
    hookFailures: 0,
    classesFound: [],
    classesNotFound: [],
    methodsHooked: [],
    errors: []
};

function logError(category, error, details) {
    var errorInfo = {
        timestamp: new Date().toISOString(),
        category: category,
        error: error.toString(),
        details: details || {}
    };
    debugStats.errors.push(errorInfo);
    console.log("❌ [" + category + "] " + error);
    if (details) {
        console.log("   详情: " + JSON.stringify(details));
    }
}

function logSuccess(category, message, details) {
    console.log("✅ [" + category + "] " + message);
    if (details) {
        console.log("   详情: " + JSON.stringify(details));
    }
}

// 检查Java环境
console.log("\n🔍 [环境检查] 开始检查Java环境...");

try {
    // 尝试获取Java虚拟机信息
    var vm = Java.vm;
    console.log("✅ [环境检查] Java VM已加载");
    console.log("   VM类型: " + (vm ? "可用" : "不可用"));
    
    // 检查是否可以执行Java代码
    Java.perform(function() {
        debugStats.javaAttachAttempts++;
        debugStats.javaAttachSuccess = true;
        
        console.log("✅ [环境检查] Java.perform成功执行");
        console.log("   尝试次数: " + debugStats.javaAttachAttempts);
        
        // 获取基本的Java类信息
        try {
            var System = Java.use("java.lang.System");
            var javaVersion = System.getProperty("java.version");
            console.log("   Java版本: " + javaVersion);
            
            var androidVersion = System.getProperty("java.vm.version");
            console.log("   Android VM版本: " + androidVersion);
            
        } catch(e) {
            logError("环境检查", e, {step: "获取系统信息"});
        }
        
        // 开始Hook尝试
        startHookAttempts();
    });
    
} catch(e) {
    logError("环境检查", e, {step: "Java环境初始化"});
    console.log("\n💡 [建议] Java环境不可用，可能的原因:");
    console.log("   1. 应用还未完全启动");
    console.log("   2. 应用有反调试保护");
    console.log("   3. Frida版本不兼容");
    console.log("   4. 设备权限不足");
}

function startHookAttempts() {
    console.log("\n🎯 [Hook尝试] 开始Hook关键类和方法...");
    
    // Hook尝试列表
    var hookTargets = [
        {
            name: "Base64编码",
            className: "android.util.Base64",
            methods: ["encodeToString"],
            critical: false
        },
        {
            name: "AES加密",
            className: "javax.crypto.Cipher",
            methods: ["doFinal", "init"],
            critical: true
        },
        {
            name: "String构造",
            className: "java.lang.String",
            methods: ["$init"],
            critical: false
        },
        {
            name: "MessageDigest",
            className: "java.security.MessageDigest",
            methods: ["digest", "update"],
            critical: false
        },
        {
            name: "OkHttp请求",
            className: "okhttp3.RequestBody",
            methods: ["create"],
            critical: false
        },
        {
            name: "HttpURLConnection",
            className: "java.net.HttpURLConnection",
            methods: ["getOutputStream", "getInputStream"],
            critical: false
        }
    ];
    
    // 尝试Hook每个目标
    hookTargets.forEach(function(target) {
        attemptHook(target);
    });
    
    // 尝试Hook mPaaS相关类
    attemptMpaasHooks();
    
    // 输出统计信息
    setTimeout(function() {
        printDebugStats();
    }, 2000);
}

function attemptHook(target) {
    console.log("\n🔧 [Hook尝试] " + target.name + " (" + target.className + ")");
    debugStats.hookAttempts++;
    
    try {
        // 尝试获取类
        var targetClass = Java.use(target.className);
        debugStats.classesFound.push(target.className);
        logSuccess("类查找", "找到类: " + target.className);
        
        // 尝试Hook方法
        target.methods.forEach(function(methodName) {
            try {
                hookMethod(targetClass, methodName, target.name);
            } catch(e) {
                logError("方法Hook", e, {
                    className: target.className,
                    methodName: methodName,
                    critical: target.critical
                });
            }
        });
        
    } catch(e) {
        debugStats.classesNotFound.push(target.className);
        logError("类查找", e, {
            className: target.className,
            critical: target.critical
        });
        
        if (target.critical) {
            console.log("⚠️ [警告] 关键类Hook失败: " + target.className);
        }
    }
}

function hookMethod(targetClass, methodName, hookName) {
    try {
        if (methodName === "$init") {
            // 特殊处理构造函数
            var overloads = targetClass.$init.overloads;
            console.log("   找到 " + overloads.length + " 个构造函数重载");
            
            // Hook字节数组构造函数
            if (overloads.length > 0) {
                targetClass.$init.overload('[B').implementation = function(bytes) {
                    var result = this.$init(bytes);
                    var str = this.toString();
                    
                    if (str.includes('"data":') || str.includes('"key":') || str.includes('"sign":')) {
                        console.log("\n🎯 [" + hookName + "] 发现关键数据!");
                        console.log("   时间: " + new Date().toLocaleTimeString());
                        console.log("   内容: " + str.substring(0, 200) + "...");
                    }
                    
                    return result;
                };
                
                debugStats.methodsHooked.push(hookName + ".$init");
                debugStats.hookSuccesses++;
                logSuccess("方法Hook", hookName + ".$init 构造函数");
            }
            
        } else {
            // 普通方法Hook
            var method = targetClass[methodName];
            if (method && method.overloads) {
                console.log("   找到 " + method.overloads.length + " 个方法重载");
                
                // 根据不同方法类型进行Hook
                if (methodName === "encodeToString") {
                    hookBase64Encode(targetClass, hookName);
                } else if (methodName === "doFinal") {
                    hookCipherDoFinal(targetClass, hookName);
                } else if (methodName === "create") {
                    hookRequestBodyCreate(targetClass, hookName);
                } else {
                    // 通用Hook
                    hookGenericMethod(targetClass, methodName, hookName);
                }
            }
        }
        
    } catch(e) {
        logError("方法Hook详细", e, {
            methodName: methodName,
            hookName: hookName
        });
    }
}

function hookBase64Encode(Base64Class, hookName) {
    try {
        var originalEncode = Base64Class.encodeToString.overload('[B', 'int');
        
        originalEncode.implementation = function(input, flags) {
            var result = originalEncode.call(this, input, flags);
            
            if (input.length > 32) {
                console.log("\n📝 [" + hookName + "] " + new Date().toLocaleTimeString());
                console.log("   原始长度: " + input.length + " 字节");
                console.log("   编码长度: " + result.length + " 字符");
                console.log("   前32字节: " + bytesToHex(input.slice(0, 32)));
                console.log("   编码结果: " + result.substring(0, 100) + "...");
                
                if (result.length > 200) {
                    console.log("   🎯 可能的加密数据!");
                }
            }
            
            return result;
        };
        
        debugStats.methodsHooked.push(hookName + ".encodeToString");
        debugStats.hookSuccesses++;
        logSuccess("方法Hook", hookName + ".encodeToString");
        
    } catch(e) {
        logError("Base64Hook", e);
    }
}

function hookCipherDoFinal(CipherClass, hookName) {
    try {
        var originalDoFinal = CipherClass.doFinal.overload('[B');
        
        originalDoFinal.implementation = function(input) {
            var result = originalDoFinal.call(this, input);
            var algorithm = this.getAlgorithm();
            
            console.log("\n🔑 [" + hookName + "] " + new Date().toLocaleTimeString());
            console.log("   算法: " + algorithm);
            console.log("   输入长度: " + input.length);
            console.log("   输出长度: " + result.length);
            console.log("   输入数据: " + bytesToHex(input.slice(0, 32)));
            console.log("   输出数据: " + bytesToHex(result.slice(0, 32)));
            
            // 尝试获取密钥
            try {
                var key = this.getKey();
                if (key) {
                    var keyBytes = key.getEncoded();
                    console.log("   🔑 密钥: " + bytesToHex(keyBytes));
                    console.log("   🎯 密钥长度: " + keyBytes.length + " 字节");
                }
            } catch(e) {
                console.log("   ⚠️ 无法获取密钥: " + e);
            }
            
            return result;
        };
        
        debugStats.methodsHooked.push(hookName + ".doFinal");
        debugStats.hookSuccesses++;
        logSuccess("方法Hook", hookName + ".doFinal");
        
    } catch(e) {
        logError("CipherHook", e);
    }
}

function hookRequestBodyCreate(RequestBodyClass, hookName) {
    try {
        var originalCreate = RequestBodyClass.create.overload('okhttp3.MediaType', 'java.lang.String');
        
        originalCreate.implementation = function(mediaType, content) {
            if (content.includes('"data":') || content.includes('"key":') || content.includes('"sign":')) {
                console.log("\n🌐 [" + hookName + "] " + new Date().toLocaleTimeString());
                console.log("   🎯 发现加密请求!");
                console.log("   Content-Type: " + mediaType);
                console.log("   请求内容: " + content);
            }
            
            return originalCreate.call(this, mediaType, content);
        };
        
        debugStats.methodsHooked.push(hookName + ".create");
        debugStats.hookSuccesses++;
        logSuccess("方法Hook", hookName + ".create");
        
    } catch(e) {
        logError("RequestBodyHook", e);
    }
}

function hookGenericMethod(targetClass, methodName, hookName) {
    // 通用方法Hook实现
    console.log("   ⚠️ 通用Hook: " + hookName + "." + methodName);
}

function attemptMpaasHooks() {
    console.log("\n🔍 [mPaaS检测] 尝试Hook mPaaS相关类...");
    
    var mpaasClasses = [
        "com.alipay.mobile.framework.service.common.RpcService",
        "com.alipay.mobile.common.rpc.RpcInvoker",
        "com.alipay.mobile.framework.service.common.SecurityService",
        "com.antfin.cube.platform.handler.ICKRequestHandler",
        "com.mpaas.framework.adapter.api.MPLogger"
    ];
    
    mpaasClasses.forEach(function(className) {
        try {
            var mpaasClass = Java.use(className);
            debugStats.classesFound.push(className);
            console.log("   ✅ 找到mPaaS类: " + className);
        } catch(e) {
            debugStats.classesNotFound.push(className);
            console.log("   ❌ 未找到mPaaS类: " + className);
        }
    });
}

function printDebugStats() {
    console.log("\n📊 [调试统计] Hook尝试统计报告");
    console.log("=" * 50);
    console.log("Java环境:");
    console.log("   尝试次数: " + debugStats.javaAttachAttempts);
    console.log("   连接成功: " + (debugStats.javaAttachSuccess ? "是" : "否"));
    
    console.log("\nHook统计:");
    console.log("   总尝试次数: " + debugStats.hookAttempts);
    console.log("   成功次数: " + debugStats.hookSuccesses);
    console.log("   失败次数: " + debugStats.hookFailures);
    console.log("   成功率: " + (debugStats.hookAttempts > 0 ? 
        Math.round(debugStats.hookSuccesses / debugStats.hookAttempts * 100) : 0) + "%");
    
    console.log("\n找到的类 (" + debugStats.classesFound.length + "个):");
    debugStats.classesFound.forEach(function(className) {
        console.log("   ✅ " + className);
    });
    
    console.log("\n未找到的类 (" + debugStats.classesNotFound.length + "个):");
    debugStats.classesNotFound.forEach(function(className) {
        console.log("   ❌ " + className);
    });
    
    console.log("\n成功Hook的方法 (" + debugStats.methodsHooked.length + "个):");
    debugStats.methodsHooked.forEach(function(methodName) {
        console.log("   🎯 " + methodName);
    });
    
    if (debugStats.errors.length > 0) {
        console.log("\n错误详情 (" + debugStats.errors.length + "个):");
        debugStats.errors.forEach(function(error, index) {
            console.log("   " + (index + 1) + ". [" + error.category + "] " + error.error);
            console.log("      时间: " + error.timestamp);
        });
    }
    
    console.log("\n💡 [建议] 基于统计结果的建议:");
    if (debugStats.hookSuccesses === 0) {
        console.log("   - 所有Hook都失败了，应用可能有强反调试保护");
        console.log("   - 建议使用Charles代理抓包");
        console.log("   - 或尝试在模拟器环境中调试");
    } else if (debugStats.hookSuccesses < 3) {
        console.log("   - 部分Hook成功，但覆盖不全");
        console.log("   - 建议结合网络抓包获取更多数据");
    } else {
        console.log("   - Hook环境良好，请在应用中触发加密操作");
        console.log("   - 进入翼支付权益商城并刷新页面");
    }
    
    console.log("\n🎉 [完成] Hook脚本加载完成!");
    console.log("💡 请在手机上操作翼支付应用...");
}

function bytesToHex(bytes) {
    if (!bytes) return "";
    var hex = "";
    for (var i = 0; i < Math.min(bytes.length, 32); i++) {
        hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2);
    }
    return hex;
}

// 定期输出心跳信息
setInterval(function() {
    console.log("💓 [心跳] Hook脚本运行中... " + new Date().toLocaleTimeString());
    console.log("   成功Hook数: " + debugStats.hookSuccesses);
}, 30000);
