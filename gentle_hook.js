// 温和的Hook脚本 - 避免被检测
// 分阶段逐步Hook关键函数

console.log("🔓 温和Hook脚本启动...");

// 延迟执行，避免立即被检测
setTimeout(function() {
    console.log("⏰ 延迟5秒后开始Hook...");
    
    try {
        // 第一阶段：Hook网络相关函数
        hookNetworkFunctions();
        
        // 延迟执行第二阶段
        setTimeout(function() {
            console.log("🔍 开始Hook加密函数...");
            hookCryptoFunctions();
        }, 3000);
        
        // 延迟执行第三阶段
        setTimeout(function() {
            console.log("📝 开始Hook字符串函数...");
            hookStringFunctions();
        }, 6000);
        
    } catch(e) {
        console.log("❌ Hook过程出错: " + e);
    }
    
}, 5000);

function hookNetworkFunctions() {
    console.log("🌐 Hook网络函数...");
    
    try {
        Java.perform(function() {
            // Hook OkHttp3
            try {
                var RequestBody = Java.use("okhttp3.RequestBody");
                var originalCreate = RequestBody.create.overload('okhttp3.MediaType', 'java.lang.String');
                
                originalCreate.implementation = function(mediaType, content) {
                    console.log("\n🌐 [HTTP请求]");
                    console.log("Content-Type: " + mediaType);
                    
                    if (content.length < 2000) {  // 只显示较短的内容
                        console.log("内容: " + content);
                        
                        // 检查是否包含加密数据
                        if (content.includes('"data":') || content.includes('"key":') || content.includes('"sign":')) {
                            console.log("🎯 发现加密请求!");
                            console.log("完整内容: " + content);
                        }
                    } else {
                        console.log("内容长度: " + content.length + " 字符");
                    }
                    
                    return originalCreate.call(this, mediaType, content);
                };
                
                console.log("✅ OkHttp RequestBody Hook成功");
            } catch(e) {
                console.log("⚠️ OkHttp Hook失败: " + e);
            }
            
            // Hook URL连接
            try {
                var URL = Java.use("java.net.URL");
                var originalOpenConnection = URL.openConnection.overload();
                
                originalOpenConnection.implementation = function() {
                    var url = this.toString();
                    if (url.includes("bestpay") || url.includes("mapi-h5")) {
                        console.log("🔗 [URL连接] " + url);
                    }
                    return originalOpenConnection.call(this);
                };
                
                console.log("✅ URL Hook成功");
            } catch(e) {
                console.log("⚠️ URL Hook失败: " + e);
            }
        });
    } catch(e) {
        console.log("❌ 网络Hook失败: " + e);
    }
}

function hookCryptoFunctions() {
    console.log("🔐 Hook加密函数...");
    
    try {
        // Hook Base64
        var base64_encode = Module.findExportByName("libc.so", "base64_encode");
        if (base64_encode) {
            Interceptor.attach(base64_encode, {
                onEnter: function(args) {
                    console.log("📝 [Base64编码]");
                },
                onLeave: function(retval) {
                    try {
                        var result = Memory.readUtf8String(retval);
                        if (result && result.length > 50) {
                            console.log("编码结果: " + result.substring(0, 100) + "...");
                        }
                    } catch(e) {}
                }
            });
            console.log("✅ Base64 Hook成功");
        }
        
        // Hook常见的加密库
        var crypto_libs = [
            "libssl.so",
            "libcrypto.so", 
            "libopenssl.so"
        ];
        
        crypto_libs.forEach(function(lib) {
            try {
                var module = Process.getModuleByName(lib);
                if (module) {
                    console.log("📚 找到加密库: " + lib);
                    
                    // Hook AES相关函数
                    var aes_funcs = ["AES_encrypt", "AES_decrypt", "AES_set_encrypt_key"];
                    aes_funcs.forEach(function(func_name) {
                        var func_addr = Module.findExportByName(lib, func_name);
                        if (func_addr) {
                            Interceptor.attach(func_addr, {
                                onEnter: function(args) {
                                    console.log("🔑 [" + func_name + "] 调用");
                                    
                                    if (func_name.includes("encrypt") || func_name.includes("decrypt")) {
                                        try {
                                            var data = Memory.readByteArray(args[0], Math.min(32, 16));
                                            console.log("数据: " + hexdump(data, {ansi: true}));
                                        } catch(e) {}
                                    }
                                }
                            });
                            console.log("✅ " + func_name + " Hook成功");
                        }
                    });
                }
            } catch(e) {
                // 忽略不存在的库
            }
        });
        
    } catch(e) {
        console.log("❌ 加密Hook失败: " + e);
    }
}

function hookStringFunctions() {
    console.log("📝 Hook字符串函数...");
    
    try {
        Java.perform(function() {
            // Hook String构造函数
            var String = Java.use("java.lang.String");
            var StringClass = Java.use("java.lang.String");
            
            // Hook字节数组构造函数
            StringClass.$init.overload('[B').implementation = function(bytes) {
                var result = this.$init(bytes);
                var str = this.toString();
                
                // 检查是否是Base64或JSON
                if (str.length > 50 && (str.includes('=') || str.startsWith('{'))) {
                    console.log("📝 [String构造] 长度: " + str.length);
                    
                    if (str.includes('"data":') || str.includes('"key":') || str.includes('"sign":')) {
                        console.log("🎯 发现加密相关字符串!");
                        console.log("内容: " + str);
                    } else if (str.length < 200) {
                        console.log("内容: " + str);
                    }
                }
                
                return result;
            };
            
            console.log("✅ String Hook成功");
        });
    } catch(e) {
        console.log("❌ 字符串Hook失败: " + e);
    }
}

// 监控应用状态
setInterval(function() {
    console.log("💓 Hook脚本运行中... " + new Date().toLocaleTimeString());
}, 30000);  // 每30秒输出一次心跳

console.log("✅ 温和Hook脚本加载完成!");
console.log("📱 请在应用中进行操作，观察输出...");
