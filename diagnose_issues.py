#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题诊断工具
帮助诊断和解决常见的调试问题
"""

import subprocess
import sys
import os
import re

class IssueDiagnoser:
    def __init__(self):
        print("🔧 翼支付调试问题诊断工具")
        print("=" * 50)
    
    def run_command(self, cmd, capture_output=True):
        """执行命令并返回结果"""
        try:
            if capture_output:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
                return result.returncode == 0, result.stdout, result.stderr
            else:
                result = subprocess.run(cmd, shell=True, timeout=30)
                return result.returncode == 0, "", ""
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def check_environment(self):
        """检查环境配置"""
        print("\n🔍 检查环境配置...")
        
        issues = []
        
        # 检查Python
        print("   检查Python...")
        success, stdout, stderr = self.run_command("python --version")
        if success:
            version = stdout.strip()
            print(f"   ✅ Python: {version}")
        else:
            print("   ❌ Python未安装或未添加到PATH")
            issues.append("Python环境问题")
        
        # 检查pip
        print("   检查pip...")
        success, stdout, stderr = self.run_command("pip --version")
        if success:
            print("   ✅ pip可用")
        else:
            print("   ❌ pip不可用")
            issues.append("pip不可用")
        
        # 检查ADB
        print("   检查ADB...")
        success, stdout, stderr = self.run_command("adb version")
        if success:
            version_line = stdout.split('\n')[0]
            print(f"   ✅ ADB: {version_line}")
        else:
            print("   ❌ ADB未安装或未添加到PATH")
            issues.append("ADB未安装")
        
        # 检查Frida
        print("   检查Frida...")
        success, stdout, stderr = self.run_command("frida --version")
        if success:
            version = stdout.strip()
            print(f"   ✅ Frida: {version}")
        else:
            print("   ❌ Frida未安装")
            issues.append("Frida未安装")
        
        # 检查pycryptodome
        print("   检查pycryptodome...")
        try:
            import Crypto
            print("   ✅ pycryptodome已安装")
        except ImportError:
            print("   ❌ pycryptodome未安装")
            issues.append("pycryptodome未安装")
        
        return issues
    
    def check_device_connection(self):
        """检查设备连接"""
        print("\n📱 检查设备连接...")
        
        issues = []
        
        # 检查设备列表
        success, stdout, stderr = self.run_command("adb devices")
        if not success:
            print("   ❌ 无法执行adb devices")
            issues.append("ADB命令失败")
            return issues
        
        lines = stdout.strip().split('\n')[1:]  # 跳过标题行
        devices = [line for line in lines if line.strip() and 'device' in line]
        
        if not devices:
            print("   ❌ 没有检测到设备")
            print("   💡 请确保:")
            print("      - 手机已连接USB")
            print("      - 已开启USB调试")
            print("      - 已授权此计算机")
            issues.append("没有设备连接")
        else:
            print(f"   ✅ 检测到 {len(devices)} 个设备:")
            for device in devices:
                print(f"      {device}")
        
        # 检查端口转发
        print("   检查端口转发...")
        success, stdout, stderr = self.run_command("adb forward --list")
        if success and "tcp:27042" in stdout:
            print("   ✅ 端口转发已设置")
        else:
            print("   ⚠️ 端口转发未设置，正在设置...")
            success, _, _ = self.run_command("adb forward tcp:27042 tcp:27042")
            if success:
                print("   ✅ 端口转发设置成功")
            else:
                print("   ❌ 端口转发设置失败")
                issues.append("端口转发失败")
        
        return issues
    
    def check_target_app(self):
        """检查目标应用"""
        print("\n📱 检查翼支付应用...")
        
        issues = []
        
        # 检查应用是否安装
        success, stdout, stderr = self.run_command("adb shell pm list packages | grep bestpay")
        if success and stdout.strip():
            print("   ✅ 翼支付应用已安装")
            print(f"      包名: {stdout.strip()}")
        else:
            print("   ❌ 翼支付应用未安装")
            issues.append("目标应用未安装")
            return issues
        
        # 检查应用版本
        success, stdout, stderr = self.run_command("adb shell dumpsys package com.chinatelecom.bestpayclient | grep versionName")
        if success and stdout.strip():
            version_line = stdout.strip().split('\n')[0]
            print(f"   📱 应用版本: {version_line}")
        
        # 检查应用是否可调试
        success, stdout, stderr = self.run_command("adb shell dumpsys package com.chinatelecom.bestpayclient | grep debuggable")
        if success and "debuggable=true" in stdout:
            print("   ✅ 应用可调试")
        else:
            print("   ⚠️ 应用不可调试 (正常情况)")
        
        # 检查SO库
        success, stdout, stderr = self.run_command("adb shell ls /data/app/com.chinatelecom.bestpayclient*/lib/arm64/")
        if success:
            so_files = stdout.strip().split('\n')
            crypto_libs = [f for f in so_files if any(keyword in f.lower() for keyword in ['crypto', 'ssl', 'mpaas', 'antssm'])]
            print(f"   📚 找到 {len(so_files)} 个SO库")
            print(f"   🔐 加密相关库: {len(crypto_libs)} 个")
            for lib in crypto_libs[:5]:  # 显示前5个
                print(f"      - {lib}")
        else:
            print("   ⚠️ 无法访问SO库目录")
        
        return issues
    
    def check_frida_server(self):
        """检查Frida Server"""
        print("\n🔧 检查Frida Server...")
        
        issues = []
        
        # 检查设备架构
        success, stdout, stderr = self.run_command("adb shell getprop ro.product.cpu.abi")
        if success:
            arch = stdout.strip()
            print(f"   📱 设备架构: {arch}")
        else:
            print("   ⚠️ 无法获取设备架构")
        
        # 检查Frida Server是否存在
        success, stdout, stderr = self.run_command("adb shell ls /data/local/tmp/frida-server*")
        if success and stdout.strip():
            print("   ✅ Frida Server文件存在")
        else:
            print("   ❌ Frida Server文件不存在")
            issues.append("Frida Server未安装")
        
        # 检查Frida Server是否运行
        success, stdout, stderr = self.run_command("adb shell ps | grep frida")
        if success and stdout.strip():
            print("   ✅ Frida Server正在运行")
        else:
            print("   ⚠️ Frida Server未运行")
            print("   💡 如果是非Root设备，这是正常的")
        
        # 测试Frida连接
        print("   测试Frida连接...")
        success, stdout, stderr = self.run_command("frida-ps -U")
        if success:
            process_count = len(stdout.strip().split('\n')) - 1  # 减去标题行
            print(f"   ✅ Frida连接成功，检测到 {process_count} 个进程")
        else:
            print("   ❌ Frida连接失败")
            print(f"   错误信息: {stderr}")
            issues.append("Frida连接失败")
        
        return issues
    
    def suggest_solutions(self, issues):
        """提供解决方案建议"""
        if not issues:
            print("\n🎉 所有检查都通过了！")
            print("您可以开始调试了:")
            print("   1. 运行 auto_debug.bat")
            print("   2. 或手动执行 frida 命令")
            return
        
        print(f"\n🔧 发现 {len(issues)} 个问题，以下是解决方案:")
        
        solutions = {
            "Python环境问题": [
                "下载并安装Python 3.7+",
                "确保Python添加到系统PATH",
                "重启命令行窗口"
            ],
            "pip不可用": [
                "重新安装Python并确保包含pip",
                "或单独安装pip"
            ],
            "ADB未安装": [
                "下载Android SDK Platform Tools",
                "解压并添加到系统PATH",
                "或安装Android Studio"
            ],
            "Frida未安装": [
                "运行: pip install frida-tools",
                "如果失败，尝试: pip install --upgrade pip",
                "然后重新安装frida-tools"
            ],
            "pycryptodome未安装": [
                "运行: pip install pycryptodome",
                "如果失败，尝试: pip install pycrypto"
            ],
            "没有设备连接": [
                "确保USB线连接正常",
                "在手机设置中开启USB调试",
                "在手机上授权此计算机",
                "尝试重新连接USB线"
            ],
            "端口转发失败": [
                "运行: adb kill-server",
                "然后: adb start-server",
                "重新执行: adb forward tcp:27042 tcp:27042"
            ],
            "目标应用未安装": [
                "从应用商店安装翼支付",
                "或使用adb install安装APK文件"
            ],
            "Frida Server未安装": [
                "如果是Root设备:",
                "  1. 下载对应架构的frida-server",
                "  2. adb push frida-server /data/local/tmp/",
                "  3. adb shell chmod 755 /data/local/tmp/frida-server",
                "  4. adb shell /data/local/tmp/frida-server &",
                "如果是非Root设备，可以直接使用frida -U"
            ],
            "Frida连接失败": [
                "检查设备连接",
                "重启adb: adb kill-server && adb start-server",
                "重新设置端口转发",
                "如果是Root设备，确保frida-server正在运行"
            ]
        }
        
        for i, issue in enumerate(issues, 1):
            print(f"\n{i}. {issue}")
            if issue in solutions:
                for solution in solutions[issue]:
                    print(f"   💡 {solution}")
            else:
                print("   💡 请查阅相关文档或寻求技术支持")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🚀 开始完整系统诊断...\n")
        
        all_issues = []
        
        # 环境检查
        issues = self.check_environment()
        all_issues.extend(issues)
        
        # 设备连接检查
        issues = self.check_device_connection()
        all_issues.extend(issues)
        
        # 目标应用检查
        issues = self.check_target_app()
        all_issues.extend(issues)
        
        # Frida Server检查
        issues = self.check_frida_server()
        all_issues.extend(issues)
        
        # 提供解决方案
        self.suggest_solutions(all_issues)
        
        print(f"\n" + "="*50)
        print(f"🔧 诊断完成！")
        if all_issues:
            print(f"❌ 发现 {len(all_issues)} 个问题需要解决")
        else:
            print(f"✅ 系统状态良好，可以开始调试")
        print(f"="*50)

def main():
    diagnoser = IssueDiagnoser()
    diagnoser.run_full_diagnosis()

if __name__ == "__main__":
    main()
