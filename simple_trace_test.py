#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的frida-trace测试工具
快速测试系统调用跟踪功能
"""

import subprocess
import time
import os

def run_simple_trace():
    """运行简化的跟踪测试"""
    print("🔍 简化frida-trace测试")
    print("=" * 50)
    
    # 检查翼支付是否在运行
    print("\n📱 检查翼支付应用状态...")
    success, stdout, stderr = run_command("frida-ps -U | findstr -i bestpay")
    
    if success and stdout.strip():
        print("   ✅ 翼支付应用正在运行")
        for line in stdout.strip().split('\n'):
            print(f"      {line}")
    else:
        print("   ❌ 翼支付应用未运行")
        return False
    
    # 创建输出目录
    output_dir = "simple_trace_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试1: 基础系统调用跟踪
    print("\n🔍 测试1: 基础系统调用跟踪 (15秒)...")
    print("   💡 请在手机上操作翼支付应用")
    
    cmd1 = "frida-trace -U com.chinatelecom.bestpayclient -i 'open*'"
    test_trace(cmd1, "基础系统调用", 15)
    
    # 测试2: 网络函数跟踪
    print("\n🔍 测试2: 网络函数跟踪 (15秒)...")
    print("   💡 请在手机上刷新权益商城页面")
    
    cmd2 = "frida-trace -U com.chinatelecom.bestpayclient -i 'connect*' -i 'send*'"
    test_trace(cmd2, "网络函数", 15)
    
    # 测试3: Java方法跟踪
    print("\n🔍 测试3: Java方法跟踪 (15秒)...")
    print("   💡 请在手机上点击活动或商品")
    
    cmd3 = "frida-trace -U com.chinatelecom.bestpayclient -j '*!*Base64*'"
    test_trace(cmd3, "Java方法", 15)
    
    print("\n🎉 简化跟踪测试完成!")
    return True

def test_trace(cmd, test_name, duration):
    """执行单个跟踪测试"""
    try:
        print(f"   🚀 执行: {cmd}")
        
        process = subprocess.Popen(
            cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待指定时间
        time.sleep(duration)
        
        # 停止跟踪
        process.terminate()
        stdout, stderr = process.communicate(timeout=5)
        
        # 分析输出
        if stdout and len(stdout.strip()) > 100:
            print(f"   ✅ {test_name}跟踪成功 - 捕获了数据")
            
            # 保存输出
            output_file = f"simple_trace_output/{test_name.replace(' ', '_')}_output.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"=== {test_name} 跟踪输出 ===\n")
                f.write(f"命令: {cmd}\n")
                f.write(f"时长: {duration}秒\n\n")
                f.write("=== STDOUT ===\n")
                f.write(stdout)
                f.write("\n=== STDERR ===\n")
                f.write(stderr)
            
            print(f"      💾 输出已保存: {output_file}")
            
            # 显示前几行输出
            lines = stdout.strip().split('\n')
            print(f"      📊 捕获行数: {len(lines)}")
            if len(lines) > 0:
                print("      📝 输出示例:")
                for line in lines[:3]:
                    if line.strip():
                        print(f"         {line[:80]}...")
        else:
            print(f"   ⚠️ {test_name}跟踪 - 无明显输出")
            if stderr:
                print(f"      错误: {stderr[:100]}...")
        
    except Exception as e:
        print(f"   ❌ {test_name}跟踪失败: {e}")

def run_command(cmd, timeout=10):
    """执行命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='ignore')
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def main():
    print("🔍 简化frida-trace测试工具")
    print("🎯 快速测试系统调用跟踪功能")
    
    confirm = input("\n是否开始简化测试? (y/n): ").strip().lower()
    if confirm != 'y':
        print("👋 测试已取消")
        return
    
    success = run_simple_trace()
    
    if success:
        print("\n💡 测试完成建议:")
        print("   1. 查看 simple_trace_output/ 目录中的输出文件")
        print("   2. 分析捕获的系统调用")
        print("   3. 如果有数据，说明frida-trace工作正常")
        print("   4. 可以尝试更复杂的跟踪命令")

if __name__ == "__main__":
    main()
