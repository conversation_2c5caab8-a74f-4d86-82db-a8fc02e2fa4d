# Android Project Analysis Script
# Analyze APK resource folder structure and content

Write-Host "Starting Android Project Analysis..." -ForegroundColor Green
Write-Host "============================================================"

# 1. Basic Project Information
Write-Host "`nApp Basic Information:" -ForegroundColor Cyan

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw

    # Extract package name
    if ($manifest -match 'package="([^"]+)"') {
        $packageName = $matches[1]
        Write-Host "   Package Name: $packageName"
    }

    # Extract version info
    if ($manifest -match 'android:versionName="([^"]+)"') {
        $versionName = $matches[1]
        Write-Host "   Version Name: $versionName"
    }

    if ($manifest -match 'android:versionCode="([^"]+)"') {
        $versionCode = $matches[1]
        Write-Host "   Version Code: $versionCode"
    }

    # Extract SDK versions
    if ($manifest -match 'android:minSdkVersion="([^"]+)"') {
        $minSdk = $matches[1]
        Write-Host "   Min SDK Version: $minSdk"
    }

    if ($manifest -match 'android:targetSdkVersion="([^"]+)"') {
        $targetSdk = $matches[1]
        Write-Host "   Target SDK Version: $targetSdk"
    }

    # Count permissions
    $permissions = [regex]::Matches($manifest, '<uses-permission[^>]+android:name="([^"]+)"') | ForEach-Object { $_.Groups[1].Value }
    Write-Host "   Permission Count: $($permissions.Count)"

    # Count components
    $activities = [regex]::Matches($manifest, '<activity[^>]*').Count
    $services = [regex]::Matches($manifest, '<service[^>]*').Count
    $receivers = [regex]::Matches($manifest, '<receiver[^>]*').Count
    $providers = [regex]::Matches($manifest, '<provider[^>]*').Count

    Write-Host "   Activity Count: $activities"
    Write-Host "   Service Count: $services"
    Write-Host "   Receiver Count: $receivers"
    Write-Host "   Provider Count: $providers"
}

# 2. File Structure Analysis
Write-Host "`nFile Structure Analysis:" -ForegroundColor Cyan

$allFiles = Get-ChildItem -Recurse -File
$totalFiles = $allFiles.Count
$totalSize = ($allFiles | Measure-Object -Property Length -Sum).Sum
$totalSizeMB = [math]::Round($totalSize / 1MB, 2)

Write-Host "   Total Files: $totalFiles"
Write-Host "   Total Size: $totalSizeMB MB"

# File type statistics
$fileTypes = $allFiles | Group-Object Extension | Sort-Object Count -Descending | Select-Object -First 15
Write-Host "   Main File Types:"
foreach ($type in $fileTypes) {
    $ext = if ($type.Name) { $type.Name } else { "No Extension" }
    Write-Host "     $ext : $($type.Count)"
}

# 3. 依赖分析
Write-Host "`n📦 依赖分析:" -ForegroundColor Cyan

# Native库分析
if (Test-Path "lib\arm64-v8a") {
    $nativeLibs = Get-ChildItem "lib\arm64-v8a" -Filter "*.so"
    Write-Host "   Native库数量: $($nativeLibs.Count)"
    
    # 分析库的类型
    $securityLibs = $nativeLibs | Where-Object { $_.Name -match "(crypto|ssl|security|encrypt)" }
    $mediaLibs = $nativeLibs | Where-Object { $_.Name -match "(media|audio|video|image|jpeg)" }
    $networkLibs = $nativeLibs | Where-Object { $_.Name -match "(http|net|curl|ssl)" }
    
    if ($securityLibs.Count -gt 0) {
        Write-Host "   安全相关库: $($securityLibs.Count)个"
    }
    if ($mediaLibs.Count -gt 0) {
        Write-Host "   媒体相关库: $($mediaLibs.Count)个"
    }
    if ($networkLibs.Count -gt 0) {
        Write-Host "   网络相关库: $($networkLibs.Count)个"
    }
}

# 框架依赖分析
if (Test-Path "META-INF") {
    $versionFiles = Get-ChildItem "META-INF" -Filter "*.version"
    Write-Host "   框架依赖数量: $($versionFiles.Count)"
    
    # 分析主要框架
    $androidxLibs = $versionFiles | Where-Object { $_.Name -match "androidx" }
    $kotlinLibs = $versionFiles | Where-Object { $_.Name -match "kotlin" }
    
    if ($androidxLibs.Count -gt 0) {
        Write-Host "   AndroidX库: $($androidxLibs.Count)个"
    }
    if ($kotlinLibs.Count -gt 0) {
        Write-Host "   Kotlin库: $($kotlinLibs.Count)个"
    }
}

# 4. 第三方SDK分析
Write-Host "`n🔌 第三方SDK分析:" -ForegroundColor Cyan

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    $sdks = @()
    
    # 检测常见SDK
    if ($manifest -match "huawei|hms") { $sdks += "华为HMS Core" }
    if ($manifest -match "alipay") { $sdks += "支付宝SDK" }
    if ($manifest -match "tencent|qq|wechat") { $sdks += "腾讯SDK" }
    if ($manifest -match "bytedance|douyin") { $sdks += "字节跳动SDK" }
    if ($manifest -match "kwad|kuaishou") { $sdks += "快手SDK" }
    if ($manifest -match "mpaas") { $sdks += "蚂蚁mPaaS" }
    if ($manifest -match "agora") { $sdks += "声网Agora" }
    if ($manifest -match "bugly") { $sdks += "腾讯Bugly" }
    if ($manifest -match "jpush") { $sdks += "极光推送" }
    if ($manifest -match "umeng") { $sdks += "友盟SDK" }
    
    foreach ($sdk in $sdks) {
        Write-Host "   • $sdk"
    }
}

# 5. 功能特性分析
Write-Host "`n🎯 功能特性分析:" -ForegroundColor Cyan

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    $features = @()
    
    # 从权限推断功能
    if ($manifest -match "CAMERA") { $features += "相机功能" }
    if ($manifest -match "RECORD_AUDIO") { $features += "录音功能" }
    if ($manifest -match "LOCATION") { $features += "定位功能" }
    if ($manifest -match "NFC") { $features += "NFC支付" }
    if ($manifest -match "FINGERPRINT|BIOMETRIC") { $features += "生物识别" }
    if ($manifest -match "BLUETOOTH") { $features += "蓝牙功能" }
    if ($manifest -match "READ_CONTACTS") { $features += "通讯录访问" }
    
    foreach ($feature in $features) {
        Write-Host "   • $feature"
    }
}

# 从assets文件推断功能
if (Test-Path "assets") {
    $assetFiles = Get-ChildItem "assets" -Recurse -File
    
    $assetFeatures = @()
    
    if ($assetFiles | Where-Object { $_.Name -match "face|liveness" }) {
        $assetFeatures += "人脸识别"
    }
    if ($assetFiles | Where-Object { $_.Name -match "ocr|idcard" }) {
        $assetFeatures += "OCR识别"
    }
    if ($assetFiles | Where-Object { $_.Name -match "bankcard" }) {
        $assetFeatures += "银行卡识别"
    }
    if ($assetFiles | Where-Object { $_.Name -match "qr|scan" }) {
        $assetFeatures += "二维码扫描"
    }
    if ($assetFiles | Where-Object { $_.Name -match "map|gps" }) {
        $assetFeatures += "地图导航"
    }
    
    foreach ($feature in $assetFeatures) {
        Write-Host "   • $feature"
    }
}

# 6. 安全分析
Write-Host "`n🔒 安全分析:" -ForegroundColor Cyan

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    # 安全特性
    $securityFeatures = @()
    if ($manifest -match "FINGERPRINT") { $securityFeatures += "指纹认证" }
    if ($manifest -match "BIOMETRIC") { $securityFeatures += "生物识别认证" }
    
    if ($securityFeatures.Count -gt 0) {
        Write-Host "   安全特性:"
        foreach ($feature in $securityFeatures) {
            Write-Host "     • $feature"
        }
    }
    
    # 潜在风险
    $risks = @()
    if ($manifest -match "SYSTEM_ALERT_WINDOW") { $risks += "系统级窗口权限" }
    if ($manifest -match "WRITE_EXTERNAL_STORAGE") { $risks += "外部存储写入权限" }
    if ($manifest -match "INSTALL_PACKAGES") { $risks += "应用安装权限" }
    
    if ($risks.Count -gt 0) {
        Write-Host "   潜在风险:"
        foreach ($risk in $risks) {
            Write-Host "     ⚠️  $risk"
        }
    }
}

# 7. 技术架构分析
Write-Host "`n🏗️ 技术架构分析:" -ForegroundColor Cyan

# 检查混合开发技术
if (Test-Path "assets\flutter_assets") {
    Write-Host "   • Flutter混合开发"
}

if (Test-Path "assets\h5_bridge.js") {
    Write-Host "   • H5 Bridge技术"
}

if (Test-Path "assets\web") {
    Write-Host "   • WebView技术"
}

# 检查小程序技术
if (Test-Path "assets\tiny") {
    Write-Host "   • 小程序技术"
}

# 检查mPaaS
if (Test-Path "assets\mpaas.properties") {
    Write-Host "   • 蚂蚁mPaaS移动开发平台"
}

# 8. 资源分析
Write-Host "`n🎨 资源分析:" -ForegroundColor Cyan

if (Test-Path "res") {
    $resFiles = Get-ChildItem "res" -Recurse -File
    $drawables = $resFiles | Where-Object { $_.Directory.Name -match "drawable" }
    $layouts = $resFiles | Where-Object { $_.Directory.Name -match "layout" }
    $values = $resFiles | Where-Object { $_.Directory.Name -match "values" }
    
    Write-Host "   图片资源: $($drawables.Count)个"
    Write-Host "   布局文件: $($layouts.Count)个"
    Write-Host "   配置文件: $($values.Count)个"
    
    # 多语言支持
    $langDirs = Get-ChildItem "res" -Directory | Where-Object { $_.Name -match "values-[a-z]{2}" }
    if ($langDirs.Count -gt 0) {
        Write-Host "   多语言支持: $($langDirs.Count)种语言"
    }
}

Write-Host "`n" + "=" * 60
Write-Host "✅ 分析完成!" -ForegroundColor Green
Write-Host "=" * 60
