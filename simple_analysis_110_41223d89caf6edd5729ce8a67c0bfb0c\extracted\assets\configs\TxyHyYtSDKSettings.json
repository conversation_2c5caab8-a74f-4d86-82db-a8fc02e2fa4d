{"version": "1.0.0", "sdk_settings": {"action+reflect_settings": {"resource_online": false, "timeout_countdown_ms": 5000, "app_id": "huiyan_private", "action_security_level": 1, "reflect_security_level": 2, "stable_frame_num": 0, "action_frame_num": 20, "last_action_frame_num": 3, "action_default_seq": [0], "predetect_countdown_ms": 5000, "frame_update_timeout_ms": 8000, "need_check_multiface": false, "reflect_tips_countdown_ms": 0, "force_pose_check": false, "need_close_timeout": true, "control_config": "need_action_video=true", "config_api_url": "/reflect/getLiveType", "result_api_url": "/reflect/compare", "need_encrypt": false, "enhance_encrypt_method": 0, "same_tips_filter": false, "need_bugly_shared": true}, "silent_settings": {"resource_online": false, "timeout_countdown_ms": 10000, "check_eye_open": false, "open_eye_threshold": 0.19, "app_id": "huiyan_private", "action_security_level": 1, "stable_frame_num": 0, "action_frame_num": 20, "result_api_url": "/reflect/compare", "action_default_seq": [5], "same_tips_filter": false, "need_bugly_shared": true, "request_options": {"best_image": true, "do_live": true, "do_mouth_detect": true, "do_eye_detect": true}}, "action_settings": {"resource_online": false, "timeout_countdown_ms": 10000, "app_id": "huiyan_private", "action_security_level": 1, "need_close_timeout": true, "stable_frame_num": 0, "action_frame_num": 20, "last_action_frame_num": 3, "result_api_url": "/reflect/compare", "action_default_seq": [1, 2], "manual_trigger": false, "same_tips_filter": false, "need_bugly_shared": true, "request_options": {"best_image": true, "do_live": true, "do_mouth_detect": true, "do_eye_detect": true}, "action_inner_settings": {"max_gray_val": 210, "min_gray_val": 40, "min_gray_ratio": 0.5, "max_gray_ratio": 0.5, "reject_iou_dq_thresh": 0.96, "reject_iou_thresh": 0.8, "reject_range_thread": 300, "reject_frame_count_ratio": 0.95}}, "need_network": true}}