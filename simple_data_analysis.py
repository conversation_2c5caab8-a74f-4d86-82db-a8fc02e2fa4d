#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的data字段分析工具
专注于数据结构和模式分析
"""

import base64
import json
import struct
import hashlib

class SimpleDataAnalyzer:
    def __init__(self):
        # 示例数据
        self.encrypted_data = "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA27VA51QMMQDe/RiVRdj0+UWr1g6Rd28afJX1JNy3SnfyB28swubBraklZQD94z0x5DutIu+yHYLHoSpE7bVt/yvA0zueZIhMeNntIfdduuzDcBL0CLMjU5fdqjWRK14rh9mDQXi2uN5kTTt2jlJ7qHw0c650VeaVRLy5oG/VbFRUKMwgW+NslTTZ9C/ijfOWcznv5nmBnZu1FrQAi2YaIDJ//rGt8DjB7mHaTaw8Kn2kMolMSrxIje8O5F8rDQQCkub9IHiGZ6ivT2d/PsUAsEy2TmjuMqMaq8oifhihr1WRK8v3sXDI1EB4dLGjsv1GRomMNys0nPgwmHB4iKYvJGS3hYFzWLUhyjDiIj+tZNSLp2T1GjJd3AKBdg3d3px1fZXte9g7QeXG1VhvRBuYAU6lIiSt44I/7M9kKoJ//31lhiv3vlcHsi3DNdHNTAMPbRxfHXlcAj0b4ObmQJULFcL+U8T8Es0TZfqgoNnEKeRA"
        self.encrypted_key = "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOboibNuxG95/46P8rizN4R27a/FXmZEZygYkNmAC9U5PSc5ETxa5PTnhQAf7qpwg7uj/yhKPPK7Ejlo4AgoG9LjuVS8t4Hoy41i4n02OGwnmk="
        self.sign = "751C0392CAB814248DBB7A4B1BCE34CD"
        
        # 解码数据
        self.data_bytes = self.safe_base64_decode(self.encrypted_data)
        self.key_bytes = self.safe_base64_decode(self.encrypted_key)
        
        print("🔍 翼支付data字段深度分析")
        print("=" * 50)
    
    def safe_base64_decode(self, data):
        """安全的Base64解码"""
        try:
            missing_padding = len(data) % 4
            if missing_padding:
                data += '=' * (4 - missing_padding)
            return base64.b64decode(data)
        except Exception as e:
            print(f"Base64解码错误: {e}")
            return b""
    
    def analyze_basic_structure(self):
        """分析基本结构"""
        print(f"\n📊 基本信息:")
        print(f"   data字段长度: {len(self.data_bytes)} 字节")
        print(f"   key字段长度: {len(self.key_bytes)} 字节")
        print(f"   sign字段: {self.sign} (MD5)")
        
        print(f"\n🔍 data字段十六进制分析:")
        data = self.data_bytes
        
        # 按16字节分组显示
        for i in range(0, min(len(data), 128), 16):
            chunk = data[i:i+16]
            hex_str = ' '.join(f'{b:02x}' for b in chunk)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"   {i:04x}: {hex_str:<48} |{ascii_str}|")
        
        if len(data) > 128:
            print(f"   ... (还有 {len(data) - 128} 字节)")
    
    def analyze_possible_structures(self):
        """分析可能的数据结构"""
        print(f"\n🏗️ 可能的数据结构:")
        
        data = self.data_bytes
        
        # 结构1: 直接是加密数据
        print(f"   结构1 - 纯加密数据:")
        print(f"     长度: {len(data)} 字节")
        print(f"     16字节对齐: {'是' if len(data) % 16 == 0 else '否'}")
        
        # 结构2: IV + 加密数据
        if len(data) >= 16:
            iv = data[:16]
            encrypted = data[16:]
            print(f"   结构2 - IV(16) + 加密数据:")
            print(f"     可能的IV: {iv.hex()}")
            print(f"     加密数据长度: {len(encrypted)} 字节")
            print(f"     加密数据16字节对齐: {'是' if len(encrypted) % 16 == 0 else '否'}")
        
        # 结构3: 长度字段 + IV + 加密数据
        if len(data) >= 20:
            length_field = struct.unpack('>I', data[:4])[0]
            iv = data[4:20]
            encrypted = data[20:]
            print(f"   结构3 - 长度(4) + IV(16) + 加密数据:")
            print(f"     长度字段: {length_field}")
            print(f"     可能的IV: {iv.hex()}")
            print(f"     加密数据长度: {len(encrypted)} 字节")
            print(f"     长度字段是否匹配: {'是' if length_field == len(encrypted) else '否'}")
        
        # 结构4: 魔数 + 数据
        if len(data) >= 8:
            magic = data[:4]
            print(f"   结构4 - 魔数分析:")
            print(f"     前4字节: {magic.hex()} ({magic})")
            print(f"     是否可打印: {'是' if all(32 <= b <= 126 for b in magic) else '否'}")
    
    def analyze_entropy_and_patterns(self):
        """分析熵和模式"""
        print(f"\n📈 数据特征分析:")
        
        data = self.data_bytes
        
        # 计算字节频率
        byte_freq = {}
        for byte in data:
            byte_freq[byte] = byte_freq.get(byte, 0) + 1
        
        # 计算熵
        import math
        entropy = 0
        for count in byte_freq.values():
            p = count / len(data)
            if p > 0:
                entropy -= p * math.log2(p)
        
        print(f"   数据熵: {entropy:.2f}/8.0")
        print(f"   唯一字节数: {len(byte_freq)}/256")
        print(f"   重复字节最多: {max(byte_freq.values())}次")
        
        # 分析16字节块
        blocks = [data[i:i+16] for i in range(0, len(data), 16)]
        unique_blocks = len(set(blocks))
        print(f"   16字节块总数: {len(blocks)}")
        print(f"   唯一16字节块: {unique_blocks}")
        print(f"   块重复率: {(len(blocks) - unique_blocks) / len(blocks) * 100:.1f}%")
        
        # 检查连续字节
        consecutive_count = 0
        max_consecutive = 0
        for i in range(1, len(data)):
            if data[i] == data[i-1]:
                consecutive_count += 1
                max_consecutive = max(max_consecutive, consecutive_count)
            else:
                consecutive_count = 0
        
        print(f"   最长连续相同字节: {max_consecutive + 1}")
    
    def analyze_key_field(self):
        """分析key字段"""
        print(f"\n🔑 key字段分析:")
        
        key_data = self.key_bytes
        print(f"   长度: {len(key_data)} 字节")
        print(f"   十六进制: {key_data.hex()}")
        
        # 分析可能的结构
        if len(key_data) == 128:
            print(f"   可能是RSA-1024加密的结果")
        elif len(key_data) == 256:
            print(f"   可能是RSA-2048加密的结果")
        elif len(key_data) == 64:
            print(f"   可能是ECC-P256加密的结果")
        
        # 检查是否有明显的结构
        print(f"   前16字节: {key_data[:16].hex()}")
        print(f"   后16字节: {key_data[-16:].hex()}")
        
        # 计算熵
        byte_freq = {}
        for byte in key_data:
            byte_freq[byte] = byte_freq.get(byte, 0) + 1
        
        import math
        entropy = 0
        for count in byte_freq.values():
            p = count / len(key_data)
            if p > 0:
                entropy -= p * math.log2(p)
        
        print(f"   key字段熵: {entropy:.2f}/8.0")
        print(f"   随机性: {'高' if entropy > 7.5 else '中' if entropy > 6.0 else '低'}")
    
    def try_simple_xor(self):
        """尝试简单的XOR分析"""
        print(f"\n⚡ 简单XOR分析:")
        
        data = self.data_bytes
        
        # 尝试与常见模式XOR
        patterns = [
            (b'{"', "JSON开头"),
            (b'<?xml', "XML开头"),
            (b'<html', "HTML开头"),
            (b'\x00\x00\x00\x00', "空字节"),
            (b'HTTP', "HTTP协议"),
        ]
        
        for pattern, desc in patterns:
            if len(data) >= len(pattern):
                xor_result = bytes(a ^ b for a, b in zip(data[:len(pattern)], pattern))
                print(f"   如果开头是{desc}: XOR密钥 = {xor_result.hex()}")
                
                # 检查XOR密钥是否有规律
                if len(set(xor_result)) == 1:
                    print(f"     -> 单字节XOR密钥: 0x{xor_result[0]:02x}")
                elif len(xor_result) >= 4 and xor_result[:2] == xor_result[2:4]:
                    print(f"     -> 可能的重复模式")
    
    def generate_frida_hooks(self):
        """生成Frida Hook脚本建议"""
        print(f"\n🎯 Frida Hook建议:")
        
        print(f"   关键SO库:")
        print(f"   - libmpaas_crypto.so (mPaaS加密)")
        print(f"   - libantssm.so (国密算法)")
        print(f"   - libopenssl.so (OpenSSL)")
        
        print(f"\n   建议Hook的函数:")
        print(f"   - AES_encrypt / AES_decrypt")
        print(f"   - SM2_encrypt / SM2_decrypt") 
        print(f"   - Base64_encode / Base64_decode")
        print(f"   - MD5_Final")
        print(f"   - 网络请求相关函数")
        
        print(f"\n   Hook脚本示例:")
        hook_script = '''
   // Hook AES加密
   var aes_encrypt = Module.findExportByName("libmpaas_crypto.so", "AES_encrypt");
   if (aes_encrypt) {
       Interceptor.attach(aes_encrypt, {
           onEnter: function(args) {
               console.log("[AES_encrypt] 输入数据:", hexdump(args[0], {length: 32}));
               console.log("[AES_encrypt] 密钥:", hexdump(args[1], {length: 32}));
           },
           onLeave: function(retval) {
               console.log("[AES_encrypt] 输出:", retval);
           }
       });
   }
        '''
        print(hook_script)
    
    def run_analysis(self):
        """运行完整分析"""
        self.analyze_basic_structure()
        self.analyze_possible_structures()
        self.analyze_entropy_and_patterns()
        self.analyze_key_field()
        self.try_simple_xor()
        self.generate_frida_hooks()
        
        print(f"\n" + "=" * 50)
        print(f"✅ 分析完成!")
        print(f"\n💡 下一步建议:")
        print(f"1. 使用Frida Hook运行时的加密函数")
        print(f"2. 逆向libmpaas_crypto.so获取加密实现")
        print(f"3. 分析更多请求样本找出规律")
        print(f"4. 尝试重放攻击测试")
        print(f"=" * 50)

def main():
    analyzer = SimpleDataAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
