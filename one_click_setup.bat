@echo off
chcp 65001 >nul
title 翼支付一键调试助手
color 0A

:main_menu
cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                    📱 翼支付一键调试助手                      ║
echo     ║                                                              ║
echo     ║  🎯 自动检测环境并指引您完成真机调试设置                      ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.
echo     🚀 选择操作:
echo.
echo        1️⃣  完整环境检查和设置 (推荐新手)
echo        2️⃣  快速启动调试 (环境已配置)
echo        3️⃣  手动安装缺失组件
echo        4️⃣  查看详细帮助文档
echo        5️⃣  退出程序
echo.
set /p choice=     请选择 (1-5): 

if "%choice%"=="1" goto full_setup
if "%choice%"=="2" goto quick_debug
if "%choice%"=="3" goto manual_install
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit_program
goto main_menu

:full_setup
cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                      🔍 完整环境检查                          ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

echo     📋 第1步: 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo     ❌ Python未安装
    echo.
    echo     💡 请先安装Python 3.7+:
    echo        1. 访问 https://www.python.org/downloads/
    echo        2. 下载并安装最新版Python
    echo        3. 安装时勾选 "Add Python to PATH"
    echo        4. 重新运行此脚本
    echo.
    pause
    goto main_menu
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo     ✅ Python %%i 已安装
)

echo.
echo     📋 第2步: 检查和安装Frida...
frida --version >nul 2>&1
if %errorlevel% neq 0 (
    echo     ⚠️  Frida未安装，正在自动安装...
    echo.
    pip install frida-tools
    if %errorlevel% neq 0 (
        echo     ❌ Frida安装失败
        echo.
        echo     💡 手动安装方法:
        echo        1. 打开命令行
        echo        2. 运行: pip install --upgrade pip
        echo        3. 运行: pip install frida-tools
        echo.
        pause
        goto main_menu
    ) else (
        echo     ✅ Frida安装成功
    )
) else (
    for /f "tokens=1" %%i in ('frida --version 2^>^&1') do echo     ✅ Frida %%i 已安装
)

echo.
echo     📋 第3步: 检查ADB工具...
set "ADB_PATH=.\android-tools\platform-tools\adb.exe"
if exist "%ADB_PATH%" (
    echo     ✅ 便携版ADB已存在
    set "ADB_CMD=%ADB_PATH%"
) else (
    adb version >nul 2>&1
    if %errorlevel% equ 0 (
        echo     ✅ 系统ADB可用
        set "ADB_CMD=adb"
    ) else (
        echo     ❌ ADB工具不存在
        echo.
        echo     🔧 正在为您下载ADB工具...
        call :download_adb
        if exist "%ADB_PATH%" (
            echo     ✅ ADB下载完成
            set "ADB_CMD=%ADB_PATH%"
        ) else (
            echo     ❌ ADB下载失败
            echo.
            echo     💡 手动下载方法:
            echo        1. 访问: https://developer.android.com/studio/releases/platform-tools
            echo        2. 下载 "SDK Platform-Tools for Windows"
            echo        3. 解压到当前目录的 android-tools 文件夹
            echo        4. 重新运行此脚本
            echo.
            pause
            goto main_menu
        )
    )
)

echo.
echo     📋 第4步: 手机连接指导...
echo.
echo     📱 请按照以下步骤设置您的手机:
echo.
echo        🔧 开启开发者选项:
echo           1. 打开手机"设置"
echo           2. 找到"关于手机"或"关于设备"
echo           3. 连续点击"版本号"7次
echo           4. 返回设置，找到"开发者选项"
echo.
echo        🔧 开启USB调试:
echo           1. 进入"开发者选项"
echo           2. 开启"USB调试"
echo           3. 开启"USB安装"(如果有)
echo.
echo        🔧 连接手机:
echo           1. 用USB数据线连接手机到电脑
echo           2. 手机会弹出"允许USB调试"对话框
echo           3. 勾选"始终允许来自这台计算机"
echo           4. 点击"确定"
echo.
set /p phone_ready=     手机已按上述步骤设置完成了吗? (y/n): 
if /i not "%phone_ready%"=="y" (
    echo.
    echo     💡 请完成手机设置后重新运行脚本
    pause
    goto main_menu
)

echo.
echo     📋 第5步: 检测设备连接...
"%ADB_CMD%" devices
echo.
echo     ❓ 检查设备状态:
echo        - 如果显示 "device"，说明连接成功 ✅
echo        - 如果显示 "unauthorized"，请在手机上点击"始终允许" ⚠️
echo        - 如果没有设备，请检查USB连接 ❌
echo.
set /p device_ok=     设备连接状态正常吗? (y/n): 
if /i not "%device_ok%"=="y" (
    echo.
    echo     🔧 设备连接问题解决方案:
    echo        1. 重新插拔USB线
    echo        2. 更换USB线或USB接口
    echo        3. 重启手机和电脑
    echo        4. 检查手机驱动是否正确安装
    echo.
    pause
    goto main_menu
)

echo.
echo     📋 第6步: 检查翼支付应用...
"%ADB_CMD%" shell pm list packages | findstr bestpay >nul 2>&1
if %errorlevel% neq 0 (
    echo     ❌ 翼支付应用未安装
    echo.
    echo     📱 请在手机上安装翼支付:
    echo        - 华为手机: 华为应用市场
    echo        - 小米手机: 小米应用商店
    echo        - OPPO/vivo: 软件商店
    echo        - 其他: Google Play 或官网下载
    echo.
    echo     安装完成后请重新运行此脚本
    pause
    goto main_menu
) else (
    echo     ✅ 翼支付应用已安装
)

echo.
echo     📋 第7步: 设置调试环境...
"%ADB_CMD%" forward tcp:27042 tcp:27042 >nul 2>&1
echo     ✅ 端口转发已设置

echo.
echo     📋 第8步: 测试Frida连接...
frida-ps -U >nul 2>&1
if %errorlevel% equ 0 (
    echo     ✅ Frida连接测试成功
) else (
    echo     ⚠️  Frida连接测试失败，但可能仍可调试
)

echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                      🎉 环境配置完成!                         ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.
echo     🚀 现在可以开始调试了!
echo.
set /p start_debug=     是否立即开始Hook调试? (y/n): 
if /i "%start_debug%"=="y" goto start_hook_debug
goto main_menu

:quick_debug
cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                      🚀 快速启动调试                          ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

REM 快速检查关键组件
frida --version >nul 2>&1
if %errorlevel% neq 0 (
    echo     ❌ Frida未安装，请选择"完整环境检查和设置"
    pause
    goto main_menu
)

set "ADB_PATH=.\android-tools\platform-tools\adb.exe"
if exist "%ADB_PATH%" (
    set "ADB_CMD=%ADB_PATH%"
) else (
    adb version >nul 2>&1
    if %errorlevel% neq 0 (
        echo     ❌ ADB不可用，请选择"完整环境检查和设置"
        pause
        goto main_menu
    )
    set "ADB_CMD=adb"
)

echo     ✅ 环境检查通过，准备启动调试...
goto start_hook_debug

:start_hook_debug
cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                      🎯 Hook调试启动                          ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

echo     🎯 选择调试模式:
echo.
echo        1️⃣  启动应用并Hook (推荐)
echo        2️⃣  附加到运行中的应用
echo        3️⃣  使用反调试绕过模式
echo        4️⃣  返回主菜单
echo.
set /p debug_mode=     请选择模式 (1-4): 

if "%debug_mode%"=="4" goto main_menu
if "%debug_mode%"=="1" set "FRIDA_CMD=frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause"
if "%debug_mode%"=="2" set "FRIDA_CMD=frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js"
if "%debug_mode%"=="3" set "FRIDA_CMD=frida -U -f com.chinatelecom.bestpayclient -l bypass_detection.js -l bestpay_crypto_hook.js --no-pause"

if not defined FRIDA_CMD (
    echo     ❌ 无效选择
    pause
    goto start_hook_debug
)

cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                      📱 开始Hook调试                          ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.
echo     🔥 Hook脚本正在启动...
echo.
echo     📱 重要操作指南:
echo        1. 保持此窗口打开
echo        2. 在手机上操作翼支付应用
echo        3. 进入"权益商城"并刷新页面
echo        4. 观察此窗口的输出
echo        5. 看到包含AES密钥的输出时，立即复制保存!
echo.
echo     🎯 寻找类似输出:
echo        🔑 [AES_decrypt] 调用
echo           密钥: 1234567890abcdef...
echo           解密结果: {"activityId":"12345",...}
echo.
echo     ================================
echo.

%FRIDA_CMD%

echo.
echo     ================================
echo     🎉 Hook会话已结束
echo.
echo     📊 如果成功捕获到数据，请运行数据收集工具:
echo        python data_collector.py
echo.
pause
goto main_menu

:manual_install
cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                      🔧 手动安装组件                          ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

echo     🛠️  选择要安装的组件:
echo.
echo        1️⃣  安装/更新 Frida
echo        2️⃣  下载 ADB 工具
echo        3️⃣  安装 pycryptodome
echo        4️⃣  返回主菜单
echo.
set /p install_choice=     请选择 (1-4): 

if "%install_choice%"=="1" (
    echo.
    echo     📦 正在安装Frida...
    pip install --upgrade frida-tools
    echo     ✅ Frida安装完成
    pause
)
if "%install_choice%"=="2" (
    call :download_adb
    pause
)
if "%install_choice%"=="3" (
    echo.
    echo     📦 正在安装pycryptodome...
    pip install pycryptodome
    echo     ✅ pycryptodome安装完成
    pause
)
if "%install_choice%"=="4" goto main_menu

goto manual_install

:download_adb
echo.
echo     📦 正在下载ADB工具...
echo     💡 这可能需要几分钟时间，请耐心等待...
echo.

if not exist "android-tools" mkdir android-tools

python -c "
import urllib.request
import zipfile
import os

url = 'https://dl.google.com/android/repository/platform-tools-latest-windows.zip'
zip_path = 'android-tools/platform-tools.zip'

try:
    print('正在下载...')
    urllib.request.urlretrieve(url, zip_path)
    print('下载完成，正在解压...')
    
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall('android-tools')
    
    if os.path.exists('android-tools/platform-tools/adb.exe'):
        print('✅ ADB工具下载并解压成功')
    else:
        print('❌ 解压失败')
        
except Exception as e:
    print(f'❌ 下载失败: {e}')
    print('💡 请手动下载:')
    print('   1. 访问: https://developer.android.com/studio/releases/platform-tools')
    print('   2. 下载 SDK Platform-Tools for Windows')
    print('   3. 解压到 android-tools 文件夹')
"
goto :eof

:show_help
cls
echo.
echo     ╔══════════════════════════════════════════════════════════════╗
echo     ║                      📖 详细帮助文档                          ║
echo     ╚══════════════════════════════════════════════════════════════╝
echo.

echo     🎯 使用流程:
echo        1. 选择"完整环境检查和设置"进行初次配置
echo        2. 按照提示完成手机设置和应用安装
echo        3. 启动Hook调试并在手机上操作翼支付
echo        4. 观察输出并保存捕获的加密数据
echo        5. 使用数据收集工具分析结果
echo.
echo     📱 手机设置要求:
echo        - Android 7.0+ 系统
echo        - 已开启开发者选项和USB调试
echo        - 已安装翼支付应用并可正常使用
echo        - USB数据线连接到电脑
echo.
echo     🔧 环境要求:
echo        - Windows 10+ 系统
echo        - Python 3.7+ 已安装
echo        - 网络连接正常(用于下载组件)
echo.
echo     ❓ 常见问题:
echo        Q: 设备显示unauthorized怎么办?
echo        A: 在手机上点击"始终允许来自这台计算机"
echo.
echo        Q: Frida连接失败怎么办?
echo        A: 检查USB连接，重启adb服务，或尝试不同调试模式
echo.
echo        Q: 没有捕获到加密数据怎么办?
echo        A: 确保在权益商城页面触发网络请求，多尝试几次
echo.
echo     📞 技术支持:
echo        如遇到其他问题，请保存错误信息并寻求技术支持
echo.
pause
goto main_menu

:exit_program
cls
echo.
echo     👋 感谢使用翼支付一键调试助手!
echo.
echo     📋 记住关键步骤:
echo        1. 环境配置 → 2. 手机设置 → 3. Hook调试 → 4. 数据收集
echo.
echo     🎯 祝您调试顺利!
echo.
timeout /t 3 >nul
exit /b 0
