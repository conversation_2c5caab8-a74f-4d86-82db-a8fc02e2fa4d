
// 网络函数跟踪脚本
console.log("🌐 网络函数跟踪启动");

Java.perform(function() {
    // Hook OkHttp
    try {
        var RequestBody = Java.use("okhttp3.RequestBody");
        var originalCreate = RequestBody.create.overload('okhttp3.MediaType', 'java.lang.String');
        
        originalCreate.implementation = function(mediaType, content) {
            console.log("\n🌐 [OkHttp请求]");
            console.log("Content-Type: " + mediaType);
            
            if (content.includes('"data":') || content.includes('"key":') || content.includes('"sign":')) {
                console.log("🎯 发现加密请求!");
                console.log("请求内容: " + content);
            } else if (content.length < 500) {
                console.log("请求内容: " + content);
            } else {
                console.log("请求长度: " + content.length + " 字符");
            }
            
            return originalCreate.call(this, mediaType, content);
        };
        
        console.log("✅ OkHttp跟踪已设置");
    } catch(e) {
        console.log("❌ OkHttp跟踪失败: " + e);
    }
    
    // Hook HttpURLConnection
    try {
        var HttpURLConnection = Java.use("java.net.HttpURLConnection");
        var originalGetOutputStream = HttpURLConnection.getOutputStream;
        
        originalGetOutputStream.implementation = function() {
            var url = this.getURL().toString();
            console.log("\n🔗 [HttpURLConnection] " + url);
            
            return originalGetOutputStream.call(this);
        };
        
        console.log("✅ HttpURLConnection跟踪已设置");
    } catch(e) {
        console.log("❌ HttpURLConnection跟踪失败: " + e);
    }
});
