{"total_count": 130, "crypto_related_count": 12, "all_libraries": [{"path": "lib\\arm64-v8a\\libagora-rtc-sdk.so", "name": "libagora-rtc-sdk.so", "size": 23936816}, {"path": "lib\\arm64-v8a\\libWebViewCore_3.22.2.72.240709175818_7z_uc.so", "name": "libWebViewCore_3.22.2.72.240709175818_7z_uc.so", "size": 13999047}, {"path": "lib\\arm64-v8a\\libapp.so", "name": "libapp.so", "size": 9081856}, {"path": "lib\\arm64-v8a\\libflutter.so", "name": "libflutter.so", "size": 8484280}, {"path": "lib\\arm64-v8a\\libliteavsdk.so", "name": "libliteavsdk.so", "size": 7204160}, {"path": "lib\\arm64-v8a\\libhyphenate.so", "name": "libhyphenate.so", "size": 6831984}, {"path": "lib\\arm64-v8a\\libssid_silent_jni.so", "name": "libssid_silent_jni.so", "size": 6471800}, {"path": "lib\\arm64-v8a\\libagora-ffmpeg.so", "name": "libagora-ffmpeg.so", "size": 6173640}, {"path": "lib\\arm64-v8a\\libAPSE_8.0.0.so", "name": "libAPSE_8.0.0.so", "size": 4829016}, {"path": "lib\\arm64-v8a\\libyolov5.so", "name": "libyolov5.so", "size": 4594144}, {"path": "lib\\arm64-v8a\\libtxffmpeg.so", "name": "libtxffmpeg.so", "size": 4484160}, {"path": "lib\\arm64-v8a\\libBifrost.so", "name": "libBifrost.so", "size": 4455776}, {"path": "lib\\arm64-v8a\\libstidocr_stream.so", "name": "libstidocr_stream.so", "size": 4068656}, {"path": "lib\\arm64-v8a\\libbestpaysheild.so", "name": "libbestpaysheild.so", "size": 3461628}, {"path": "lib\\arm64-v8a\\libQPlayer.so", "name": "libQPlayer.so", "size": 3211152}, {"path": "lib\\arm64-v8a\\libjdcard.so", "name": "libjdcard.so", "size": 3146536}, {"path": "lib\\arm64-v8a\\libtraeimp-rtmp.so", "name": "libtraeimp-rtmp.so", "size": 2721000}, {"path": "lib\\arm64-v8a\\libh264encoder.so", "name": "libh264encoder.so", "size": 2707040}, {"path": "lib\\arm64-v8a\\libxnn.so", "name": "libxnn.so", "size": 2680336}, {"path": "lib\\arm64-v8a\\libopenssl.so", "name": "libopenssl.so", "size": 2444056}, {"path": "lib\\arm64-v8a\\libqcOpenSSL.so", "name": "libqcOpenSSL.so", "size": 2284200}, {"path": "lib\\arm64-v8a\\libRSSupport.so", "name": "libRSSupport.so", "size": 2184400}, {"path": "lib\\arm64-v8a\\libkyctoolkit.so", "name": "libkyctoolkit.so", "size": 2150360}, {"path": "lib\\arm64-v8a\\libcvenginelite.so", "name": "libcvenginelite.so", "size": 2051440}, {"path": "lib\\arm64-v8a\\libdatabase_sqlcrypto.so", "name": "libdatabase_sqlcrypto.so", "size": 2041040}, {"path": "lib\\arm64-v8a\\libantssm.so", "name": "libantssm.so", "size": 1925512}, {"path": "lib\\arm64-v8a\\libcqyh_adConfig.so", "name": "libcqyh_adConfig.so", "size": 1920480}, {"path": "lib\\arm64-v8a\\libkeyboardGuard.so", "name": "libkeyboardGuard.so", "size": 1617948}, {"path": "lib\\arm64-v8a\\libcipherdb.so", "name": "libcipherdb.so", "size": 1583472}, {"path": "lib\\arm64-v8a\\libttmplayer_lite.so", "name": "libttmplayer_lite.so", "size": 1502480}, {"path": "lib\\arm64-v8a\\libnllvm1624349491.so", "name": "libnllvm1624349491.so", "size": 1458296}, {"path": "lib\\arm64-v8a\\libYTLiveness.so", "name": "libYTLiveness.so", "size": 1457648}, {"path": "lib\\arm64-v8a\\libdecode1002235b60ba.so", "name": "libdecode1002235b60ba.so", "size": 1246704}, {"path": "lib\\arm64-v8a\\libquickjs.so", "name": "libquickjs.so", "size": 997336}, {"path": "lib\\arm64-v8a\\libMdSecurity.so", "name": "libMdSecurity.so", "size": 984044}, {"path": "lib\\arm64-v8a\\libxmcore.so", "name": "libxmcore.so", "size": 980368}, {"path": "lib\\arm64-v8a\\libc++_shared.so", "name": "libc++_shared.so", "size": 919888}, {"path": "assets\\ijm_lib\\arm64-v8a\\libexecmain.so", "name": "libexecmain.so", "size": 911284}, {"path": "assets\\ijm_lib\\x86_64\\libexecmain.so", "name": "libexecmain.so", "size": 907204}, {"path": "lib\\arm64-v8a\\libsgsecuritybodyso-5.5.61.so", "name": "libsgsecuritybodyso-5.5.61.so", "size": 895404}, {"path": "lib\\arm64-v8a\\libAPSE_J.so", "name": "libAPSE_J.so", "size": 892456}, {"path": "lib\\arm64-v8a\\libPglbizssdk_ml.so", "name": "libPglbizssdk_ml.so", "size": 882848}, {"path": "lib\\arm64-v8a\\libperf.so", "name": "libperf.so", "size": 850736}, {"path": "lib\\arm64-v8a\\liblivenessdetection_ka_v2.4.7.so", "name": "liblivenessdetection_ka_v2.4.7.so", "size": 788728}, {"path": "lib\\arm64-v8a\\libYTCommonLiveness.so", "name": "libYTCommonLiveness.so", "size": 760152}, {"path": "lib\\arm64-v8a\\libavmdl_lite.so", "name": "libavmdl_lite.so", "size": 747584}, {"path": "lib\\arm64-v8a\\libYTWBCommonLiveness.so", "name": "libYTWBCommonLiveness.so", "size": 694824}, {"path": "lib\\arm64-v8a\\libagora-fdkaac.so", "name": "libagora-fdkaac.so", "size": 694584}, {"path": "lib\\arm64-v8a\\libsgmain.so", "name": "libsgmain.so", "size": 674738}, {"path": "lib\\arm64-v8a\\libsgmiddletierso-5.5.53.so", "name": "libsgmiddletierso-5.5.53.so", "size": 658412}, {"path": "lib\\arm64-v8a\\libdex2oat10.so", "name": "libdex2oat10.so", "size": 635152}, {"path": "lib\\arm64-v8a\\libstlport_shared.so", "name": "libstlport_shared.so", "size": 587520}, {"path": "lib\\arm64-v8a\\libenvid-sdk.so", "name": "libenvid-sdk.so", "size": 571768}, {"path": "lib\\arm64-v8a\\libnetsecsdk-4.1.3.so", "name": "libnetsecsdk-4.1.3.so", "size": 563456}, {"path": "assets\\ijm_lib\\x86_64\\libexec.so", "name": "libexec.so", "size": 548252}, {"path": "lib\\arm64-v8a\\libjdt-idcard-core.so", "name": "libjdt-idcard-core.so", "size": 534752}, {"path": "assets\\ijm_lib\\arm64-v8a\\libexec.so", "name": "libexec.so", "size": 527612}, {"path": "lib\\arm64-v8a\\libcrashsdk.so", "name": "libcrashsdk.so", "size": 519304}, {"path": "lib\\arm64-v8a\\libbestpaysheilddispatch.so", "name": "libbestpaysheilddispatch.so", "size": 517204}, {"path": "lib\\arm64-v8a\\libagora-core.so", "name": "libagora-core.so", "size": 498800}, {"path": "lib\\arm64-v8a\\libCtaApiLib.so", "name": "libCtaApiLib.so", "size": 498176}, {"path": "lib\\arm64-v8a\\libmmkv.so", "name": "libmmkv.so", "size": 483120}, {"path": "lib\\arm64-v8a\\libCoreCpt.so", "name": "libCoreCpt.so", "size": 444196}, {"path": "lib\\arm64-v8a\\libturbojpeg.so", "name": "libturbojpeg.so", "size": 391376}, {"path": "assets\\libijmDataEncryption_arm64.so", "name": "libijmDataEncryption_arm64.so", "size": 371044}, {"path": "assets\\libijmDataEncryption_x86_64.so", "name": "libijmDataEncryption_x86_64.so", "size": 370708}, {"path": "lib\\arm64-v8a\\libTencentSM.so", "name": "libTencentSM.so", "size": 354880}, {"path": "lib\\arm64-v8a\\libtt_ugen_yoga.so", "name": "libtt_ugen_yoga.so", "size": 352560}, {"path": "lib\\arm64-v8a\\libsecsdk.so", "name": "libsecsdk.so", "size": 349040}, {"path": "lib\\arm64-v8a\\libturingmfa.so", "name": "libturingmfa.so", "size": 304512}, {"path": "lib\\arm64-v8a\\libpl_droidsonroids_gif.so", "name": "libpl_droidsonroids_gif.so", "size": 302112}, {"path": "lib\\arm64-v8a\\libjpeg.so", "name": "libjpeg.so", "size": 300824}, {"path": "lib\\arm64-v8a\\libdevInfo.so", "name": "libdevInfo.so", "size": 280632}, {"path": "lib\\arm64-v8a\\libzstd.so", "name": "libzstd.so", "size": 276328}, {"path": "lib\\arm64-v8a\\libweyuv.so", "name": "libweyuv.so", "size": 264056}, {"path": "lib\\arm64-v8a\\libyoga.so", "name": "libyoga.so", "size": 250088}, {"path": "lib\\arm64-v8a\\libpanglearmor.so", "name": "libpanglearmor.so", "size": 246968}, {"path": "lib\\arm64-v8a\\libquickjs-android.so", "name": "libquickjs-android.so", "size": 239672}, {"path": "lib\\arm64-v8a\\libsta.so", "name": "libsta.so", "size": 235680}, {"path": "lib\\arm64-v8a\\libBugly_Native_idasc.so", "name": "libBugly_Native_idasc.so", "size": 195200}, {"path": "lib\\arm64-v8a\\libagora-soundtouch.so", "name": "libagora-soundtouch.so", "size": 178016}, {"path": "lib\\arm64-v8a\\libentryexpro.so", "name": "libentryexpro.so", "size": 177624}, {"path": "lib\\arm64-v8a\\libfbjni.so", "name": "libfbjni.so", "size": 168560}, {"path": "lib\\arm64-v8a\\libjsengine-api.so", "name": "libjsengine-api.so", "size": 156112}, {"path": "lib\\arm64-v8a\\libijm-emulator.so", "name": "libijm-emulator.so", "size": 154364}, {"path": "lib\\arm64-v8a\\libandfix.so", "name": "libandfix.so", "size": 153656}, {"path": "lib\\arm64-v8a\\libandfix_x86.so", "name": "libandfix_x86.so", "size": 145872}, {"path": "lib\\arm64-v8a\\liby_bbc.so", "name": "liby_bbc.so", "size": 144596}, {"path": "lib\\arm64-v8a\\libsgnocaptchaso-5.5.8.so", "name": "libsgnocaptchaso-5.5.8.so", "size": 137140}, {"path": "lib\\arm64-v8a\\libv8worker-native.so", "name": "libv8worker-native.so", "size": 116848}, {"path": "lib\\arm64-v8a\\libtanx.so", "name": "libtanx.so", "size": 116800}, {"path": "lib\\arm64-v8a\\libAkSdk.so", "name": "libAkSdk.so", "size": 113448}, {"path": "lib\\arm64-v8a\\libsgmiscso-5.5.9.so", "name": "libsgmiscso-5.5.9.so", "size": 112524}, {"path": "lib\\arm64-v8a\\libjsengine-platform.so", "name": "libjsengine-platform.so", "size": 97904}, {"path": "lib\\arm64-v8a\\libsgcore.so", "name": "libsgcore.so", "size": 88264}, {"path": "lib\\arm64-v8a\\libAPMUOCPLIB.so", "name": "libAPMUOCPLIB.so", "size": 87896}, {"path": "lib\\arm64-v8a\\libpatcher.so", "name": "libpatcher.so", "size": 82552}, {"path": "lib\\arm64-v8a\\librsjni.so", "name": "librsjni.so", "size": 72000}, {"path": "lib\\arm64-v8a\\libhtsfx.so", "name": "libhtsfx.so", "size": 69472}, {"path": "lib\\arm64-v8a\\librsjni_androidx.so", "name": "librsjni_androidx.so", "size": 62168}, {"path": "lib\\arm64-v8a\\libCryptoOperAd.so", "name": "libCryptoOperAd.so", "size": 54212}, {"path": "lib\\arm64-v8a\\libsgsecuritybody.so", "name": "libsgsecuritybody.so", "size": 43980}, {"path": "lib\\arm64-v8a\\libwind.so", "name": "libwind.so", "size": 42888}, {"path": "lib\\arm64-v8a\\libmpaas_crypto.so", "name": "libmpaas_crypto.so", "size": 42760}, {"path": "lib\\arm64-v8a\\libutility.so", "name": "libutility.so", "size": 42536}, {"path": "lib\\arm64-v8a\\libxnnloader.so", "name": "libxnnloader.so", "size": 34792}, {"path": "lib\\arm64-v8a\\libsgmiddletier.so", "name": "libsgmiddletier.so", "size": 31114}, {"path": "lib\\arm64-v8a\\libdexpatch_x86.so", "name": "libdexpatch_x86.so", "size": 30720}, {"path": "lib\\arm64-v8a\\libdexpatch.so", "name": "libdexpatch.so", "size": 30400}, {"path": "lib\\arm64-v8a\\libimage_processing_util_jni.so", "name": "libimage_processing_util_jni.so", "size": 28440}, {"path": "lib\\arm64-v8a\\libjsengine-loadso.so", "name": "libjsengine-loadso.so", "size": 26616}, {"path": "lib\\arm64-v8a\\libpps-jni.so", "name": "libpps-jni.so", "size": 26512}, {"path": "lib\\arm64-v8a\\libweibosdkcore.so", "name": "libweibosdkcore.so", "size": 26136}, {"path": "lib\\arm64-v8a\\libIpStack.so", "name": "libIpStack.so", "size": 14248}, {"path": "lib\\arm64-v8a\\libstidocr_stream_jni.so", "name": "libstidocr_stream_jni.so", "size": 14176}, {"path": "lib\\arm64-v8a\\libzeus_direct_dex.so", "name": "libzeus_direct_dex.so", "size": 14160}, {"path": "lib\\arm64-v8a\\libsgnocaptcha.so", "name": "libsgnocaptcha.so", "size": 12942}, {"path": "lib\\arm64-v8a\\libtobEmbedEncrypt.so", "name": "libtobEmbedEncrypt.so", "size": 10504}, {"path": "lib\\arm64-v8a\\libpangleflipped.so", "name": "libpangleflipped.so", "size": 10064}, {"path": "lib\\arm64-v8a\\libAlipayBitmapNative.so", "name": "libAlipayBitmapNative.so", "size": 10008}, {"path": "lib\\arm64-v8a\\libimg-compress.so", "name": "libimg-compress.so", "size": 9992}, {"path": "lib\\arm64-v8a\\liblinearallocpatch.so", "name": "liblinearallocpatch.so", "size": 9920}, {"path": "lib\\arm64-v8a\\libweconvert.so", "name": "libweconvert.so", "size": 9832}, {"path": "lib\\arm64-v8a\\libsgmisc.so", "name": "libsgmisc.so", "size": 9544}, {"path": "lib\\arm64-v8a\\libapm_bitmaps.so", "name": "libapm_bitmaps.so", "size": 6032}, {"path": "lib\\arm64-v8a\\libap_bitmaps.so", "name": "libap_bitmaps.so", "size": 6032}, {"path": "lib\\arm64-v8a\\libmaparmor.so", "name": "libmaparmor.so", "size": 5824}, {"path": "lib\\arm64-v8a\\libmpaascpu.so", "name": "libmpaascpu.so", "size": 5824}, {"path": "assets\\ijm_lib\\x86_64\\libexecoat.so", "name": "libexecoat.so", "size": 5296}, {"path": "assets\\ijm_lib\\arm64-v8a\\libexecoat.so", "name": "libexecoat.so", "size": 5024}], "crypto_libraries": [{"path": "lib\\arm64-v8a\\libopenssl.so", "name": "libopenssl.so", "size": 2444056}, {"path": "lib\\arm64-v8a\\libqcOpenSSL.so", "name": "libqcOpenSSL.so", "size": 2284200}, {"path": "lib\\arm64-v8a\\libdatabase_sqlcrypto.so", "name": "libdatabase_sqlcrypto.so", "size": 2041040}, {"path": "lib\\arm64-v8a\\libMdSecurity.so", "name": "libMdSecurity.so", "size": 984044}, {"path": "lib\\arm64-v8a\\libsgsecuritybodyso-5.5.61.so", "name": "libsgsecuritybodyso-5.5.61.so", "size": 895404}, {"path": "assets\\libijmDataEncryption_arm64.so", "name": "libijmDataEncryption_arm64.so", "size": 371044}, {"path": "assets\\libijmDataEncryption_x86_64.so", "name": "libijmDataEncryption_x86_64.so", "size": 370708}, {"path": "lib\\arm64-v8a\\libCryptoOperAd.so", "name": "libCryptoOperAd.so", "size": 54212}, {"path": "lib\\arm64-v8a\\libsgsecuritybody.so", "name": "libsgsecuritybody.so", "size": 43980}, {"path": "lib\\arm64-v8a\\libmpaas_crypto.so", "name": "libmpaas_crypto.so", "size": 42760}, {"path": "lib\\arm64-v8a\\libtobEmbedEncrypt.so", "name": "libtobEmbedEncrypt.so", "size": 10504}, {"path": "lib\\arm64-v8a\\libmpaascpu.so", "name": "libmpaascpu.so", "size": 5824}]}