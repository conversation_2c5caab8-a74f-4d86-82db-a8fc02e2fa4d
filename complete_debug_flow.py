#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付完整调试流程脚本
自动化完成从环境检查到Hook调试的全部流程
"""

import subprocess
import time
import os
import sys

class CompleteDebugFlow:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        self.device_id = None
        self.frida_server_running = False
        
    def print_step(self, step, title):
        """打印步骤标题"""
        print(f"\n{'='*60}")
        print(f"🎯 第{step}步: {title}")
        print(f"{'='*60}")
    
    def run_command(self, cmd, capture_output=True, timeout=30):
        """执行命令"""
        try:
            if capture_output:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
                return result.returncode == 0, result.stdout, result.stderr
            else:
                result = subprocess.run(cmd, shell=True, timeout=timeout)
                return result.returncode == 0, "", ""
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def wait_for_user(self, message):
        """等待用户确认"""
        input(f"\n⏸️  {message} (按回车继续...)")
    
    def check_environment(self):
        """检查环境"""
        self.print_step(1, "环境检查")
        
        print("🔍 检查Python...")
        success, stdout, stderr = self.run_command("python --version")
        if success:
            print(f"   ✅ {stdout.strip()}")
        else:
            print("   ❌ Python未安装")
            return False
        
        print("🔍 检查Frida...")
        success, stdout, stderr = self.run_command("frida --version")
        if success:
            print(f"   ✅ Frida {stdout.strip()}")
        else:
            print("   ❌ Frida未安装")
            return False
        
        print("🔍 检查ADB...")
        if os.path.exists(self.adb_cmd):
            print("   ✅ ADB工具存在")
        else:
            print("   ❌ ADB工具不存在")
            return False
        
        return True
    
    def check_device_connection(self):
        """检查设备连接"""
        self.print_step(2, "设备连接检查")
        
        print("🔍 检查设备连接...")
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} devices")
        
        if not success:
            print("   ❌ ADB命令失败")
            return False
        
        lines = stdout.strip().split('\n')[1:]  # 跳过标题行
        devices = [line for line in lines if line.strip() and '\t' in line]
        
        if not devices:
            print("   ❌ 没有检测到设备")
            print("\n💡 请确保:")
            print("   - 手机已连接USB")
            print("   - 已开启USB调试")
            print("   - 已授权此计算机")
            return False
        
        for device in devices:
            device_id, status = device.split('\t')
            print(f"   📱 设备 {device_id}: {status}")
            
            if status == "device":
                self.device_id = device_id
                print("   ✅ 设备连接正常")
                return True
            elif status == "unauthorized":
                print("   ⚠️ 设备未授权，请在手机上点击'始终允许'")
                return False
        
        return False
    
    def check_target_app(self):
        """检查翼支付应用"""
        self.print_step(3, "检查翼支付应用")
        
        print("🔍 检查翼支付应用...")
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell pm list packages | findstr bestpay")
        
        if success and "com.chinatelecom.bestpayclient" in stdout:
            print("   ✅ 翼支付应用已安装")
            print(f"   📱 包名: com.chinatelecom.bestpayclient")
            return True
        else:
            print("   ❌ 翼支付应用未安装")
            print("\n💡 请在手机上安装翼支付应用:")
            print("   - 从应用商店搜索'翼支付'")
            print("   - 或从官网下载APK安装")
            return False
    
    def setup_frida_server(self):
        """设置Frida Server"""
        self.print_step(4, "设置Frida Server")
        
        print("🔍 检查Frida Server...")
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ls /data/local/tmp/frida-server")
        
        if not success:
            print("   ❌ Frida Server不存在")
            print("   💡 请确保已按照之前的步骤推送了frida-server到设备")
            return False
        
        print("   ✅ Frida Server文件存在")
        
        # 清理可能存在的进程
        print("🧹 清理旧的Frida进程...")
        self.run_command(f"{self.adb_cmd} shell su -c 'pkill -f frida-server'")
        time.sleep(2)
        
        # 重启ADB服务
        print("🔄 重启ADB服务...")
        self.run_command(f"{self.adb_cmd} kill-server")
        time.sleep(1)
        self.run_command(f"{self.adb_cmd} start-server")
        time.sleep(2)
        
        # 重新设置端口转发
        print("🔧 设置端口转发...")
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} forward tcp:27042 tcp:27042")
        if not success:
            print("   ❌ 端口转发设置失败")
            return False
        
        # 启动Frida Server
        print("🚀 启动Frida Server...")
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c '/data/local/tmp/frida-server &'", timeout=5)
        
        # 等待启动
        time.sleep(3)
        
        return True
    
    def test_frida_connection(self):
        """测试Frida连接"""
        self.print_step(5, "测试Frida连接")
        
        print("🔍 测试Frida连接...")
        
        for attempt in range(3):
            print(f"   尝试 {attempt + 1}/3...")
            success, stdout, stderr = self.run_command("frida-ps -U", timeout=10)
            
            if success and stdout.strip():
                lines = stdout.strip().split('\n')
                process_count = len(lines) - 1  # 减去标题行
                print(f"   ✅ Frida连接成功！检测到 {process_count} 个进程")
                self.frida_server_running = True
                
                # 检查翼支付是否在运行
                if "bestpay" in stdout.lower() or "chinatelecom" in stdout.lower():
                    print("   🎯 检测到翼支付进程正在运行")
                
                return True
            else:
                print(f"   ❌ 连接失败: {stderr}")
                if attempt < 2:
                    print("   🔄 重新启动Frida Server...")
                    self.run_command(f"{self.adb_cmd} shell su -c 'pkill -f frida-server'")
                    time.sleep(2)
                    self.run_command(f"{self.adb_cmd} shell su -c '/data/local/tmp/frida-server &'")
                    time.sleep(3)
        
        print("   ❌ Frida连接失败")
        return False
    
    def start_hook_debug(self):
        """开始Hook调试"""
        self.print_step(6, "开始Hook调试")
        
        if not self.frida_server_running:
            print("   ❌ Frida Server未运行，无法开始调试")
            return False
        
        print("🎯 选择调试模式:")
        print("   1. 启动应用并Hook (推荐)")
        print("   2. 附加到运行中的应用")
        print("   3. 使用反调试绕过模式")
        
        while True:
            choice = input("\n请选择模式 (1-3): ").strip()
            if choice in ['1', '2', '3']:
                break
            print("请输入 1、2 或 3")
        
        # 构建Frida命令
        if choice == '1':
            frida_cmd = "frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause"
            print("\n🚀 模式1: 启动应用并Hook")
        elif choice == '2':
            frida_cmd = "frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js"
            print("\n📎 模式2: 附加到运行中的应用")
            print("💡 请先在手机上打开翼支付应用")
            self.wait_for_user("翼支付应用已打开")
        else:
            frida_cmd = "frida -U -f com.chinatelecom.bestpayclient -l bypass_detection.js -l bestpay_crypto_hook.js --no-pause"
            print("\n🛡️ 模式3: 反调试绕过模式")
        
        print(f"\n🔥 执行命令: {frida_cmd}")
        print("\n" + "="*60)
        print("📱 重要操作指南:")
        print("   1. 保持此窗口打开")
        print("   2. 在手机上操作翼支付应用")
        print("   3. 进入'权益商城'并刷新页面")
        print("   4. 观察此窗口的输出")
        print("   5. 看到包含AES密钥的输出时，立即复制保存!")
        print("\n🎯 寻找类似输出:")
        print("   🔑 [AES_decrypt] 调用")
        print("      密钥: 1234567890abcdef...")
        print("      解密结果: {\"activityId\":\"12345\",...}")
        print("="*60)
        
        self.wait_for_user("准备开始Hook调试")
        
        # 启动Frida Hook
        print(f"\n🚀 启动Hook调试...")
        try:
            subprocess.run(frida_cmd, shell=True)
        except KeyboardInterrupt:
            print("\n⏹️ Hook调试已停止")
        
        print("\n" + "="*60)
        print("🎉 Hook会话已结束")
        print("\n📊 如果成功捕获到数据，请运行数据收集工具:")
        print("   python data_collector.py")
        print("="*60)
        
        return True
    
    def run_complete_flow(self):
        """运行完整流程"""
        print("🚀 翼支付完整调试流程")
        print("🎯 自动化完成从环境检查到Hook调试的全部流程")
        print("="*60)
        
        # 步骤1: 环境检查
        if not self.check_environment():
            print("\n❌ 环境检查失败，请先解决环境问题")
            return False
        
        # 步骤2: 设备连接检查
        if not self.check_device_connection():
            print("\n❌ 设备连接失败，请检查设备连接")
            return False
        
        # 步骤3: 检查翼支付应用
        if not self.check_target_app():
            print("\n❌ 翼支付应用检查失败，请先安装应用")
            return False
        
        # 步骤4: 设置Frida Server
        if not self.setup_frida_server():
            print("\n❌ Frida Server设置失败")
            return False
        
        # 步骤5: 测试Frida连接
        if not self.test_frida_connection():
            print("\n❌ Frida连接测试失败")
            return False
        
        # 步骤6: 开始Hook调试
        self.start_hook_debug()
        
        print("\n🎉 完整流程执行完成!")
        return True

def main():
    flow = CompleteDebugFlow()
    
    try:
        flow.run_complete_flow()
    except KeyboardInterrupt:
        print("\n\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
    
    print("\n👋 感谢使用翼支付调试工具!")

if __name__ == "__main__":
    main()
