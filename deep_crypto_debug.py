#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付data字段深度解密调试工具
尝试多种方法分析和解密data字段内容
"""

import base64
import json
import struct
import hashlib
import hmac
import binascii
import os

try:
    from Crypto.Cipher import AES
    from Crypto.Util.Padding import unpad, pad
    from Crypto.Random import get_random_bytes
    CRYPTO_AVAILABLE = True
except ImportError:
    print("警告: pycryptodome未安装，将跳过AES解密测试")
    CRYPTO_AVAILABLE = False

class DataDecryptionDebugger:
    def __init__(self):
        # 示例数据
        self.encrypted_data = "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA27VA51QMMQDe/RiVRdj0+UWr1g6Rd28afJX1JNy3SnfyB28swubBraklZQD94z0x5DutIu+yHYLHoSpE7bVt/yvA0zueZIhMeNntIfdduuzDcBL0CLMjU5fdqjWRK14rh9mDQXi2uN5kTTt2jlJ7qHw0c650VeaVRLy5oG/VbFRUKMwgW+NslTTZ9C/ijfOWcznv5nmBnZu1FrQAi2YaIDJ//rGt8DjB7mHaTaw8Kn2kMolMSrxIje8O5F8rDQQCkub9IHiGZ6ivT2d/PsUAsEy2TmjuMqMaq8oifhihr1WRK8v3sXDI1EB4dLGjsv1GRomMNys0nPgwmHB4iKYvJGS3hYFzWLUhyjDiIj+tZNSLp2T1GjJd3AKBdg3d3px1fZXte9g7QeXG1VhvRBuYAU6lIiSt44I/7M9kKoJ//31lhiv3vlcHsi3DNdHNTAMPbRxfHXlcAj0b4ObmQJULFcL+U8T8Es0TZfqgoNnEKeRA"
        self.encrypted_key = "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOboibNuxG95/46P8rizN4R27a/FXmZEZygYkNmAC9U5PSc5ETxa5PTnhQAf7qpwg7uj/yhKPPK7Ejlo4AgoG9LjuVS8t4Hoy41i4n02OGwnmk="
        self.sign = "751C0392CAB814248DBB7A4B1BCE34CD"
        
        # 解码数据
        self.data_bytes = self.safe_base64_decode(self.encrypted_data)
        self.key_bytes = self.safe_base64_decode(self.encrypted_key)
        
        print(f"🔍 开始深度调试data字段解密...")
        print(f"Data长度: {len(self.data_bytes)} 字节")
        print(f"Key长度: {len(self.key_bytes)} 字节")
    
    def safe_base64_decode(self, data):
        """安全的Base64解码"""
        try:
            missing_padding = len(data) % 4
            if missing_padding:
                data += '=' * (4 - missing_padding)
            return base64.b64decode(data)
        except Exception as e:
            print(f"Base64解码错误: {e}")
            return b""
    
    def analyze_data_structure(self):
        """分析data字段的内部结构"""
        print("\n📊 Data字段结构分析:")
        
        data = self.data_bytes
        print(f"总长度: {len(data)} 字节")
        
        # 分析可能的结构
        print("\n🔍 可能的结构模式:")
        
        # 模式1: IV(16) + 加密数据
        if len(data) >= 16:
            possible_iv = data[:16]
            encrypted_payload = data[16:]
            print(f"模式1 - IV + 数据:")
            print(f"  可能的IV: {possible_iv.hex()}")
            print(f"  加密数据长度: {len(encrypted_payload)} 字节")
            print(f"  是否16字节对齐: {len(encrypted_payload) % 16 == 0}")
        
        # 模式2: 长度(4) + IV(16) + 数据
        if len(data) >= 20:
            possible_length = struct.unpack('>I', data[:4])[0]
            possible_iv = data[4:20]
            encrypted_payload = data[20:]
            print(f"模式2 - 长度 + IV + 数据:")
            print(f"  可能的长度字段: {possible_length}")
            print(f"  可能的IV: {possible_iv.hex()}")
            print(f"  加密数据长度: {len(encrypted_payload)} 字节")
        
        # 模式3: 魔数(4) + IV(16) + 数据
        if len(data) >= 20:
            magic = data[:4]
            possible_iv = data[4:20]
            encrypted_payload = data[20:]
            print(f"模式3 - 魔数 + IV + 数据:")
            print(f"  魔数: {magic.hex()} ({magic})")
            print(f"  可能的IV: {possible_iv.hex()}")
            print(f"  加密数据长度: {len(encrypted_payload)} 字节")
        
        # 分析数据的熵和模式
        self.analyze_entropy(data)
    
    def analyze_entropy(self, data):
        """分析数据的熵和随机性"""
        print(f"\n📈 数据熵分析:")
        
        # 计算字节频率
        byte_freq = {}
        for byte in data:
            byte_freq[byte] = byte_freq.get(byte, 0) + 1
        
        # 计算熵
        entropy = 0
        for count in byte_freq.values():
            p = count / len(data)
            if p > 0:
                entropy -= p * (p.bit_length() - 1)
        
        print(f"  数据熵: {entropy:.2f} (最大8.0)")
        print(f"  唯一字节数: {len(byte_freq)}/256")
        print(f"  随机性评估: {'高' if entropy > 7.5 else '中' if entropy > 6.0 else '低'}")
        
        # 检查重复模式
        self.check_patterns(data)
    
    def check_patterns(self, data):
        """检查数据中的重复模式"""
        print(f"\n🔍 模式检查:")
        
        # 检查16字节块的重复
        blocks_16 = [data[i:i+16] for i in range(0, len(data)-15, 16)]
        unique_blocks_16 = set(blocks_16)
        print(f"  16字节块: {len(blocks_16)}个, 唯一: {len(unique_blocks_16)}个")
        
        if len(unique_blocks_16) < len(blocks_16):
            print(f"  ⚠️ 发现重复的16字节块!")
        
        # 检查8字节块的重复
        blocks_8 = [data[i:i+8] for i in range(0, len(data)-7, 8)]
        unique_blocks_8 = set(blocks_8)
        print(f"  8字节块: {len(blocks_8)}个, 唯一: {len(unique_blocks_8)}个")
    
    def try_common_keys(self):
        """尝试常见的密钥模式"""
        print(f"\n🔑 尝试常见密钥模式:")
        
        # 常见的密钥模式
        common_keys = [
            b"bestpay123456789" + b"0" * 16,  # 32字节
            b"chinatelecom1234" + b"0" * 16,  # 32字节
            b"mpaas123456789012" + b"0" * 16,  # 32字节
            hashlib.md5(b"bestpay").digest() + hashlib.md5(b"bestpay").digest(),  # MD5双重
            hashlib.sha256(b"bestpay").digest(),  # SHA256
            hashlib.sha256(b"chinatelecom").digest(),  # SHA256
            b"1234567890123456" * 2,  # 简单重复
            b"\x00" * 32,  # 全零
            b"\xFF" * 32,  # 全一
        ]
        
        for i, key in enumerate(common_keys):
            print(f"  尝试密钥 {i+1}: {key[:16].hex()}...")
            self.try_decrypt_with_key(key, f"common_key_{i+1}")
    
    def try_decrypt_with_key(self, key, key_name):
        """使用指定密钥尝试解密"""
        if not CRYPTO_AVAILABLE:
            print(f"    跳过{key_name}: 加密库不可用")
            return

        try:
            data = self.data_bytes

            # 尝试不同的结构
            structures = [
                ("IV+Data", data[:16], data[16:]),
                ("Magic+IV+Data", data[4:20], data[20:]) if len(data) >= 20 else (None, None, None),
                ("NoIV", b"\x00" * 16, data),
            ]

            for struct_name, iv, encrypted_data in structures:
                if iv is None:
                    continue

                # 尝试不同的AES模式
                modes = [
                    ("CBC", AES.MODE_CBC),
                    ("ECB", AES.MODE_ECB),
                    ("CFB", AES.MODE_CFB),
                    ("OFB", AES.MODE_OFB),
                ]

                for mode_name, mode in modes:
                    try:
                        if mode == AES.MODE_ECB:
                            cipher = AES.new(key[:32], mode)
                        else:
                            cipher = AES.new(key[:32], mode, iv)

                        decrypted = cipher.decrypt(encrypted_data)

                        # 尝试去除填充
                        try:
                            unpadded = unpad(decrypted, 16)
                            self.analyze_decrypted_data(unpadded, f"{key_name}_{struct_name}_{mode_name}_padded")
                        except:
                            pass

                        # 不去除填充
                        self.analyze_decrypted_data(decrypted, f"{key_name}_{struct_name}_{mode_name}_raw")

                    except Exception as e:
                        continue

        except Exception as e:
            print(f"    解密失败: {e}")
    
    def analyze_decrypted_data(self, data, method_name):
        """分析解密后的数据"""
        try:
            # 检查是否是有效的文本
            if self.is_likely_text(data):
                print(f"  ✅ {method_name}: 可能是文本数据")
                print(f"    前64字节: {data[:64]}")
                
                # 尝试解析为JSON
                try:
                    text = data.decode('utf-8')
                    json_data = json.loads(text)
                    print(f"    🎯 成功解析为JSON!")
                    print(f"    JSON内容: {json.dumps(json_data, ensure_ascii=False, indent=2)}")
                    return True
                except:
                    pass
                
                # 尝试其他编码
                for encoding in ['utf-8', 'gbk', 'gb2312']:
                    try:
                        text = data.decode(encoding)
                        if len(text) > 10 and text.isprintable():
                            print(f"    📝 {encoding}解码: {text[:100]}...")
                    except:
                        continue
            
            # 检查是否是结构化数据
            elif self.is_likely_structured(data):
                print(f"  🔍 {method_name}: 可能是结构化数据")
                self.analyze_binary_structure(data)
                
        except Exception as e:
            pass
        
        return False
    
    def is_likely_text(self, data):
        """判断数据是否可能是文本"""
        try:
            # 检查是否包含常见的文本字符
            text_chars = sum(1 for b in data if 32 <= b <= 126 or b in [9, 10, 13])
            ratio = text_chars / len(data) if len(data) > 0 else 0
            
            # 检查是否包含JSON特征字符
            json_chars = b'{}[]":,'
            has_json_chars = any(c in data for c in json_chars)
            
            return ratio > 0.7 or has_json_chars
        except:
            return False
    
    def is_likely_structured(self, data):
        """判断数据是否可能是结构化数据"""
        if len(data) < 8:
            return False
        
        # 检查是否有明显的结构特征
        # 例如：长度字段、魔数等
        first_4_bytes = struct.unpack('>I', data[:4])[0]
        
        # 检查第一个4字节是否可能是长度
        if 0 < first_4_bytes < len(data) * 2:
            return True
        
        # 检查是否有重复的模式
        if len(set(data[:16])) < 8:  # 前16字节重复度高
            return True
        
        return False
    
    def analyze_binary_structure(self, data):
        """分析二进制结构"""
        print(f"    二进制结构分析:")
        print(f"    长度: {len(data)} 字节")
        print(f"    前16字节: {data[:16].hex()}")
        
        # 尝试解析为不同的结构
        if len(data) >= 4:
            length_be = struct.unpack('>I', data[:4])[0]
            length_le = struct.unpack('<I', data[:4])[0]
            print(f"    可能的长度字段(大端): {length_be}")
            print(f"    可能的长度字段(小端): {length_le}")
    
    def try_key_derivation(self):
        """尝试密钥派生"""
        print(f"\n🔐 尝试密钥派生:")
        
        # 从固定字符串派生密钥
        base_strings = [
            "bestpay",
            "chinatelecom", 
            "mpaas",
            "13341293407",  # productNo
            "yzf_app",      # fromChannelId
            "C005",         # encyType
            self.sign,      # sign字段
        ]
        
        for base_str in base_strings:
            # 不同的派生方法
            keys = [
                hashlib.md5(base_str.encode()).digest() * 2,  # MD5 * 2
                hashlib.sha256(base_str.encode()).digest(),   # SHA256
                hashlib.sha1(base_str.encode()).digest() + hashlib.sha1(base_str.encode()).digest()[:12],  # SHA1扩展
            ]
            
            for i, key in enumerate(keys):
                print(f"  尝试从'{base_str}'派生的密钥{i+1}")
                self.try_decrypt_with_key(key, f"derived_{base_str}_{i+1}")
    
    def try_xor_analysis(self):
        """尝试XOR分析"""
        print(f"\n⚡ XOR分析:")
        
        data = self.data_bytes
        
        # 尝试与常见字符串XOR
        common_plaintexts = [
            b'{"',  # JSON开头
            b'<?xml',  # XML开头
            b'HTTP',   # HTTP
            b'\x00\x00\x00',  # 空字节
        ]
        
        for plaintext in common_plaintexts:
            if len(data) >= len(plaintext):
                # 计算可能的XOR密钥
                xor_key = bytes(a ^ b for a, b in zip(data[:len(plaintext)], plaintext))
                print(f"  如果开头是 {plaintext}, XOR密钥可能是: {xor_key.hex()}")
                
                # 尝试用这个密钥解密更多数据
                if len(xor_key) > 0:
                    extended_key = (xor_key * (len(data) // len(xor_key) + 1))[:len(data)]
                    xor_result = bytes(a ^ b for a, b in zip(data, extended_key))
                    
                    if self.is_likely_text(xor_result):
                        print(f"    ✅ XOR结果看起来像文本: {xor_result[:64]}")
    
    def run_debug(self):
        """运行完整的调试流程"""
        self.analyze_data_structure()
        self.try_common_keys()
        self.try_key_derivation()
        self.try_xor_analysis()
        
        print(f"\n" + "="*60)
        print(f"🔍 调试完成! 如果没有找到明显的解密结果，可能需要:")
        print(f"1. 获取真实的AES密钥 (通过逆向key字段)")
        print(f"2. 分析SO库中的加密实现")
        print(f"3. Hook运行时的加密/解密函数")
        print(f"4. 分析更多的请求样本")
        print(f"="*60)

def main():
    debugger = DataDecryptionDebugger()
    debugger.run_debug()

if __name__ == "__main__":
    main()
