# SELinux宽容模式设置指南

## 🎯 目标
将SELinux从强制模式(Enforcing)设置为宽容模式(Permissive)，以便进行Frida Hook调试。

## ⚠️ 重要警告
- **安全风险**: 宽容模式会降低系统安全性
- **临时性**: 建议仅在调试时使用
- **重启恢复**: 临时设置在重启后会恢复为强制模式

## 🔍 检查当前SELinux状态

### 方法1: 使用ADB命令
```bash
adb shell getenforce
```

**可能的输出:**
- `Enforcing` - 强制模式 (需要修改)
- `Permissive` - 宽容模式 (已经是目标状态)
- `Disabled` - 禁用状态 (无需修改)

### 方法2: 在设备上检查
```bash
adb shell
su
getenforce
```

## 🔧 设置SELinux宽容模式

### 方法1: 临时设置 (推荐)

#### 使用ADB命令
```bash
# 进入设备shell
adb shell

# 获取root权限
su

# 设置宽容模式
setenforce 0

# 验证设置
getenforce
```

#### 一键命令
```bash
adb shell su -c "setenforce 0"
```

### 方法2: 使用自动化脚本
```bash
# 运行我们提供的自动化脚本
python selinux_permissive_setter.py
```

### 方法3: 使用Magisk (永久设置)

#### 如果已安装Magisk
```bash
# 检查Magisk是否可用
adb shell which magisk

# 使用Magisk设置
adb shell su -c "magisk --selinux permissive"
```

#### 使用Magisk模块
1. 下载 "SELinux Permissive" 模块
2. 通过Magisk Manager安装
3. 重启设备

### 方法4: 修改系统属性
```bash
# 设置系统属性
adb shell su -c "setprop ro.boot.selinux permissive"

# 或者
adb shell su -c "setprop persist.security.selinux 0"
```

## 🧪 验证设置是否成功

### 检查SELinux状态
```bash
adb shell getenforce
```
**期望输出**: `Permissive`

### 测试Frida连接
```bash
# 检查Frida是否能正常连接
frida-ps -U

# 测试简单Hook
frida -U com.chinatelecom.bestpayclient -l selinux_test.js
```

## 🔄 恢复强制模式

### 临时恢复
```bash
adb shell su -c "setenforce 1"
```

### 重启恢复
```bash
adb reboot
```

## 🛠️ 故障排除

### 问题1: 权限被拒绝
```
Permission denied
```

**解决方案:**
- 确保设备已Root
- 检查su权限是否正确授权
- 尝试使用Magisk SU

### 问题2: 命令不存在
```
setenforce: not found
```

**解决方案:**
- 设备可能不支持SELinux
- 尝试使用系统属性方法
- 检查设备是否为定制ROM

### 问题3: 设置后仍然是强制模式
```
getenforce 仍然显示 Enforcing
```

**解决方案:**
- 等待几秒后重新检查
- 尝试使用Magisk方法
- 检查是否有其他安全策略阻止

### 问题4: Hook仍然失败
即使SELinux设置为宽容模式，Hook仍然失败

**可能原因:**
- 应用有其他反调试保护
- 需要绕过SSL Pinning
- 应用使用了Native层保护

**解决方案:**
- 使用Charles代理抓包
- 在模拟器环境中调试
- 使用Xposed框架

## 📱 不同设备的特殊情况

### 小米设备 (MIUI)
```bash
# MIUI可能需要额外步骤
adb shell su -c "setprop persist.security.selinux 0"
adb reboot
```

### 华为设备 (EMUI)
```bash
# 华为设备可能需要解锁Bootloader
# 某些版本可能完全禁用了setenforce命令
```

### 三星设备 (One UI)
```bash
# 三星设备通常支持标准方法
adb shell su -c "setenforce 0"
```

### 原生Android
```bash
# 原生Android通常支持所有标准方法
adb shell su -c "setenforce 0"
```

## 🎯 针对翼支付调试的建议

### 设置完SELinux后的步骤
1. **验证SELinux状态**
   ```bash
   adb shell getenforce
   ```

2. **重启Frida Server**
   ```bash
   adb shell su -c "pkill frida-server"
   adb shell su -c "cd /data/local/tmp && ./frida-server &"
   ```

3. **测试Hook功能**
   ```bash
   python simple_hook_analyzer.py
   ```

4. **如果Hook成功，运行增强脚本**
   ```bash
   frida -U com.chinatelecom.bestpayclient -l debug_hook_with_logs.js
   ```

### 如果仍然失败
- 翼支付可能有多层保护
- 建议使用Charles代理抓包
- 或在模拟器环境中调试

## 💡 最佳实践

### 调试时
1. 设置SELinux为宽容模式
2. 进行必要的调试工作
3. 完成后立即恢复强制模式

### 安全考虑
- 不要在生产环境长期使用宽容模式
- 调试完成后及时恢复安全设置
- 定期检查系统安全状态

### 备用方案
如果SELinux设置失败或Hook仍然不工作:
1. **Charles代理抓包** (最推荐)
2. **静态分析APK**
3. **网络层抓包**
4. **模拟器环境调试**

---

**总结**: SELinux宽容模式设置可能会解决Hook问题，但翼支付还有其他保护机制。建议优先尝试Charles代理抓包方案。
