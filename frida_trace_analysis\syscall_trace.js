
// 系统调用跟踪脚本
console.log("🔍 系统调用跟踪启动");

// 跟踪关键系统调用
var syscalls = [
    "open", "openat", "read", "write", "close",
    "connect", "sendto", "recvfrom", "socket",
    "ptrace", "kill", "access", "stat", "readlink",
    "mmap", "mprotect", "munmap"
];

syscalls.forEach(function(syscall) {
    try {
        var funcPtr = Module.findExportByName(null, syscall);
        if (funcPtr) {
            Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                    console.log("[" + syscall + "] 调用");
                    
                    // 特殊处理某些系统调用
                    if (syscall === "open" || syscall === "openat") {
                        var path = syscall === "open" ? args[0] : args[1];
                        var pathStr = path.readCString();
                        if (pathStr) {
                            console.log("  路径: " + pathStr);
                        }
                    }
                },
                onLeave: function(retval) {
                    // 记录返回值
                }
            });
            console.log("✅ " + syscall + " 跟踪已设置");
        }
    } catch(e) {
        console.log("❌ " + syscall + " 跟踪失败: " + e);
    }
});
