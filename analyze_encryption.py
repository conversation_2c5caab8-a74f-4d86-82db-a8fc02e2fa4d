#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付API加密分析工具
分析 https://mapi-h5.bestpay.com.cn/gapi/equitymall/client/Activity/queryActivityInfo 的加密机制
"""

import base64
import json
import re
from Crypto.PublicKey import RSA, ECC
from Crypto.Cipher import AES, PKCS1_v1_5
from Crypto.Hash import MD5, SHA256
from Crypto.Signature import DSS
import binascii

class BestPayCryptoAnalyzer:
    def __init__(self):
        # 从mpaas_netconfig.properties中提取的公钥
        self.public_key_pem = """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4RAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----END PUBLIC KEY-----"""
        
        # 示例请求数据
        self.sample_request = {
            "data": "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA27VA51QMMQDe/RiVRdj0+UWr1g6Rd28afJX1JNy3SnfyB28swubBraklZQD94z0x5DutIu+yHYLHoSpE7bVt/yvA0zueZIhMeNntIfdduuzDcBL0CLMjU5fdqjWRK14rh9mDQXi2uN5kTTt2jlJ7qHw0c650VeaVRLy5oG/VbFRUKMwgW+NslTTZ9C/ijfOWcznv5nmBnZu1FrQAi2YaIDJ//rGt8DjB7mHaTaw8Kn2kMolMSrxIje8O5F8rDQQCkub9IHiGZ6ivT2d/PsUAsEy2TmjuMqMaq8oifhihr1WRK8v3sXDI1EB4dLGjsv1GRomMNys0nPgwmHB4iKYvJGS3hYFzWLUhyjDiIj+tZNSLp2T1GjJd3AKBdg3d3px1fZXte9g7QeXG1VhvRBuYAU6lIiSt44I/7M9kKoJ//31lhiv3vlcHsi3DNdHNTAMPbRxfHXlcAj0b4ObmQJULFcL+U8T8Es0TZfqgoNnEKeRA",
            "key": "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOboibNuxG95/46P8rizN4R27a/FXmZEZygYkNmAC9U5PSc5ETxa5PTnhQAf7qpwg7uj/yhKPPK7Ejlo4AgoG9LjuVS8t4Hoy41i4n02OGwnmk=",
            "sign": "751C0392CAB814248DBB7A4B1BCE34CD",
            "productNo": "13341293407",
            "encyType": "C005",
            "fromChannelId": "yzf_app"
        }
    
    def safe_base64_decode(self, data):
        """安全的Base64解码"""
        try:
            # 添加必要的填充
            missing_padding = len(data) % 4
            if missing_padding:
                data += '=' * (4 - missing_padding)
            return base64.b64decode(data)
        except Exception as e:
            print(f"Base64解码错误: {e}")
            return b""

    def analyze_encryption_structure(self):
        """分析加密结构"""
        print("🔐 翼支付API加密结构分析")
        print("=" * 60)

        print("\n📊 请求结构分析:")
        print(f"   API地址: https://mapi-h5.bestpay.com.cn/gapi/equitymall/client/Activity/queryActivityInfo")
        print(f"   加密类型: {self.sample_request['encyType']} (C005)")
        print(f"   产品编号: {self.sample_request['productNo']}")
        print(f"   渠道标识: {self.sample_request['fromChannelId']}")

        print("\n🔑 加密字段分析:")

        # 分析data字段
        data_b64 = self.sample_request['data']
        data_bytes = self.safe_base64_decode(data_b64)
        print(f"   data字段:")
        print(f"     - Base64长度: {len(data_b64)} 字符")
        print(f"     - 解码后长度: {len(data_bytes)} 字节")
        print(f"     - 可能的加密算法: AES-256-CBC/GCM")

        # 分析key字段
        key_b64 = self.sample_request['key']
        key_bytes = self.safe_base64_decode(key_b64)
        print(f"   key字段:")
        print(f"     - Base64长度: {len(key_b64)} 字符")
        print(f"     - 解码后长度: {len(key_bytes)} 字节")
        print(f"     - 可能用途: RSA/ECC加密的AES密钥")

        # 分析sign字段
        sign = self.sample_request['sign']
        print(f"   sign字段:")
        print(f"     - 长度: {len(sign)} 字符")
        print(f"     - 格式: 十六进制字符串")
        print(f"     - 可能算法: MD5/SHA256签名")
    
    def analyze_public_key(self):
        """分析公钥"""
        print("\n🔑 公钥分析:")
        
        try:
            # 尝试解析为ECC公钥（基于OID判断）
            if "YHKoZIzj0CAQYIKoEcz1UBgi0" in self.public_key_pem:
                print("   公钥类型: ECC (椭圆曲线)")
                print("   曲线类型: SM2 (国密算法)")
                print("   密钥长度: 256位")
                print("   用途: 用于加密AES密钥")
            
            # 解析公钥内容
            key_data = base64.b64decode(self.public_key_pem.replace("-----BEGIN PUBLIC KEY-----", "").replace("-----END PUBLIC KEY-----", "").replace("\n", ""))
            print(f"   公钥数据长度: {len(key_data)} 字节")
            print(f"   公钥十六进制: {key_data.hex()[:64]}...")
            
        except Exception as e:
            print(f"   公钥解析错误: {e}")
    
    def analyze_encryption_flow(self):
        """分析加密流程"""
        print("\n🔄 推测的加密流程:")
        
        print("   1. 客户端生成随机AES密钥 (256位)")
        print("   2. 使用AES密钥加密原始请求数据 -> data字段")
        print("   3. 使用服务器公钥(SM2/ECC)加密AES密钥 -> key字段")
        print("   4. 计算请求参数的签名(MD5/SHA256) -> sign字段")
        print("   5. 添加固定参数(productNo, encyType, fromChannelId)")
        print("   6. 发送加密请求到服务器")
        
        print("\n🔓 推测的解密流程:")
        print("   1. 服务器使用私钥解密key字段获得AES密钥")
        print("   2. 使用AES密钥解密data字段获得原始数据")
        print("   3. 验证sign字段确保数据完整性")
        print("   4. 处理业务逻辑并返回结果")
    
    def analyze_encryption_type(self):
        """分析加密类型C005"""
        print("\n🏷️ 加密类型C005分析:")
        
        print("   C005可能含义:")
        print("   - C: Crypto/Cipher (加密)")
        print("   - 005: 版本号或算法标识")
        print("   - 可能对应: AES-256-CBC + SM2/ECC + MD5签名")
        
        print("\n📋 其他可能的加密类型:")
        print("   - C001: 基础RSA加密")
        print("   - C002: AES-128加密")
        print("   - C003: AES-192加密")
        print("   - C004: AES-256-ECB加密")
        print("   - C005: AES-256-CBC + 国密SM2")
        print("   - C006: AES-256-GCM加密")
    
    def analyze_security_features(self):
        """分析安全特性"""
        print("\n🛡️ 安全特性分析:")
        
        print("   多层加密保护:")
        print("   ✓ 对称加密: AES-256保护数据内容")
        print("   ✓ 非对称加密: SM2/ECC保护密钥传输")
        print("   ✓ 数字签名: 确保数据完整性")
        print("   ✓ 随机密钥: 每次请求使用不同AES密钥")
        
        print("\n   防护能力:")
        print("   ✓ 防窃听: 数据全程加密传输")
        print("   ✓ 防篡改: 数字签名验证")
        print("   ✓ 防重放: 可能包含时间戳或随机数")
        print("   ✓ 前向安全: 每次使用新的会话密钥")
    
    def try_decrypt_analysis(self):
        """尝试分析加密数据（仅结构分析）"""
        print("\n🔍 加密数据结构分析:")

        try:
            # 分析data字段
            data_bytes = self.safe_base64_decode(self.sample_request['data'])
            print(f"   data字段二进制分析:")
            print(f"     - 总长度: {len(data_bytes)} 字节")
            if len(data_bytes) >= 16:
                print(f"     - 前16字节(可能是IV): {data_bytes[:16].hex()}")
            print(f"     - 数据特征: {'随机性强' if self.check_randomness(data_bytes) else '可能有模式'}")

            # 分析key字段
            key_bytes = self.safe_base64_decode(self.sample_request['key'])
            print(f"   key字段二进制分析:")
            print(f"     - 总长度: {len(key_bytes)} 字节")
            if len(key_bytes) >= 16:
                print(f"     - 前16字节: {key_bytes[:16].hex()}")
            print(f"     - 数据特征: {'随机性强' if self.check_randomness(key_bytes) else '可能有模式'}")

            # 分析sign字段
            try:
                sign_bytes = bytes.fromhex(self.sample_request['sign'])
                print(f"   sign字段分析:")
                print(f"     - 长度: {len(sign_bytes)} 字节")
                print(f"     - 十六进制: {self.sample_request['sign']}")
                print(f"     - 可能算法: {'MD5' if len(sign_bytes) == 16 else 'SHA256' if len(sign_bytes) == 32 else '未知'}")
            except ValueError:
                print(f"   sign字段分析: 非标准十六进制格式")

        except Exception as e:
            print(f"   分析错误: {e}")
    
    def check_randomness(self, data):
        """检查数据随机性"""
        if len(data) < 16:
            return False
        
        # 简单的随机性检查
        unique_bytes = len(set(data))
        return unique_bytes > len(data) * 0.7
    
    def generate_attack_suggestions(self):
        """生成安全测试建议"""
        print("\n⚠️ 安全测试建议:")
        
        print("   可能的攻击向量:")
        print("   1. 密钥重用检测: 观察多次请求的key字段是否相同")
        print("   2. 签名绕过测试: 尝试修改sign字段或删除")
        print("   3. 重放攻击测试: 重复发送相同的加密请求")
        print("   4. 参数污染测试: 添加额外的请求参数")
        print("   5. 加密类型降级: 尝试修改encyType为较弱的算法")
        
        print("\n   建议的测试方法:")
        print("   1. 抓包分析: 使用Burp Suite/Charles分析请求")
        print("   2. 时间分析: 观察加密/解密的时间差异")
        print("   3. 错误注入: 故意发送格式错误的加密数据")
        print("   4. 密钥猜测: 尝试常见的密钥模式")
        print("   5. 侧信道分析: 分析加密过程中的其他信息泄露")
    
    def run_analysis(self):
        """运行完整分析"""
        self.analyze_encryption_structure()
        self.analyze_public_key()
        self.analyze_encryption_flow()
        self.analyze_encryption_type()
        self.analyze_security_features()
        self.try_decrypt_analysis()
        self.generate_attack_suggestions()
        
        print("\n" + "=" * 60)
        print("✅ 分析完成!")
        print("=" * 60)

def main():
    analyzer = BestPayCryptoAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
