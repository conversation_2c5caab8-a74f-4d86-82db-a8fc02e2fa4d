<!DOCTYPE html>
<html>
<head>
    <title></title>
</head>
<body></body>
<script type="text/javascript">
    function bestpay_wkpost(path, params) {
        params = JSON.parse(params);
        var method = "POST";
        var form = document.createElement("form");
        form.setAttribute("method", method);
        form.setAttribute("action", path);

        for(var key in params){

            if (params.hasOwnProperty(key)) {

                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", key);
                hiddenField.setAttribute("value", params[key]);

                console.log("key ===== "+key);
                console.log("data ===== "+params[key]);
            }

            form.appendChild(hiddenField);
        }

        document.body.appendChild(form);
        form.submit();

    }
</script>
</html>
