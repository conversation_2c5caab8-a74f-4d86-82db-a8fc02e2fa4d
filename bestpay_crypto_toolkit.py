#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付加密工具包
基于综合分析结果的实用解密工具原型
"""

import json
import base64
import hashlib
import time
import random
import string
from datetime import datetime

class BestPayCryptoToolkit:
    def __init__(self):
        print("🔧 翼支付加密工具包")
        print("🎯 基于综合分析的实用解密工具原型")
        print("=" * 60)
        
        # mPaaS配置 (基于分析结果)
        self.mpaas_config = {
            "public_key_pem": """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4R
AmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----<PERSON><PERSON>IC KEY-----""",
            "algorithm": "SM2+AES",
            "encryption_type": "C005",
            "api_endpoint": "/gapi/equitymall/client/Activity/queryActivityInfo",
            "domain": "mapi-h5.bestpay.com.cn"
        }
        
        # 已知的请求参数
        self.known_params = {
            "fromChannelId": "yzf_app",
            "productNo": "unknown",
            "encyType": "C005"
        }
    
    def analyze_encrypted_request(self, encrypted_data):
        """分析加密请求"""
        print("\n🔍 分析加密请求...")
        
        if isinstance(encrypted_data, str):
            try:
                encrypted_data = json.loads(encrypted_data)
            except:
                print("   ❌ 无效的JSON格式")
                return None
        
        analysis = {
            "timestamp": datetime.now().isoformat(),
            "fields_found": [],
            "field_analysis": {},
            "encryption_indicators": []
        }
        
        # 检查必要字段
        required_fields = ["data", "key", "sign", "encyType"]
        for field in required_fields:
            if field in encrypted_data:
                analysis["fields_found"].append(field)
                
                field_value = encrypted_data[field]
                field_analysis = {
                    "length": len(str(field_value)),
                    "type": type(field_value).__name__,
                    "is_base64": self.is_base64(str(field_value)) if field in ["data", "key"] else False,
                    "is_hex": self.is_hex(str(field_value)) if field == "sign" else False
                }
                
                analysis["field_analysis"][field] = field_analysis
                
                print(f"   ✅ {field}: 长度{field_analysis['length']}, 类型{field_analysis['type']}")
                
                if field_analysis.get("is_base64"):
                    print(f"      📝 Base64编码数据")
                if field_analysis.get("is_hex"):
                    print(f"      📝 十六进制签名")
        
        # 分析加密指标
        if "encyType" in encrypted_data and encrypted_data["encyType"] == "C005":
            analysis["encryption_indicators"].append("C005加密类型确认")
        
        if "fromChannelId" in encrypted_data and encrypted_data["fromChannelId"] == "yzf_app":
            analysis["encryption_indicators"].append("翼支付应用渠道确认")
        
        return analysis
    
    def is_base64(self, s):
        """检查是否为Base64编码"""
        try:
            if len(s) % 4 == 0:
                base64.b64decode(s, validate=True)
                return True
        except:
            pass
        return False
    
    def is_hex(self, s):
        """检查是否为十六进制"""
        try:
            int(s, 16)
            return len(s) % 2 == 0
        except:
            return False
    
    def extract_encryption_patterns(self, request_samples):
        """提取加密模式"""
        print(f"\n📊 分析 {len(request_samples)} 个请求样本...")
        
        patterns = {
            "data_lengths": [],
            "key_lengths": [],
            "sign_patterns": [],
            "common_fields": {},
            "timestamp_analysis": []
        }
        
        for i, sample in enumerate(request_samples):
            if isinstance(sample, str):
                try:
                    sample = json.loads(sample)
                except:
                    continue
            
            # 分析data字段
            if "data" in sample:
                data_len = len(sample["data"])
                patterns["data_lengths"].append(data_len)
            
            # 分析key字段
            if "key" in sample:
                key_len = len(sample["key"])
                patterns["key_lengths"].append(key_len)
            
            # 分析sign字段
            if "sign" in sample:
                sign_val = sample["sign"]
                patterns["sign_patterns"].append({
                    "length": len(sign_val),
                    "first_chars": sign_val[:8] if len(sign_val) >= 8 else sign_val
                })
            
            # 分析公共字段
            for key, value in sample.items():
                if key not in ["data", "key", "sign"]:
                    if key not in patterns["common_fields"]:
                        patterns["common_fields"][key] = []
                    patterns["common_fields"][key].append(value)
        
        # 统计分析
        if patterns["data_lengths"]:
            avg_data_len = sum(patterns["data_lengths"]) / len(patterns["data_lengths"])
            print(f"   📊 data字段平均长度: {avg_data_len:.1f}")
        
        if patterns["key_lengths"]:
            avg_key_len = sum(patterns["key_lengths"]) / len(patterns["key_lengths"])
            print(f"   📊 key字段平均长度: {avg_key_len:.1f}")
        
        if patterns["sign_patterns"]:
            sign_lengths = [p["length"] for p in patterns["sign_patterns"]]
            avg_sign_len = sum(sign_lengths) / len(sign_lengths)
            print(f"   📊 sign字段平均长度: {avg_sign_len:.1f}")
        
        # 分析公共字段
        print(f"   📊 公共字段分析:")
        for field, values in patterns["common_fields"].items():
            unique_values = list(set(values))
            if len(unique_values) == 1:
                print(f"      {field}: 固定值 '{unique_values[0]}'")
            else:
                print(f"      {field}: {len(unique_values)} 个不同值")
        
        return patterns
    
    def attempt_partial_decryption(self, encrypted_request):
        """尝试部分解密"""
        print(f"\n🔓 尝试部分解密...")
        
        if isinstance(encrypted_request, str):
            try:
                encrypted_request = json.loads(encrypted_request)
            except:
                print("   ❌ 无效的JSON格式")
                return None
        
        results = {
            "data_analysis": None,
            "key_analysis": None,
            "sign_verification": None,
            "decryption_attempts": []
        }
        
        # 分析data字段
        if "data" in encrypted_request:
            data_field = encrypted_request["data"]
            if self.is_base64(data_field):
                try:
                    decoded_data = base64.b64decode(data_field)
                    results["data_analysis"] = {
                        "original_length": len(data_field),
                        "decoded_length": len(decoded_data),
                        "decoded_hex": decoded_data[:32].hex() + "..." if len(decoded_data) > 32 else decoded_data.hex(),
                        "entropy": self.calculate_entropy(decoded_data)
                    }
                    print(f"   📊 data字段解码成功: {len(decoded_data)} 字节")
                    print(f"      熵值: {results['data_analysis']['entropy']:.2f} (高熵值表示加密数据)")
                except Exception as e:
                    print(f"   ❌ data字段解码失败: {e}")
        
        # 分析key字段
        if "key" in encrypted_request:
            key_field = encrypted_request["key"]
            if self.is_base64(key_field):
                try:
                    decoded_key = base64.b64decode(key_field)
                    results["key_analysis"] = {
                        "original_length": len(key_field),
                        "decoded_length": len(decoded_key),
                        "decoded_hex": decoded_key.hex(),
                        "entropy": self.calculate_entropy(decoded_key)
                    }
                    print(f"   📊 key字段解码成功: {len(decoded_key)} 字节")
                    print(f"      可能是加密的AES密钥")
                except Exception as e:
                    print(f"   ❌ key字段解码失败: {e}")
        
        # 分析sign字段
        if "sign" in encrypted_request:
            sign_field = encrypted_request["sign"]
            results["sign_verification"] = {
                "length": len(sign_field),
                "is_hex": self.is_hex(sign_field),
                "possible_algorithm": "MD5" if len(sign_field) == 32 else "SHA256" if len(sign_field) == 64 else "Unknown"
            }
            print(f"   📊 sign字段分析: {results['sign_verification']['possible_algorithm']}")
        
        return results
    
    def calculate_entropy(self, data):
        """计算数据熵值"""
        if not data:
            return 0
        
        # 计算字节频率
        byte_counts = {}
        for byte in data:
            byte_counts[byte] = byte_counts.get(byte, 0) + 1
        
        # 计算熵
        entropy = 0
        data_len = len(data)
        for count in byte_counts.values():
            probability = count / data_len
            if probability > 0:
                entropy -= probability * (probability.bit_length() - 1)
        
        return entropy
    
    def generate_request_template(self, analysis_results=None):
        """生成请求模板"""
        print(f"\n📝 生成请求模板...")
        
        template = {
            "url": f"https://{self.mpaas_config['domain']}{self.mpaas_config['api_endpoint']}",
            "method": "POST",
            "headers": {
                "Content-Type": "application/json",
                "User-Agent": "BestPay/11.0 (Android)",
                "Accept": "application/json"
            },
            "body": {
                "data": "<BASE64_ENCODED_ENCRYPTED_DATA>",
                "key": "<BASE64_ENCODED_ENCRYPTED_AES_KEY>", 
                "sign": "<MD5_OR_SM3_SIGNATURE>",
                "encyType": "C005",
                "fromChannelId": "yzf_app",
                "productNo": "<PRODUCT_NUMBER>",
                "timestamp": int(time.time() * 1000)
            }
        }
        
        print(f"   ✅ 请求模板生成完成")
        print(f"      URL: {template['url']}")
        print(f"      方法: {template['method']}")
        print(f"      加密类型: {template['body']['encyType']}")
        
        return template
    
    def save_analysis_results(self, results, filename="crypto_analysis_results.json"):
        """保存分析结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 分析结果已保存: {filename}")
    
    def run_comprehensive_analysis(self, sample_requests=None):
        """运行综合分析"""
        print("🎯 开始综合加密分析...")
        
        analysis_results = {
            "timestamp": datetime.now().isoformat(),
            "mpaas_config": self.mpaas_config,
            "analysis_summary": {}
        }
        
        if sample_requests:
            # 分析样本请求
            if len(sample_requests) == 1:
                # 单个请求分析
                request_analysis = self.analyze_encrypted_request(sample_requests[0])
                decryption_attempt = self.attempt_partial_decryption(sample_requests[0])
                
                analysis_results["single_request_analysis"] = request_analysis
                analysis_results["decryption_attempt"] = decryption_attempt
            else:
                # 多个请求模式分析
                pattern_analysis = self.extract_encryption_patterns(sample_requests)
                analysis_results["pattern_analysis"] = pattern_analysis
        
        # 生成请求模板
        request_template = self.generate_request_template()
        analysis_results["request_template"] = request_template
        
        # 保存结果
        self.save_analysis_results(analysis_results)
        
        print("\n" + "=" * 60)
        print("🎉 综合加密分析完成!")
        print("\n💡 下一步建议:")
        print("   1. 使用Charles Proxy收集更多请求样本")
        print("   2. 分析libmpaas_crypto.so获取私钥")
        print("   3. 实现SM2解密算法")
        print("   4. 构建完整的解密工具")
        print("=" * 60)
        
        return analysis_results

def main():
    toolkit = BestPayCryptoToolkit()
    
    print("\n🔧 翼支付加密工具包使用说明:")
    print("   1. 提供加密请求样本进行分析")
    print("   2. 分析加密模式和参数")
    print("   3. 尝试部分解密")
    print("   4. 生成请求模板")
    
    # 示例用法
    sample_request = {
        "data": "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K14=",  # 示例Base64数据
        "key": "BNK8xHZObcbeUEFUxn3eiMD+H1/lK/4taVuJ0wqTXBKiD9Szcr...",  # 示例加密密钥
        "sign": "a1b2c3d4e5f6789012345678901234567",  # 示例签名
        "encyType": "C005",
        "fromChannelId": "yzf_app",
        "productNo": "12345"
    }
    
    print(f"\n📝 使用示例请求进行演示...")
    results = toolkit.run_comprehensive_analysis([sample_request])

if __name__ == "__main__":
    main()
