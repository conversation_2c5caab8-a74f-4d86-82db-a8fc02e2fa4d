(function(){var e='%@';var n=4096;var a=n*4;console.log('BugMe: loading appx dev hook...');var o=0;var r=function(e,n,r,t){var p=++o;if(t){p+='_'+t}var i=r;if(typeof i==='object'){try{i=JSON.stringify(r)}catch(e){}}if(i&&typeof i!=='string'&&i.toString){i=i.toString()}if(i&&i.slice){i=i.slice(0,a)}else{i=''}console.log('devTool@'+p+'@'+e+'@'+n+' '+i)};var t=function(e,n,a){var o;try{if(e&&n&&typeof e[n]==='object'){o=JSON.stringify(e[n])}}catch(e){}if(o&&o.length>a){e[n]=o.slice(0,a)+'...'}return e};var p=function(){if(typeof AFAppX!=='undefined'){var e={ua:navigator.swuserAgent,sdkVersion:AFAppX.abridge.SDKVersion}}};var i=function(){return;if(typeof AFAppX!=='undefined'){var e={};var n=(AFAppX.getCurrentPages()||[]).map(function(n){var a=n.route;var o=n.data;e[a]=o;return{route:a}});r('bugme','reportStatus',{pages:n})}};var l=function(e,a){return function(o){if(e==='App'){r('App',a,o&&o.options||{})}else if(e==='Page'){var p=o&&o.page||{};var l=p&&p.route;var g='';if(a==='onUpdate'&&p.data){g=p.data}else if(a==='onPerf'){g={dataLen:o.dataLen,transferStart:o.transferStart,transferEnd:o.transferEnd,renderStart:o.renderStart,renderEnd:o.renderEnd}}else if(a==='onLoad'){g={query:o.query}}else if(a==='onTabItemTap'||a==='onPageScroll'||a==='onPullDownRefresh'||a==='onOptionMenuClick'||a==='onPopMenuClick'||a==='onShareAppMessage'){g=o.options||''}r('Page#'+l,a,g);i()}else if(e==='api'){var c=o&&o.name||'';var s=o||{};s=t(s,'res',n);r('api#'+a,c,s,o&&o.reqId)}}};__APPX_DEVTOOLS_GLOBAL_HOOK__={onAppLaunch:l('App','onLaunch'),onAppShow:l('App','onShow'),onAppHide:l('App','onHide'),onAppError:l('App','onError'),onPageLoad:l('Page','onLoad'),onPageShow:l('Page','onShow'),onPageHide:l('Page','onHide'),onPageReady:l('Page','onReady'),onPageUnload:l('Page','onUnload'),onPageUpdate:l('Page','onUpdate'),onPagePerf:l('Page','onPerf'),onTabItemTap:l('Page','onTabItemTap'),onPageScroll:l('Page','onPageScroll'),onReachBottom:l('Page','onReachBottom'),onPopMenuClick:l('Page','onPopMenuClick'),onTitleClick:l('Page','onTitleClick'),onOptionMenuClick:l('Page','onOptionMenuClick'),onPullDownRefresh:l('Page','onPullDownRefresh'),onPullIntercept:l('Page','onPullIntercept'),onShareAppMessage:l('Page','onShareAppMessage'),onApiCall:l('api','jsApiCall'),onApiSyncCall:l('api','jsApiSyncCall'),onApiCallback:l('api','jsApiCallback'),onApiSyncCallback:l('api','jsApiSyncCallback')};console.log('BugMe: loading appx dev hook...Done');if(e==='worker'){r('BugMe','workerLoaded','UA - '+navigator.swuserAgent)}})();