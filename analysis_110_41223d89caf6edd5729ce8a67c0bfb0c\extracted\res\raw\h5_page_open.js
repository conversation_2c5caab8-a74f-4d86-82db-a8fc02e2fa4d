(function(){
  if (window.NEBULALOGHASADDEDAUTOOPENEVENT) {
    return;
  };
  function onDOMReady(callback){
    var readyRE = /complete|loaded|interactive/;
    if(readyRE.test(document.readyState)) {
      setTimeout(function() {
        callback();
      }, 1);
    } else {
      document.defaultView.addEventListener('DOMContentLoaded', function () {
        callback();
      }, false);
    }
  }

  
  function getPageParam() {
    return {
      url: window.location.href,
      title: sessionStorage.getItem('H5_PAGE_TITLE') || document.title,
      url_path: window.location.pathname,
      referrer: document.referrer,
      referrer_host: document.referrer && getUrlHost(document.referrer)
    };
  }
  
  function getUrlHost(url) {
    const a=document.createElement('a');
    a.href=url;
    return a.hostname;
  }

  onDOMReady(function(){
    AlipayJSBridge.call("h5PageOpen", getPageParam());
  });
  window.NEBULALOGHASADDEDAUTOOPENEVENT = true;
})();
