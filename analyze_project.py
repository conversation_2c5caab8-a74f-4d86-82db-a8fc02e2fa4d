#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android项目分析工具
分析APK资源文件夹的结构、依赖关系和技术栈
"""

import os
import json
import xml.etree.ElementTree as ET
from collections import defaultdict, Counter
import re

class AndroidProjectAnalyzer:
    def __init__(self, project_path='.'):
        self.project_path = project_path
        self.analysis_result = {
            'project_info': {},
            'file_statistics': {},
            'dependencies': {},
            'features': [],
            'security_analysis': {},
            'architecture': {}
        }
    
    def analyze(self):
        """执行完整的项目分析"""
        print("🔍 开始分析Android项目...")
        
        self.analyze_manifest()
        self.analyze_file_structure()
        self.analyze_dependencies()
        self.analyze_features()
        self.analyze_security()
        self.analyze_architecture()
        
        return self.analysis_result
    
    def analyze_manifest(self):
        """分析AndroidManifest.xml"""
        print("📋 分析AndroidManifest.xml...")
        
        manifest_path = os.path.join(self.project_path, 'AndroidManifest.xml')
        if not os.path.exists(manifest_path):
            return
        
        try:
            tree = ET.parse(manifest_path)
            root = tree.getroot()
            
            # 基本信息
            self.analysis_result['project_info'] = {
                'package_name': root.get('package'),
                'version_code': root.get('{http://schemas.android.com/apk/res/android}versionCode'),
                'version_name': root.get('{http://schemas.android.com/apk/res/android}versionName'),
                'min_sdk': None,
                'target_sdk': None,
                'compile_sdk': root.get('{http://schemas.android.com/apk/res/android}compileSdkVersion')
            }
            
            # SDK版本
            uses_sdk = root.find('uses-sdk')
            if uses_sdk is not None:
                self.analysis_result['project_info']['min_sdk'] = uses_sdk.get('{http://schemas.android.com/apk/res/android}minSdkVersion')
                self.analysis_result['project_info']['target_sdk'] = uses_sdk.get('{http://schemas.android.com/apk/res/android}targetSdkVersion')
            
            # 权限分析
            permissions = []
            for perm in root.findall('uses-permission'):
                perm_name = perm.get('{http://schemas.android.com/apk/res/android}name')
                if perm_name:
                    permissions.append(perm_name)
            
            self.analysis_result['project_info']['permissions'] = permissions
            self.analysis_result['project_info']['permission_count'] = len(permissions)
            
            # 组件统计
            application = root.find('application')
            if application is not None:
                activities = len(application.findall('activity'))
                services = len(application.findall('service'))
                receivers = len(application.findall('receiver'))
                providers = len(application.findall('provider'))
                
                self.analysis_result['project_info']['components'] = {
                    'activities': activities,
                    'services': services,
                    'receivers': receivers,
                    'providers': providers
                }
        
        except Exception as e:
            print(f"❌ 分析AndroidManifest.xml时出错: {e}")
    
    def analyze_file_structure(self):
        """分析文件结构"""
        print("📁 分析文件结构...")
        
        file_stats = defaultdict(int)
        total_size = 0
        file_count = 0
        
        for root, dirs, files in os.walk(self.project_path):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    size = os.path.getsize(file_path)
                    total_size += size
                    file_count += 1
                    
                    ext = os.path.splitext(file)[1].lower()
                    file_stats[ext] += 1
                except:
                    continue
        
        self.analysis_result['file_statistics'] = {
            'total_files': file_count,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'file_types': dict(file_stats)
        }
    
    def analyze_dependencies(self):
        """分析依赖关系"""
        print("📦 分析依赖关系...")
        
        dependencies = {
            'native_libraries': [],
            'frameworks': [],
            'third_party_sdks': []
        }
        
        # 分析native库
        lib_path = os.path.join(self.project_path, 'lib', 'arm64-v8a')
        if os.path.exists(lib_path):
            for lib in os.listdir(lib_path):
                if lib.endswith('.so'):
                    dependencies['native_libraries'].append(lib)
        
        # 分析META-INF中的依赖信息
        meta_inf_path = os.path.join(self.project_path, 'META-INF')
        if os.path.exists(meta_inf_path):
            for file in os.listdir(meta_inf_path):
                if file.endswith('.version'):
                    framework = file.replace('.version', '').replace('_', '.')
                    dependencies['frameworks'].append(framework)
        
        # 从AndroidManifest.xml中提取第三方SDK
        manifest_path = os.path.join(self.project_path, 'AndroidManifest.xml')
        if os.path.exists(manifest_path):
            with open(manifest_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 检测常见的第三方SDK
                sdk_patterns = {
                    'HMS Core': r'com\.huawei\.hms',
                    'Alipay': r'com\.alipay',
                    'WeChat': r'com\.tencent',
                    'ByteDance': r'com\.bytedance',
                    'Kuaishou': r'com\.kwad',
                    'mPaaS': r'com\.mpaas',
                    'Agora': r'agora',
                    'Bugly': r'bugly'
                }
                
                for sdk_name, pattern in sdk_patterns.items():
                    if re.search(pattern, content, re.IGNORECASE):
                        dependencies['third_party_sdks'].append(sdk_name)
        
        self.analysis_result['dependencies'] = dependencies
    
    def analyze_features(self):
        """分析应用功能特性"""
        print("🎯 分析应用功能特性...")
        
        features = []
        
        # 从权限推断功能
        permissions = self.analysis_result.get('project_info', {}).get('permissions', [])
        
        permission_features = {
            'android.permission.CAMERA': '相机功能',
            'android.permission.RECORD_AUDIO': '录音功能',
            'android.permission.ACCESS_FINE_LOCATION': 'GPS定位',
            'android.permission.ACCESS_COARSE_LOCATION': '网络定位',
            'android.permission.NFC': 'NFC支付',
            'android.permission.USE_FINGERPRINT': '指纹识别',
            'android.permission.USE_BIOMETRIC': '生物识别',
            'android.permission.BLUETOOTH': '蓝牙功能',
            'android.permission.READ_CONTACTS': '通讯录访问',
            'android.permission.WRITE_EXTERNAL_STORAGE': '文件存储',
            'android.permission.INTERNET': '网络访问'
        }
        
        for perm in permissions:
            if perm in permission_features:
                features.append(permission_features[perm])
        
        # 从资源文件推断功能
        assets_path = os.path.join(self.project_path, 'assets')
        if os.path.exists(assets_path):
            for root, dirs, files in os.walk(assets_path):
                for file in files:
                    if 'face' in file.lower() or 'liveness' in file.lower():
                        features.append('人脸识别')
                    elif 'ocr' in file.lower() or 'idcard' in file.lower():
                        features.append('OCR识别')
                    elif 'bankcard' in file.lower():
                        features.append('银行卡识别')
                    elif 'qr' in file.lower() or 'scan' in file.lower():
                        features.append('二维码扫描')
        
        self.analysis_result['features'] = list(set(features))
    
    def analyze_security(self):
        """安全分析"""
        print("🔒 进行安全分析...")
        
        security_info = {
            'encryption_libraries': [],
            'security_features': [],
            'potential_risks': []
        }
        
        # 检查加密库
        lib_path = os.path.join(self.project_path, 'lib', 'arm64-v8a')
        if os.path.exists(lib_path):
            for lib in os.listdir(lib_path):
                if any(keyword in lib.lower() for keyword in ['crypto', 'ssl', 'security', 'encrypt']):
                    security_info['encryption_libraries'].append(lib)
        
        # 检查安全特性
        permissions = self.analysis_result.get('project_info', {}).get('permissions', [])
        if 'android.permission.USE_FINGERPRINT' in permissions:
            security_info['security_features'].append('指纹认证')
        if 'android.permission.USE_BIOMETRIC' in permissions:
            security_info['security_features'].append('生物识别认证')
        
        # 检查潜在风险
        if 'android.permission.SYSTEM_ALERT_WINDOW' in permissions:
            security_info['potential_risks'].append('系统级窗口权限')
        if 'android.permission.WRITE_EXTERNAL_STORAGE' in permissions:
            security_info['potential_risks'].append('外部存储写入权限')
        
        self.analysis_result['security_analysis'] = security_info
    
    def analyze_architecture(self):
        """架构分析"""
        print("🏗️ 分析应用架构...")
        
        architecture = {
            'ui_framework': [],
            'hybrid_technologies': [],
            'architecture_patterns': []
        }
        
        # 检查UI框架
        if os.path.exists(os.path.join(self.project_path, 'assets', 'flutter_assets')):
            architecture['ui_framework'].append('Flutter')
        
        # 检查混合开发技术
        if os.path.exists(os.path.join(self.project_path, 'assets', 'h5_bridge.js')):
            architecture['hybrid_technologies'].append('H5 Bridge')
        
        if any('webview' in f.lower() for f in os.listdir(self.project_path) if os.path.isfile(f)):
            architecture['hybrid_technologies'].append('WebView')
        
        # 检查架构模式
        if 'mpaas' in str(self.analysis_result.get('dependencies', {})):
            architecture['architecture_patterns'].append('mPaaS移动开发平台')
        
        self.analysis_result['architecture'] = architecture
    
    def generate_report(self):
        """生成分析报告"""
        print("\n" + "="*60)
        print("📊 Android项目分析报告")
        print("="*60)
        
        # 基本信息
        info = self.analysis_result.get('project_info', {})
        print(f"\n📱 应用基本信息:")
        print(f"   包名: {info.get('package_name', 'N/A')}")
        print(f"   版本: {info.get('version_name', 'N/A')} ({info.get('version_code', 'N/A')})")
        print(f"   SDK版本: {info.get('min_sdk', 'N/A')} - {info.get('target_sdk', 'N/A')}")
        
        # 文件统计
        stats = self.analysis_result.get('file_statistics', {})
        print(f"\n📁 文件统计:")
        print(f"   总文件数: {stats.get('total_files', 0)}")
        print(f"   总大小: {stats.get('total_size_mb', 0)} MB")
        
        # 主要文件类型
        file_types = stats.get('file_types', {})
        if file_types:
            print(f"   主要文件类型:")
            sorted_types = sorted(file_types.items(), key=lambda x: x[1], reverse=True)[:10]
            for ext, count in sorted_types:
                ext_name = ext if ext else '无扩展名'
                print(f"     {ext_name}: {count}")
        
        # 组件统计
        components = info.get('components', {})
        if components:
            print(f"\n🧩 应用组件:")
            print(f"   Activity: {components.get('activities', 0)}")
            print(f"   Service: {components.get('services', 0)}")
            print(f"   Receiver: {components.get('receivers', 0)}")
            print(f"   Provider: {components.get('providers', 0)}")
        
        # 权限分析
        permissions = info.get('permissions', [])
        print(f"\n🔐 权限分析 (共{len(permissions)}个):")
        dangerous_perms = [p for p in permissions if any(keyword in p for keyword in 
                          ['CAMERA', 'LOCATION', 'RECORD_AUDIO', 'READ_CONTACTS', 'WRITE_EXTERNAL'])]
        if dangerous_perms:
            print(f"   敏感权限:")
            for perm in dangerous_perms[:5]:
                print(f"     {perm}")
        
        # 功能特性
        features = self.analysis_result.get('features', [])
        if features:
            print(f"\n🎯 主要功能:")
            for feature in features:
                print(f"   • {feature}")
        
        # 依赖分析
        deps = self.analysis_result.get('dependencies', {})
        native_libs = deps.get('native_libraries', [])
        third_party = deps.get('third_party_sdks', [])
        
        if native_libs:
            print(f"\n📚 Native库 (共{len(native_libs)}个):")
            for lib in native_libs[:10]:
                print(f"   • {lib}")
        
        if third_party:
            print(f"\n🔌 第三方SDK:")
            for sdk in third_party:
                print(f"   • {sdk}")
        
        # 安全分析
        security = self.analysis_result.get('security_analysis', {})
        enc_libs = security.get('encryption_libraries', [])
        sec_features = security.get('security_features', [])
        risks = security.get('potential_risks', [])
        
        if enc_libs or sec_features:
            print(f"\n🔒 安全特性:")
            for feature in sec_features:
                print(f"   • {feature}")
            if enc_libs:
                print(f"   加密库: {len(enc_libs)}个")
        
        if risks:
            print(f"\n⚠️  潜在风险:")
            for risk in risks:
                print(f"   • {risk}")
        
        # 架构信息
        arch = self.analysis_result.get('architecture', {})
        ui_fw = arch.get('ui_framework', [])
        hybrid = arch.get('hybrid_technologies', [])
        patterns = arch.get('architecture_patterns', [])
        
        if ui_fw or hybrid or patterns:
            print(f"\n🏗️ 技术架构:")
            for fw in ui_fw:
                print(f"   UI框架: {fw}")
            for tech in hybrid:
                print(f"   混合技术: {tech}")
            for pattern in patterns:
                print(f"   架构模式: {pattern}")
        
        print("\n" + "="*60)

def main():
    analyzer = AndroidProjectAnalyzer()
    analyzer.analyze()
    analyzer.generate_report()
    
    # 保存详细结果到JSON文件
    with open('analysis_result.json', 'w', encoding='utf-8') as f:
        json.dump(analyzer.analysis_result, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细分析结果已保存到 analysis_result.json")

if __name__ == "__main__":
    main()
