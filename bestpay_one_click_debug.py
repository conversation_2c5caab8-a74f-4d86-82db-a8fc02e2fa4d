#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付一键调试脚本
自动化完成从环境检查到数据获取的全部流程
"""

import subprocess
import time
import os
import json
import threading
from datetime import datetime

class BestPayOneClickDebug:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        self.device_id = None
        self.frida_server_running = False
        self.hook_data = []
        self.debug_session_active = False
        
        print("🚀 翼支付一键调试脚本")
        print("🎯 自动化完成完整调试流程")
        print("=" * 60)
    
    def print_step(self, step, title, status="🔄"):
        """打印步骤信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"\n{status} [{timestamp}] 第{step}步: {title}")
        print("-" * 50)
    
    def run_command(self, cmd, capture_output=True, timeout=30):
        """执行命令"""
        try:
            if capture_output:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
                return result.returncode == 0, result.stdout, result.stderr
            else:
                result = subprocess.run(cmd, shell=True, timeout=timeout)
                return result.returncode == 0, "", ""
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def check_environment(self):
        """检查环境"""
        self.print_step(1, "环境检查", "🔍")
        
        # 检查Python
        success, stdout, stderr = self.run_command("python --version")
        if success:
            print(f"   ✅ Python: {stdout.strip()}")
        else:
            print("   ❌ Python未安装")
            return False
        
        # 检查Frida
        success, stdout, stderr = self.run_command("frida --version")
        if success:
            print(f"   ✅ Frida: {stdout.strip()}")
        else:
            print("   ❌ Frida未安装")
            return False
        
        # 检查ADB
        if os.path.exists(self.adb_cmd):
            print("   ✅ ADB工具存在")
        else:
            print("   ❌ ADB工具不存在")
            return False
        
        print("   🎉 环境检查通过!")
        return True
    
    def setup_device(self):
        """设置设备连接"""
        self.print_step(2, "设备连接设置", "📱")
        
        # 重启ADB服务
        print("   🔄 重启ADB服务...")
        self.run_command(f"{self.adb_cmd} kill-server")
        time.sleep(2)
        self.run_command(f"{self.adb_cmd} start-server")
        time.sleep(2)
        
        # 检查设备连接
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} devices")
        if not success:
            print("   ❌ ADB命令失败")
            return False
        
        lines = stdout.strip().split('\n')[1:]
        devices = [line for line in lines if line.strip() and '\t' in line]
        
        if not devices:
            print("   ❌ 没有检测到设备")
            print("   💡 请确保手机已连接并开启USB调试")
            return False
        
        for device in devices:
            device_id, status = device.split('\t')
            print(f"   📱 设备 {device_id}: {status}")
            
            if status == "device":
                self.device_id = device_id
                print("   ✅ 设备连接正常")
                break
        
        if not self.device_id:
            print("   ❌ 没有可用设备")
            return False
        
        # 设置端口转发
        print("   🔧 设置端口转发...")
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} forward tcp:27042 tcp:27042")
        if success:
            print("   ✅ 端口转发设置成功")
        else:
            print(f"   ⚠️ 端口转发设置失败: {stderr}")
        
        return True
    
    def setup_frida_server(self):
        """设置Frida Server"""
        self.print_step(3, "Frida Server设置", "🔧")
        
        # 检查Frida Server文件
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ls /data/local/tmp/frida-server")
        if not success:
            print("   ❌ Frida Server文件不存在")
            print("   💡 请先推送frida-server到设备")
            return False
        
        print("   ✅ Frida Server文件存在")
        
        # 清理旧进程
        print("   🧹 清理旧的Frida进程...")
        self.run_command(f"{self.adb_cmd} shell su -c 'pkill -f frida-server'")
        time.sleep(2)
        
        # 启动Frida Server
        print("   🚀 启动Frida Server...")
        try:
            # 使用后台进程启动
            process = subprocess.Popen(
                f"{self.adb_cmd} shell su -c 'cd /data/local/tmp && ./frida-server'",
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待启动
            time.sleep(5)
            
            # 检查是否启动成功
            if process.poll() is None:
                print("   ✅ Frida Server启动成功")
                self.frida_server_running = True
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"   ❌ Frida Server启动失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"   ❌ 启动失败: {e}")
            return False
    
    def test_frida_connection(self):
        """测试Frida连接"""
        self.print_step(4, "Frida连接测试", "🔍")
        
        for attempt in range(3):
            print(f"   尝试 {attempt + 1}/3...")
            success, stdout, stderr = self.run_command("frida-ps -U", timeout=10)
            
            if success and stdout.strip():
                lines = stdout.strip().split('\n')
                process_count = len(lines) - 1
                print(f"   ✅ Frida连接成功！检测到 {process_count} 个进程")
                
                # 检查翼支付是否在运行
                if "bestpay" in stdout.lower() or "chinatelecom" in stdout.lower():
                    print("   🎯 检测到翼支付进程正在运行")
                else:
                    print("   💡 翼支付应用未运行，请手动启动")
                
                return True
            else:
                print(f"   ❌ 连接失败: {stderr}")
                if attempt < 2:
                    print("   🔄 重新启动Frida Server...")
                    self.run_command(f"{self.adb_cmd} shell su -c 'pkill -f frida-server'")
                    time.sleep(2)
                    subprocess.Popen(
                        f"{self.adb_cmd} shell su -c 'cd /data/local/tmp && ./frida-server'",
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    time.sleep(3)
        
        print("   ❌ Frida连接失败")
        return False
    
    def start_hook_session(self):
        """开始Hook会话"""
        self.print_step(5, "启动Hook调试会话", "🎯")
        
        # 检查Hook脚本是否存在
        if not os.path.exists("mpaas_hook.js"):
            print("   ❌ Hook脚本不存在，正在创建...")
            self.create_hook_script()
        
        print("   📱 操作指南:")
        print("      1. 确保翼支付应用已打开")
        print("      2. 进入'权益商城'页面")
        print("      3. 刷新页面或点击活动")
        print("      4. 观察Hook输出")
        print("      5. 按Ctrl+C停止Hook")
        
        input("\n   ⏸️ 准备好后按回车开始Hook...")
        
        # 启动Hook
        print("   🚀 启动Hook会话...")
        self.debug_session_active = True
        
        try:
            # 启动数据收集线程
            data_thread = threading.Thread(target=self.collect_hook_data)
            data_thread.daemon = True
            data_thread.start()
            
            # 运行Frida Hook
            cmd = "frida -U com.chinatelecom.bestpayclient -l mpaas_hook.js"
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            print("   ✅ Hook会话已启动")
            print("   📊 实时监控中...")
            print("   💡 在手机上操作翼支付应用")
            
            # 等待用户中断或进程结束
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n   ⏹️ 用户停止Hook会话")
                process.terminate()
            
            self.debug_session_active = False
            
        except Exception as e:
            print(f"   ❌ Hook会话失败: {e}")
            self.debug_session_active = False
            return False
        
        return True
    
    def collect_hook_data(self):
        """收集Hook数据"""
        while self.debug_session_active:
            # 这里可以添加数据收集逻辑
            time.sleep(1)
    
    def create_hook_script(self):
        """创建Hook脚本"""
        script = '''// 翼支付mPaaS加密Hook脚本
Java.perform(function() {
    console.log("🔐 启动翼支付加密Hook...");
    
    // Hook AES加密
    try {
        var Cipher = Java.use("javax.crypto.Cipher");
        var originalDoFinal = Cipher.doFinal.overload('[B');
        
        originalDoFinal.implementation = function(input) {
            var result = originalDoFinal.call(this, input);
            var algorithm = this.getAlgorithm();
            
            if (algorithm.includes("AES")) {
                console.log("\\n🔑 [AES加密] " + new Date().toLocaleTimeString());
                console.log("算法: " + algorithm);
                console.log("输入长度: " + input.length);
                console.log("输出长度: " + result.length);
                console.log("输入数据: " + bytesToHex(input.slice(0, 32)));
                console.log("输出数据: " + bytesToHex(result.slice(0, 32)));
                
                // 尝试获取密钥
                try {
                    var key = this.getKey();
                    if (key) {
                        console.log("🔑 AES密钥: " + bytesToHex(key.getEncoded()));
                    }
                } catch(e) {}
            }
            
            return result;
        };
        
        console.log("✅ AES Hook成功");
    } catch(e) {
        console.log("❌ AES Hook失败: " + e);
    }
    
    // Hook Base64编码
    try {
        var Base64 = Java.use("android.util.Base64");
        var originalEncode = Base64.encodeToString.overload('[B', 'int');
        
        originalEncode.implementation = function(input, flags) {
            var result = originalEncode.call(this, input, flags);
            
            if (input.length > 32) {
                console.log("\\n📝 [Base64编码] " + new Date().toLocaleTimeString());
                console.log("原始长度: " + input.length);
                console.log("编码结果: " + result.substring(0, 100) + "...");
                
                // 检查是否是关键数据
                if (result.length > 100) {
                    console.log("🎯 可能的加密数据!");
                }
            }
            
            return result;
        };
        
        console.log("✅ Base64 Hook成功");
    } catch(e) {
        console.log("❌ Base64 Hook失败: " + e);
    }
    
    // Hook网络请求
    try {
        var RequestBody = Java.use("okhttp3.RequestBody");
        var originalCreate = RequestBody.create.overload('okhttp3.MediaType', 'java.lang.String');
        
        originalCreate.implementation = function(mediaType, content) {
            if (content.includes('"data":') || content.includes('"key":') || content.includes('"sign":')) {
                console.log("\\n🌐 [网络请求] " + new Date().toLocaleTimeString());
                console.log("🎯 发现加密请求!");
                console.log("Content-Type: " + mediaType);
                console.log("请求内容: " + content);
            }
            
            return originalCreate.call(this, mediaType, content);
        };
        
        console.log("✅ 网络请求Hook成功");
    } catch(e) {
        console.log("❌ 网络请求Hook失败: " + e);
    }
});

function bytesToHex(bytes) {
    var hex = "";
    for (var i = 0; i < Math.min(bytes.length, 32); i++) {
        hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2);
    }
    return hex;
}

console.log("🎉 Hook脚本加载完成!");
console.log("💡 请在手机上操作翼支付应用...");'''
        
        with open("mpaas_hook.js", "w", encoding="utf-8") as f:
            f.write(script)
        
        print("   ✅ Hook脚本已创建")
    
    def generate_summary_report(self):
        """生成总结报告"""
        self.print_step(6, "生成调试报告", "📋")
        
        report = {
            "session_info": {
                "timestamp": datetime.now().isoformat(),
                "device_id": self.device_id,
                "frida_server_status": self.frida_server_running
            },
            "hook_data": self.hook_data,
            "next_steps": [
                "分析Hook输出中的AES密钥",
                "使用Charles抓包获取完整请求",
                "分析libmpaas_crypto.so获取私钥",
                "实现完整解密工具"
            ]
        }
        
        with open("debug_session_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print("   ✅ 调试报告已保存: debug_session_report.json")
        
        # 显示总结
        print("\n   📊 调试会话总结:")
        print(f"      - 设备ID: {self.device_id}")
        print(f"      - Frida状态: {'运行中' if self.frida_server_running else '未运行'}")
        print(f"      - Hook数据: {len(self.hook_data)} 条")
    
    def run_one_click_debug(self):
        """运行一键调试"""
        start_time = time.time()
        
        try:
            # 步骤1: 环境检查
            if not self.check_environment():
                print("\n❌ 环境检查失败，请解决环境问题后重试")
                return False
            
            # 步骤2: 设备设置
            if not self.setup_device():
                print("\n❌ 设备设置失败，请检查设备连接")
                return False
            
            # 步骤3: Frida Server设置
            if not self.setup_frida_server():
                print("\n❌ Frida Server设置失败")
                return False
            
            # 步骤4: 连接测试
            if not self.test_frida_connection():
                print("\n❌ Frida连接测试失败")
                return False
            
            # 步骤5: Hook会话
            self.start_hook_session()
            
            # 步骤6: 生成报告
            self.generate_summary_report()
            
            elapsed_time = time.time() - start_time
            
            print("\n" + "=" * 60)
            print("🎉 一键调试完成!")
            print(f"⏱️ 总耗时: {elapsed_time:.1f} 秒")
            print("📁 生成文件:")
            print("   - mpaas_hook.js (Hook脚本)")
            print("   - debug_session_report.json (调试报告)")
            print("\n🎯 如果获得了加密数据，请使用解密工具进行分析")
            print("=" * 60)
            
            return True
            
        except KeyboardInterrupt:
            print("\n\n⏹️ 用户中断调试")
            return False
        except Exception as e:
            print(f"\n❌ 调试过程出错: {e}")
            return False

def main():
    print("🚀 翼支付一键调试脚本")
    print("🎯 自动化完成完整调试流程")
    print("\n💡 使用前请确保:")
    print("   - 手机已连接并开启USB调试")
    print("   - 手机已Root并授权")
    print("   - 翼支付应用已安装")
    print("   - frida-server已推送到设备")
    
    confirm = input("\n是否开始一键调试? (y/n): ").strip().lower()
    if confirm != 'y':
        print("👋 调试已取消")
        return
    
    debugger = BestPayOneClickDebug()
    debugger.run_one_click_debug()

if __name__ == "__main__":
    main()
