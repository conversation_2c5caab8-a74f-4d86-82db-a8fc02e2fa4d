{"total_strings": 137, "crypto_strings_count": 4, "crypto_strings": [{"string": "com.chinatelecom.bestpayclient", "keyword": "bestpay", "file": "classes.dex"}, {"string": "com.ijm.dataencryption.DETool", "keyword": "encrypt", "file": "classes.dex"}, {"string": "hashCode", "keyword": "hash", "file": "classes.dex"}, {"string": "(images/icon_max_data_encrypted_xxxxx.png", "keyword": "encrypt", "file": "classes.dex"}], "sample_strings": ["l\"QT5", "/bin/linker64", "/lib64/libaoc.so", "/lib64/libart.so", "/libexec.so", "/libexecmain.so", "/proc/self/maps", "/system/bin/linker", "<clinit>", "<init>", "LLLLL", "!Landroid/annotation/SuppressLint;", "Landroid/annotation/TargetApi;", "Landroid/app/Activity;", "Landroid/app/ActivityThread;", "!Landroid/app/AppComponentFactory;", "Landroid/app/Application;", "Landroid/app/LoadedApk;", "Landroid/app/Service;", "#Landroid/content/BroadcastReceiver;", "!Landroid/content/ContentProvider;", "Landroid/content/Context;", "Landroid/content/Intent;", "$Landroid/content/pm/ApplicationInfo;", "\"Landroid/content/res/AssetManager;", "Landroid/os/Build;", "Ldalvik/annotation/Throws;", "<PERSON><PERSON><PERSON>/io/BufferedReader;", "Ljava/io/File;", "Ljava/io/FileInputStream;", "Ljava/io/FileOutputStream;", "Ljava/io/IOException;", "Ljava/io/InputStream;", "Ljava/io/InputStreamReader;", "<PERSON><PERSON><PERSON>/io/Reader;", "<PERSON><PERSON><PERSON>/lang/CharSequence;", "L<PERSON>va/lang/Class;", "<PERSON><PERSON><PERSON>/lang/ClassLoader;", "\"Ljava/lang/ClassNotFoundException;", "<PERSON><PERSON><PERSON>/lang/Exception;", "\"Ljava/lang/IllegalAccessException;", "\"Ljava/lang/InstantiationException;", "<PERSON><PERSON><PERSON>/lang/Object;", "<PERSON><PERSON><PERSON>/lang/String;", "<PERSON><PERSON><PERSON>/lang/StringBuilder;", "<PERSON><PERSON><PERSON>/lang/System;", "<PERSON><PERSON><PERSON>/lang/Throwable;", "<PERSON><PERSON><PERSON>/lang/reflect/Field;", "<PERSON><PERSON><PERSON>/lang/reflect/Method;", "<PERSON><PERSON><PERSON>/util/Arrays;", "Ljava/util/zip/CRC32;", "\"Ljava/util/zip/CheckedInputStream;", "<PERSON><PERSON><PERSON>/util/zip/Checksum;", "<PERSON><PERSON><PERSON>/util/zip/ZipEntry;", "<PERSON><PERSON><PERSON>/util/zip/ZipFile;", "Ls/h/e/l/l/A;", "Ls/h/e/l/l/C;", "Ls/h/e/l/l/N;", "Ls/h/e/l/l/S;", "Override", "SUPPORTED_ABIS", "[Ljava/lang/Class;", "[<PERSON><PERSON><PERSON>/lang/Object;", "[<PERSON><PERSON><PERSON>/lang/String;", "&androidx.core.app.CoreComponentFactory", "append", "arm64-v8a", "<PERSON><PERSON><PERSON>", "assets/ijm_lib/%s/libexec.so", " assets/ijm_lib/%s/libexecmain.so", "attachBaseContext", "ccc.rrr.hhh.s.a", "className", "close", "com.chinatelecom.bestpayclient", "com.ijm.dataencryption.DETool", "contains", "currentActivityThread", "currentTimeMillis", "dataDir", "equals", "exec_x86", "execmain", "execmain_x86", "exists", "files", "forName", "format", "getACF", "getAbsolutePath", "getApplicationInfo", "getAssets", "getClass", "getCrc", "getDeclaredField", "getDeclaredMethod", "getEntry", "getFilesDir", "getInputStream", "getPackageCodePath", "getParentFile", "getString", "getValue", "hashCode", "(images/icon_max_data_encrypted_xxxxx.png", "instantiateActivity", "instantiateApplication", "instantiateClassLoader", "instantiateProvider", "instantiateReceiver", "instantiateService", "invoke", "libexec.so", "libexecmain.so", "loadClass", "loadDEso", "loadLibrary", "mBoundApplication", "mkdirs", "newInstance", "onCreate", "orignACF", "orignAppName", "orignName", "packageName", "printStackTrace", "readLine", "ro.product.cpu.abi", "s.h.e.l.l.S", "setAccessible", "sourceDir", "supportInstantiateClassLoader", "toString", "value", "valueOf", "write", "x86_64"]}