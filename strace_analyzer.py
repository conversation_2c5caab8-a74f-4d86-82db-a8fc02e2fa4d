#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
strace系统调用跟踪分析工具
深度分析翼支付的系统调用行为
"""

import subprocess
import time
import os
import re
import json
from datetime import datetime

class StraceAnalyzer:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        self.target_package = "com.chinatelecom.bestpayclient"
        self.output_dir = "strace_analysis"
        self.analysis_results = {
            "timestamp": datetime.now().isoformat(),
            "target_package": self.target_package,
            "syscalls": {},
            "file_operations": [],
            "network_operations": [],
            "crypto_operations": [],
            "anti_debug_detections": [],
            "interesting_patterns": []
        }
        
        print("🔍 strace系统调用跟踪分析工具")
        print("🎯 深度分析翼支付的系统调用行为")
        print("=" * 60)
    
    def run_command(self, cmd, timeout=30):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='ignore')
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def check_strace_availability(self):
        """检查strace是否可用"""
        print("\n🔧 检查strace可用性...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell which strace")
        if success and stdout.strip():
            print(f"   ✅ strace可用: {stdout.strip()}")
            return True
        
        # 检查是否在/system/xbin/
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ls /system/xbin/strace")
        if success:
            print("   ✅ strace可用: /system/xbin/strace")
            return True
        
        # 检查是否在/system/bin/
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ls /system/bin/strace")
        if success:
            print("   ✅ strace可用: /system/bin/strace")
            return True
        
        print("   ❌ strace不可用")
        return False
    
    def get_target_pids(self):
        """获取目标应用的进程ID"""
        print(f"\n📱 获取{self.target_package}的进程ID...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ps | grep {self.target_package}")
        if not success or not stdout.strip():
            print("   ❌ 目标应用未运行")
            return []
        
        pids = []
        for line in stdout.strip().split('\n'):
            parts = line.split()
            if len(parts) >= 2:
                try:
                    pid = int(parts[1])
                    process_name = ' '.join(parts[8:]) if len(parts) > 8 else parts[-1]
                    pids.append((pid, process_name))
                    print(f"   📍 进程ID: {pid}, 名称: {process_name}")
                except ValueError:
                    continue
        
        return pids
    
    def run_strace_analysis(self, pid, duration=60):
        """运行strace分析"""
        print(f"\n🔍 对进程{pid}运行strace分析 (时长: {duration}秒)...")
        
        os.makedirs(self.output_dir, exist_ok=True)
        output_file = f"{self.output_dir}/strace_{pid}_{int(time.time())}.log"
        
        # 构建strace命令
        strace_cmd = f"{self.adb_cmd} shell su -c 'strace -p {pid} -f -t -e trace=all -o /data/local/tmp/strace_{pid}.log'"
        
        print(f"   🚀 执行命令: strace -p {pid} -f -t -e trace=all")
        print("   💡 请在手机上操作翼支付应用...")
        print("   📱 建议操作: 进入权益商城、刷新页面、点击活动")
        
        try:
            # 启动strace
            process = subprocess.Popen(strace_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待指定时间
            time.sleep(duration)
            
            # 停止strace
            print(f"\n⏹️ 停止strace跟踪...")
            process.terminate()
            
            # 获取日志文件
            success, stdout, stderr = self.run_command(f"{self.adb_cmd} pull /data/local/tmp/strace_{pid}.log {output_file}")
            
            if success:
                print(f"   ✅ strace日志已保存: {output_file}")
                return output_file
            else:
                print(f"   ❌ 日志获取失败: {stderr}")
                return None
                
        except Exception as e:
            print(f"   ❌ strace执行失败: {e}")
            return None
    
    def analyze_strace_log(self, log_file):
        """分析strace日志"""
        print(f"\n📊 分析strace日志: {log_file}...")
        
        if not os.path.exists(log_file):
            print("   ❌ 日志文件不存在")
            return
        
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            print(f"   📝 日志行数: {len(lines)}")
            
            # 分析系统调用
            self.analyze_syscalls(lines)
            
            # 分析文件操作
            self.analyze_file_operations(lines)
            
            # 分析网络操作
            self.analyze_network_operations(lines)
            
            # 分析加密操作
            self.analyze_crypto_operations(lines)
            
            # 检测反调试行为
            self.detect_anti_debug_behavior(lines)
            
            # 查找有趣的模式
            self.find_interesting_patterns(lines)
            
        except Exception as e:
            print(f"   ❌ 日志分析失败: {e}")
    
    def analyze_syscalls(self, lines):
        """分析系统调用统计"""
        print("   🔍 分析系统调用统计...")
        
        syscall_counts = {}
        
        for line in lines:
            # 解析系统调用名称
            match = re.match(r'.*?(\w+)\(', line)
            if match:
                syscall = match.group(1)
                syscall_counts[syscall] = syscall_counts.get(syscall, 0) + 1
        
        # 排序并显示前20个
        sorted_syscalls = sorted(syscall_counts.items(), key=lambda x: x[1], reverse=True)
        
        print("      📊 系统调用频率 (前20个):")
        for syscall, count in sorted_syscalls[:20]:
            print(f"         {syscall}: {count}次")
        
        self.analysis_results["syscalls"] = dict(sorted_syscalls)
    
    def analyze_file_operations(self, lines):
        """分析文件操作"""
        print("   📁 分析文件操作...")
        
        file_ops = []
        file_patterns = [
            r'open\("([^"]+)"',
            r'openat\([^,]+, "([^"]+)"',
            r'access\("([^"]+)"',
            r'stat\("([^"]+)"',
            r'readlink\("([^"]+)"'
        ]
        
        for line in lines:
            for pattern in file_patterns:
                matches = re.findall(pattern, line)
                for match in matches:
                    if any(keyword in match for keyword in ['/proc', '/data', '/system', 'frida', 'gum']):
                        file_ops.append({
                            "operation": line.strip(),
                            "file": match,
                            "timestamp": self.extract_timestamp(line)
                        })
        
        print(f"      📊 文件操作数量: {len(file_ops)}")
        
        # 显示有趣的文件操作
        interesting_files = [op for op in file_ops if any(keyword in op["file"] for keyword in [
            '/proc/self', '/data/local/tmp', 'frida', 'gum', 'maps', 'status'
        ])]
        
        if interesting_files:
            print("      🎯 有趣的文件操作:")
            for op in interesting_files[:10]:
                print(f"         {op['file']}")
        
        self.analysis_results["file_operations"] = file_ops
    
    def analyze_network_operations(self, lines):
        """分析网络操作"""
        print("   🌐 分析网络操作...")
        
        network_ops = []
        network_patterns = [
            r'connect\([^,]+, \{.*?sin_addr=inet_addr\("([^"]+)"\)',
            r'sendto\([^,]+, "([^"]*)"',
            r'write\([^,]+, "([^"]*)"'
        ]
        
        for line in lines:
            if any(keyword in line for keyword in ['connect', 'sendto', 'recvfrom', 'write', 'read']):
                if any(domain in line for domain in ['bestpay', 'chinatelecom', 'mapi-h5']):
                    network_ops.append({
                        "operation": line.strip(),
                        "timestamp": self.extract_timestamp(line)
                    })
        
        print(f"      📊 网络操作数量: {len(network_ops)}")
        
        if network_ops:
            print("      🎯 网络操作示例:")
            for op in network_ops[:5]:
                print(f"         {op['operation'][:100]}...")
        
        self.analysis_results["network_operations"] = network_ops
    
    def analyze_crypto_operations(self, lines):
        """分析加密操作"""
        print("   🔐 分析加密操作...")
        
        crypto_ops = []
        crypto_keywords = [
            'encrypt', 'decrypt', 'cipher', 'crypto', 'aes', 'rsa', 'sm2', 'sm3', 'sm4',
            'md5', 'sha', 'hash', 'sign', 'verify', 'key', 'iv'
        ]
        
        for line in lines:
            if any(keyword in line.lower() for keyword in crypto_keywords):
                crypto_ops.append({
                    "operation": line.strip(),
                    "timestamp": self.extract_timestamp(line)
                })
        
        print(f"      📊 加密相关操作: {len(crypto_ops)}")
        
        if crypto_ops:
            print("      🎯 加密操作示例:")
            for op in crypto_ops[:5]:
                print(f"         {op['operation'][:100]}...")
        
        self.analysis_results["crypto_operations"] = crypto_ops
    
    def detect_anti_debug_behavior(self, lines):
        """检测反调试行为"""
        print("   🛡️ 检测反调试行为...")
        
        anti_debug_patterns = [
            (r'ptrace\(PTRACE_TRACEME', "ptrace反调试检测"),
            (r'open\("[^"]*proc[^"]*maps', "内存映射检查"),
            (r'open\("[^"]*proc[^"]*status', "进程状态检查"),
            (r'strstr.*frida', "Frida字符串检测"),
            (r'access\("[^"]*frida', "Frida文件检测"),
            (r'kill\([^,]+, SIGKILL', "进程终止信号")
        ]
        
        detections = []
        
        for line in lines:
            for pattern, description in anti_debug_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    detections.append({
                        "type": description,
                        "operation": line.strip(),
                        "timestamp": self.extract_timestamp(line)
                    })
        
        print(f"      📊 反调试检测数量: {len(detections)}")
        
        if detections:
            print("      🎯 反调试行为:")
            for detection in detections[:10]:
                print(f"         {detection['type']}: {detection['operation'][:80]}...")
        
        self.analysis_results["anti_debug_detections"] = detections
    
    def find_interesting_patterns(self, lines):
        """查找有趣的模式"""
        print("   🔍 查找有趣的模式...")
        
        patterns = []
        
        # 查找Base64编码模式
        base64_pattern = r'[A-Za-z0-9+/]{20,}={0,2}'
        
        # 查找JSON数据
        json_pattern = r'\{[^}]*"[^"]*":[^}]*\}'
        
        # 查找URL
        url_pattern = r'https?://[^\s"]+'
        
        for line in lines:
            # Base64检测
            base64_matches = re.findall(base64_pattern, line)
            for match in base64_matches:
                if len(match) > 50:  # 只关注长的Base64字符串
                    patterns.append({
                        "type": "Base64编码",
                        "content": match[:100] + "...",
                        "line": line.strip()[:200]
                    })
            
            # JSON检测
            json_matches = re.findall(json_pattern, line)
            for match in json_matches:
                if any(keyword in match for keyword in ['data', 'key', 'sign', 'encrypt']):
                    patterns.append({
                        "type": "JSON数据",
                        "content": match,
                        "line": line.strip()[:200]
                    })
            
            # URL检测
            url_matches = re.findall(url_pattern, line)
            for match in url_matches:
                if 'bestpay' in match or 'chinatelecom' in match:
                    patterns.append({
                        "type": "API URL",
                        "content": match,
                        "line": line.strip()[:200]
                    })
        
        print(f"      📊 有趣模式数量: {len(patterns)}")
        
        if patterns:
            print("      🎯 有趣模式示例:")
            for pattern in patterns[:10]:
                print(f"         {pattern['type']}: {pattern['content'][:80]}...")
        
        self.analysis_results["interesting_patterns"] = patterns
    
    def extract_timestamp(self, line):
        """提取时间戳"""
        timestamp_match = re.match(r'(\d{2}:\d{2}:\d{2})', line)
        return timestamp_match.group(1) if timestamp_match else ""
    
    def save_analysis_results(self):
        """保存分析结果"""
        results_file = f"{self.output_dir}/strace_analysis_results.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 分析结果已保存: {results_file}")
    
    def generate_summary_report(self):
        """生成总结报告"""
        report_file = f"{self.output_dir}/strace_summary.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# strace系统调用分析报告\n\n")
            f.write(f"## 分析时间\n{self.analysis_results['timestamp']}\n\n")
            f.write(f"## 目标应用\n{self.analysis_results['target_package']}\n\n")
            
            f.write("## 系统调用统计\n")
            syscalls = self.analysis_results.get("syscalls", {})
            for syscall, count in list(syscalls.items())[:20]:
                f.write(f"- {syscall}: {count}次\n")
            f.write("\n")
            
            f.write("## 文件操作\n")
            file_ops = self.analysis_results.get("file_operations", [])
            f.write(f"总数: {len(file_ops)}个\n\n")
            
            f.write("## 网络操作\n")
            network_ops = self.analysis_results.get("network_operations", [])
            f.write(f"总数: {len(network_ops)}个\n\n")
            
            f.write("## 反调试检测\n")
            anti_debug = self.analysis_results.get("anti_debug_detections", [])
            f.write(f"总数: {len(anti_debug)}个\n")
            for detection in anti_debug[:10]:
                f.write(f"- {detection['type']}\n")
            f.write("\n")
            
            f.write("## 有趣模式\n")
            patterns = self.analysis_results.get("interesting_patterns", [])
            f.write(f"总数: {len(patterns)}个\n")
            for pattern in patterns[:10]:
                f.write(f"- {pattern['type']}: {pattern['content'][:50]}...\n")
        
        print(f"💾 总结报告已保存: {report_file}")
    
    def run_analysis(self):
        """运行完整分析"""
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 检查strace可用性
        if not self.check_strace_availability():
            print("❌ strace不可用，无法进行系统调用跟踪")
            return False
        
        # 获取目标进程
        pids = self.get_target_pids()
        if not pids:
            print("❌ 目标应用未运行，请先启动翼支付应用")
            return False
        
        # 选择主进程进行跟踪
        main_pid = pids[0][0]  # 选择第一个进程
        print(f"\n🎯 选择主进程进行跟踪: PID {main_pid}")
        
        # 运行strace分析
        log_file = self.run_strace_analysis(main_pid, duration=60)
        
        if log_file:
            # 分析日志
            self.analyze_strace_log(log_file)
            
            # 保存结果
            self.save_analysis_results()
            self.generate_summary_report()
            
            print("\n" + "=" * 60)
            print("🎉 strace分析完成!")
            print(f"📁 分析结果目录: {self.output_dir}")
            print("📋 主要文件:")
            print("   - strace_analysis_results.json (详细结果)")
            print("   - strace_summary.md (总结报告)")
            print("   - strace_*.log (原始日志)")
            print("=" * 60)
            
            return True
        else:
            print("❌ strace分析失败")
            return False

def main():
    print("🔍 strace系统调用跟踪分析工具")
    print("💡 此工具将跟踪翼支付应用的系统调用")
    print("⚠️ 请确保:")
    print("   - 翼支付应用正在运行")
    print("   - 设备已Root")
    print("   - strace工具可用")
    
    confirm = input("\n是否开始strace分析? (y/n): ").strip().lower()
    if confirm != 'y':
        print("👋 分析已取消")
        return
    
    analyzer = StraceAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        print("\n💡 下一步建议:")
        print("   1. 查看分析结果中的反调试检测")
        print("   2. 分析网络操作中的API调用")
        print("   3. 查找加密相关的系统调用")
        print("   4. 结合frida-trace进行更深入分析")
    else:
        print("\n💡 如果strace不可用，建议:")
        print("   1. 使用frida-trace替代")
        print("   2. 使用Charles代理抓包")
        print("   3. 进行静态分析")

if __name__ == "__main__":
    main()
