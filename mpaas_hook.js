// 翼支付mPaaS加密Hook脚本
Java.perform(function() {
    console.log("🔐 启动翼支付加密Hook...");
    
    // Hook AES加密
    try {
        var Cipher = Java.use("javax.crypto.Cipher");
        var originalDoFinal = Cipher.doFinal.overload('[B');
        
        originalDoFinal.implementation = function(input) {
            var result = originalDoFinal.call(this, input);
            var algorithm = this.getAlgorithm();
            
            if (algorithm.includes("AES")) {
                console.log("\n🔑 [AES加密]");
                console.log("算法: " + algorithm);
                console.log("输入: " + bytesToHex(input.slice(0, 32)));
                console.log("输出: " + bytesToHex(result.slice(0, 32)));
            }
            
            return result;
        };
        
        console.log("✅ AES Hook成功");
    } catch(e) {
        console.log("❌ AES Hook失败: " + e);
    }
    
    // Hook Base64编码
    try {
        var Base64 = Java.use("android.util.Base64");
        var originalEncode = Base64.encodeToString.overload('[B', 'int');
        
        originalEncode.implementation = function(input, flags) {
            var result = originalEncode.call(this, input, flags);
            
            if (input.length > 16) {
                console.log("\n📝 [Base64编码]");
                console.log("长度: " + input.length);
                console.log("结果: " + result.substring(0, 50) + "...");
            }
            
            return result;
        };
        
        console.log("✅ Base64 Hook成功");
    } catch(e) {
        console.log("❌ Base64 Hook失败: " + e);
    }
});

function bytesToHex(bytes) {
    var hex = "";
    for (var i = 0; i < Math.min(bytes.length, 32); i++) {
        hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2);
    }
    return hex;
}