#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据收集助手
帮助用户收集和验证Hook获取的数据
"""

import json
import re
import base64
from datetime import datetime

class DataCollector:
    def __init__(self):
        self.collected_data = []
        print("📊 翼支付数据收集助手")
        print("=" * 50)
    
    def parse_frida_output(self, frida_log):
        """解析Frida输出日志"""
        print("🔍 解析Frida输出...")
        
        # 查找AES密钥
        key_pattern = r'密钥[:\s]*([0-9a-fA-F]{64})'
        keys = re.findall(key_pattern, frida_log)
        
        # 查找IV
        iv_pattern = r'IV[:\s]*([0-9a-fA-F]{32})'
        ivs = re.findall(iv_pattern, frida_log)
        
        # 查找解密结果
        json_pattern = r'解密结果[:\s]*(\{.*?\})'
        jsons = re.findall(json_pattern, frida_log, re.DOTALL)
        
        print(f"   找到 {len(keys)} 个AES密钥")
        print(f"   找到 {len(ivs)} 个IV")
        print(f"   找到 {len(jsons)} 个JSON结果")
        
        return keys, ivs, jsons
    
    def interactive_collect(self):
        """交互式数据收集"""
        print("\n🎯 交互式数据收集模式")
        print("请按照Hook输出复制粘贴相关数据")
        
        while True:
            print("\n" + "="*50)
            print("📋 数据收集 - 第{}组".format(len(self.collected_data) + 1))
            
            # 收集原始data字段
            data_field = input("\n请粘贴原始data字段 (Base64): ").strip()
            if not data_field:
                break
            
            # 收集AES密钥
            aes_key = input("请粘贴AES密钥 (64位十六进制): ").strip()
            if not aes_key:
                print("❌ AES密钥不能为空")
                continue
            
            # 验证密钥格式
            if not re.match(r'^[0-9a-fA-F]{64}$', aes_key):
                print("❌ AES密钥格式错误，应为64位十六进制")
                continue
            
            # 收集IV (可选)
            iv = input("请粘贴IV (32位十六进制，可选): ").strip()
            if iv and not re.match(r'^[0-9a-fA-F]{32}$', iv):
                print("❌ IV格式错误，应为32位十六进制")
                continue
            
            # 收集解密结果 (如果有)
            decrypted_json = input("请粘贴解密后的JSON (可选): ").strip()
            
            # 收集其他信息
            key_field = input("请粘贴key字段 (Base64，可选): ").strip()
            sign_field = input("请粘贴sign字段 (可选): ").strip()
            url = input("请输入API URL (可选): ").strip()
            
            # 保存数据
            data_entry = {
                'timestamp': datetime.now().isoformat(),
                'data_field': data_field,
                'aes_key': aes_key,
                'iv': iv if iv else None,
                'decrypted_json': decrypted_json if decrypted_json else None,
                'key_field': key_field if key_field else None,
                'sign_field': sign_field if sign_field else None,
                'url': url if url else None
            }
            
            self.collected_data.append(data_entry)
            print(f"✅ 第{len(self.collected_data)}组数据已保存")
            
            # 立即验证解密
            self.verify_decryption(data_entry)
            
            # 询问是否继续
            cont = input("\n是否继续收集下一组数据? (y/n): ").strip().lower()
            if cont != 'y':
                break
        
        # 保存所有数据
        self.save_collected_data()
    
    def verify_decryption(self, data_entry):
        """验证解密"""
        print(f"\n🔓 验证解密...")
        
        try:
            from Crypto.Cipher import AES
            from Crypto.Util.Padding import unpad
            
            # 解码数据
            data_bytes = base64.b64decode(data_entry['data_field'])
            aes_key = bytes.fromhex(data_entry['aes_key'])
            
            # 确定IV
            if data_entry['iv']:
                iv = bytes.fromhex(data_entry['iv'])
                encrypted_data = data_bytes
            else:
                iv = data_bytes[:16]
                encrypted_data = data_bytes[16:]
            
            # AES解密
            cipher = AES.new(aes_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除填充
            try:
                unpadded = unpad(decrypted, 16)
                json_str = unpadded.decode('utf-8')
                json_data = json.loads(json_str)
                
                print("✅ 解密成功!")
                print("📄 解密结果:")
                print(json.dumps(json_data, ensure_ascii=False, indent=2))
                
                # 更新数据条目
                data_entry['verified_decryption'] = json_data
                data_entry['decryption_status'] = 'success'
                
                return True
                
            except Exception as e:
                print(f"❌ 解密失败: {e}")
                data_entry['decryption_status'] = 'failed'
                data_entry['decryption_error'] = str(e)
                return False
                
        except ImportError:
            print("⚠️ 缺少pycryptodome库，无法验证解密")
            print("请运行: pip install pycryptodome")
            return False
        except Exception as e:
            print(f"❌ 验证过程出错: {e}")
            return False
    
    def save_collected_data(self):
        """保存收集的数据"""
        if not self.collected_data:
            print("📭 没有数据需要保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"collected_data_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.collected_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 数据已保存到: {filename}")
            print(f"📊 总共收集了 {len(self.collected_data)} 组数据")
            
            # 统计成功解密的数量
            success_count = sum(1 for d in self.collected_data if d.get('decryption_status') == 'success')
            print(f"✅ 成功解密: {success_count}/{len(self.collected_data)}")
            
        except Exception as e:
            print(f"❌ 保存失败: {e}")
    
    def load_and_analyze(self, filename):
        """加载并分析已保存的数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📂 加载了 {len(data)} 组数据")
            
            # 分析密钥模式
            self.analyze_key_patterns(data)
            
            # 分析解密内容
            self.analyze_decrypted_content(data)
            
        except Exception as e:
            print(f"❌ 加载失败: {e}")
    
    def analyze_key_patterns(self, data):
        """分析密钥模式"""
        print(f"\n🔍 分析密钥模式:")
        
        keys = [d['aes_key'] for d in data if d.get('aes_key')]
        
        if len(keys) < 2:
            print("   数据不足，无法分析模式")
            return
        
        # 检查密钥是否重复
        unique_keys = set(keys)
        print(f"   总密钥数: {len(keys)}")
        print(f"   唯一密钥数: {len(unique_keys)}")
        print(f"   重复率: {(len(keys) - len(unique_keys)) / len(keys) * 100:.1f}%")
        
        # 检查密钥前缀模式
        prefixes = [key[:8] for key in keys]
        unique_prefixes = set(prefixes)
        print(f"   唯一前缀数: {len(unique_prefixes)}")
        
        if len(unique_prefixes) < len(keys):
            print("   ⚠️ 发现密钥前缀模式!")
    
    def analyze_decrypted_content(self, data):
        """分析解密内容"""
        print(f"\n📄 分析解密内容:")
        
        successful_decryptions = [d for d in data if d.get('decryption_status') == 'success']
        
        if not successful_decryptions:
            print("   没有成功的解密结果")
            return
        
        print(f"   成功解密数: {len(successful_decryptions)}")
        
        # 分析JSON结构
        json_keys = set()
        for d in successful_decryptions:
            if d.get('verified_decryption'):
                json_keys.update(d['verified_decryption'].keys())
        
        print(f"   发现的JSON字段: {sorted(json_keys)}")
    
    def generate_report(self):
        """生成分析报告"""
        if not self.collected_data:
            print("📭 没有数据可生成报告")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"analysis_report_{timestamp}.md"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("# 翼支付数据分析报告\n\n")
            f.write(f"生成时间: {datetime.now().isoformat()}\n\n")
            f.write(f"## 数据概览\n\n")
            f.write(f"- 总数据组数: {len(self.collected_data)}\n")
            
            success_count = sum(1 for d in self.collected_data if d.get('decryption_status') == 'success')
            f.write(f"- 成功解密: {success_count}\n")
            f.write(f"- 解密成功率: {success_count/len(self.collected_data)*100:.1f}%\n\n")
            
            # 添加详细数据
            for i, d in enumerate(self.collected_data, 1):
                f.write(f"## 数据组 {i}\n\n")
                f.write(f"- 时间: {d['timestamp']}\n")
                f.write(f"- AES密钥: {d['aes_key']}\n")
                f.write(f"- 解密状态: {d.get('decryption_status', 'unknown')}\n")
                
                if d.get('verified_decryption'):
                    f.write(f"- 解密结果: ```json\n{json.dumps(d['verified_decryption'], ensure_ascii=False, indent=2)}\n```\n")
                
                f.write("\n")
        
        print(f"📊 报告已生成: {report_filename}")

def main():
    collector = DataCollector()
    
    print("🎯 选择操作模式:")
    print("   1. 交互式数据收集")
    print("   2. 加载并分析已有数据")
    print("   3. 生成分析报告")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == '1':
        collector.interactive_collect()
        collector.generate_report()
    elif choice == '2':
        filename = input("请输入数据文件名: ").strip()
        collector.load_and_analyze(filename)
    elif choice == '3':
        collector.generate_report()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
