#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付加密配置简化分析工具
"""

import base64

class SimpleCryptoAnalyzer:
    def __init__(self):
        print("🔐 翼支付加密配置分析")
        print("=" * 50)
        
        # 从APK提取的关键信息
        self.mpaas_public_key = """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4RAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----END PUBLIC KEY-----"""
        
        self.crypto_config = {
            "encryption": True,
            "algorithm": "RSA/ECC/SM2=RMK",
            "gateway": "https://spanner.bestpay.com.cn:10081"
        }
    
    def analyze_key_info(self):
        """分析密钥信息"""
        print("\n🔑 mPaaS公钥分析:")
        
        try:
            # 提取Base64部分
            key_lines = self.mpaas_public_key.strip().split('\n')
            key_data = ''.join(key_lines[1:-1])
            key_bytes = base64.b64decode(key_data)
            
            print(f"   📏 密钥长度: {len(key_bytes)} 字节")
            print(f"   🔢 密钥数据: {key_bytes.hex()[:32]}...")
            
            # 分析密钥结构
            if len(key_bytes) == 91:  # 典型的ECC公钥长度
                print(f"   ✅ 椭圆曲线公钥 (ECC)")
                print(f"   🎯 可能是SM2国密算法")
            
        except Exception as e:
            print(f"   ❌ 密钥分析失败: {e}")
    
    def analyze_encryption_flow(self):
        """分析加密流程"""
        print("\n🔄 推断的加密流程:")
        print("   1️⃣ 生成随机AES密钥")
        print("   2️⃣ 用AES加密请求数据 → data字段")
        print("   3️⃣ 用SM2公钥加密AES密钥 → key字段")
        print("   4️⃣ 计算MD5签名 → sign字段")
        print("   5️⃣ 设置加密类型 → encyType: C005")
        print("   6️⃣ 发送到API端点")
    
    def show_key_libraries(self):
        """显示关键库文件"""
        print("\n📚 关键加密库:")
        libraries = [
            ("libmpaas_crypto.so", "mPaaS核心加密库"),
            ("libantssm.so", "蚂蚁国密算法库"),
            ("libTencentSM.so", "腾讯国密实现"),
            ("libopenssl.so", "OpenSSL标准库"),
            ("libijmDataEncryption_arm64.so", "IJM数据加密")
        ]
        
        for lib, desc in libraries:
            print(f"   📖 {lib}: {desc}")
    
    def generate_hook_script(self):
        """生成Hook脚本"""
        print("\n📝 生成Hook脚本...")
        
        script = '''// 翼支付mPaaS加密Hook脚本
Java.perform(function() {
    console.log("🔐 启动翼支付加密Hook...");
    
    // Hook AES加密
    try {
        var Cipher = Java.use("javax.crypto.Cipher");
        var originalDoFinal = Cipher.doFinal.overload('[B');
        
        originalDoFinal.implementation = function(input) {
            var result = originalDoFinal.call(this, input);
            var algorithm = this.getAlgorithm();
            
            if (algorithm.includes("AES")) {
                console.log("\\n🔑 [AES加密]");
                console.log("算法: " + algorithm);
                console.log("输入: " + bytesToHex(input.slice(0, 32)));
                console.log("输出: " + bytesToHex(result.slice(0, 32)));
            }
            
            return result;
        };
        
        console.log("✅ AES Hook成功");
    } catch(e) {
        console.log("❌ AES Hook失败: " + e);
    }
    
    // Hook Base64编码
    try {
        var Base64 = Java.use("android.util.Base64");
        var originalEncode = Base64.encodeToString.overload('[B', 'int');
        
        originalEncode.implementation = function(input, flags) {
            var result = originalEncode.call(this, input, flags);
            
            if (input.length > 16) {
                console.log("\\n📝 [Base64编码]");
                console.log("长度: " + input.length);
                console.log("结果: " + result.substring(0, 50) + "...");
            }
            
            return result;
        };
        
        console.log("✅ Base64 Hook成功");
    } catch(e) {
        console.log("❌ Base64 Hook失败: " + e);
    }
});

function bytesToHex(bytes) {
    var hex = "";
    for (var i = 0; i < Math.min(bytes.length, 32); i++) {
        hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2);
    }
    return hex;
}'''
        
        with open("mpaas_hook.js", "w", encoding="utf-8") as f:
            f.write(script)
        
        print("   ✅ Hook脚本已保存: mpaas_hook.js")
    
    def generate_decryption_template(self):
        """生成解密模板"""
        print("\n🛠️ 生成解密模板...")
        
        template = '''#!/usr/bin/env python3
# 翼支付解密工具模板

import base64

class BestPayDecryptor:
    def __init__(self):
        # mPaaS公钥
        self.public_key = """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4RAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----END PUBLIC KEY-----"""
    
    def analyze_request(self, data, key, sign):
        """分析加密请求"""
        print(f"🔍 分析加密请求:")
        print(f"data长度: {len(data)}")
        print(f"key长度: {len(key)}")
        print(f"sign: {sign}")
        
        try:
            data_bytes = base64.b64decode(data)
            key_bytes = base64.b64decode(key)
            
            print(f"解码后data长度: {len(data_bytes)}")
            print(f"解码后key长度: {len(key_bytes)}")
            print(f"data前16字节: {data_bytes[:16].hex()}")
            print(f"key前16字节: {key_bytes[:16].hex()}")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")

# 使用示例
if __name__ == "__main__":
    decryptor = BestPayDecryptor()
    
    # 从抓包获取的示例数据
    sample_data = "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA2"
    sample_key = "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOb"
    sample_sign = "751C0392CAB814248DBB7A4B1BCE34CD"
    
    decryptor.analyze_request(sample_data, sample_key, sample_sign)
'''
        
        with open("bestpay_decryptor_template.py", "w", encoding="utf-8") as f:
            f.write(template)
        
        print("   ✅ 解密模板已保存: bestpay_decryptor_template.py")
    
    def show_next_steps(self):
        """显示下一步建议"""
        print("\n🎯 下一步行动建议:")
        print("   1️⃣ 使用Charles抓包获取真实数据")
        print("   2️⃣ 运行Hook脚本: frida -U com.chinatelecom.bestpayclient -l mpaas_hook.js")
        print("   3️⃣ 在翼支付中进入权益商城触发加密")
        print("   4️⃣ 分析Hook输出的AES密钥")
        print("   5️⃣ 使用IDA分析libmpaas_crypto.so")
        print("   6️⃣ 实现完整解密工具")
    
    def run_analysis(self):
        """运行分析"""
        self.analyze_key_info()
        self.analyze_encryption_flow()
        self.show_key_libraries()
        self.generate_hook_script()
        self.generate_decryption_template()
        self.show_next_steps()
        
        print("\n" + "=" * 50)
        print("🎉 分析完成!")
        print("📁 生成文件:")
        print("   - mpaas_hook.js")
        print("   - bestpay_decryptor_template.py")
        print("=" * 50)

def main():
    analyzer = SimpleCryptoAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
