{"project_info": {"package_name": "com.chinatelecom.bestpayclient", "version_code": "109470", "version_name": "10.94.70", "min_sdk": "21", "target_sdk": "30", "compile_sdk": "30", "permissions": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.BLUETOOTH", "android.permission.QUERY_ALL_PACKAGES", "android.permission.ACCESS_NETWORK_STATE", "com.android.launcher.permission.INSTALL_SHORTCUT", "com.android.launcher.permission.UNINSTALL_SHORTCUT", "com.android.launcher.permission.READ_SETTINGS", "android.permission.WAKE_LOCK", "android.permission.RECORD_AUDIO", "android.permission.INTERNET", "android.permission.ACCESS_WIFI_STATE", "android.permission.SYSTEM_ALERT_WINDOW", "android.permission.MODIFY_AUDIO_SETTINGS", "com.chinatelecom.bestpayclient.permission.JPUSH_MESSAGE", "android.permission.RECEIVE_USER_PRESENT", "android.permission.NFC", "android.permission.READ_CONTACTS", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.REQUEST_INSTALL_PACKAGES", "com.android.launcher2.permission.READ_SETTINGS", "com.android.launcher3.permission.READ_SETTINGS", "org.adw.launcher.permission.READ_SETTINGS", "com.htc.launcher.permission.READ_SETTINGS", "com.qihoo360.launcher.permission.READ_SETTINGS", "com.lge.launcher.permission.READ_SETTINGS", "net.qihoo.launcher.permission.READ_SETTINGS", "org.adwfreak.launcher.permission.READ_SETTINGS", "org.adw.launcher_donut.permission.READ_SETTINGS", "com.huawei.launcher3.permission.READ_SETTINGS", "com.fede.launcher.permission.READ_SETTINGS", "com.sec.android.app.twlauncher.settings.READ_SETTINGS", "com.anddoes.launcher.permission.READ_SETTINGS", "com.tencent.qqlauncher.permission.READ_SETTINGS", "com.huawei.launcher2.permission.READ_SETTINGS", "com.android.mylauncher.permission.READ_SETTINGS", "com.ebproductions.android.launcher.permission.READ_SETTINGS", "com.oppo.launcher.permission.READ_SETTINGS", "com.huawei.android.launcher.permission.READ_SETTINGS", "telecom.mdesk.permission.READ_SETTINGS", "dianxin.permission.ACCESS_LAUNCHER_DATA", "com.oppo.launcher.permission.READ_SETTINGS", "android.permission.EXPAND_STATUS_BAR", "com.oppo.launcher.permission.READ_SETTINGS", "android.permission.EXPAND_STATUS_BAR", "android.permission.FOREGROUND_SERVICE", "android.permission.ACTIVITY_RECOGNITION", "android.permission.REORDER_TASKS", "android.permission.REORDER_TASKS", "com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE", "com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE", "com.chinatelecom.bestpayclient.permission.MIPUSH_RECEIVE", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.CAMERA", "android.permission.VIBRATE", "android.permission.FLASHLIGHT", "android.permission.CHANGE_NETWORK_STATE", "android.permission.USE_BIOMETRIC", "android.permission.USE_FINGERPRINT", "org.fidoalliance.uaf.permissions.FIDO_CLIENT", "android.permission.SCHEDULE_EXACT_ALARM", "android.permission.BLUETOOTH_CONNECT", "com.asus.msa.SupplementaryDID.ACCESS", "freemme.permission.msa", "android.permission.POST_NOTIFICATIONS", "android.permission.ACCESS_BACKGROUND_LOCATION", "com.chinatelecom.bestpayclient.permission.PROCESS_PUSH_MSG", "com.chinatelecom.bestpayclient.permission.PUSH_PROVIDER", "com.chinatelecom.bestpayclient.openadsdk.permission.TT_PANGOLIN", "android.permission.CHANGE_WIFI_STATE", "android.permission.READ_SETTINGS", "android.permission.ZTE_HEARTYSERVICE_MANAGEMENT", "com.huawei.android.launcher.permission.CHANGE_BADGE", "com.meizu.flyme.permission.PUSH", "com.meizu.flyme.push.permission.RECEIVE", "com.chinatelecom.bestpayclient.push.permission.MESSAGE", "com.meizu.c2dm.permission.RECEIVE", "com.chinatelecom.bestpayclient.permission.C2D_MESSAGE"], "permission_count": 81, "components": {"activities": 767, "services": 80, "receivers": 42, "providers": 29}}, "file_statistics": {"total_files": 11115, "total_size_mb": 284.22, "file_types": {".properties": 54, ".py": 1, ".xml": 5618, ".txt": 125, ".ftl": 7, ".bin": 9, ".json": 65, ".dat": 6, ".png": 3872, ".data": 7, "": 26, ".jar": 3, ".config": 1, ".bks": 3, ".js": 27, ".ttf": 26, ".model": 10, ".so": 130, ".html": 11, ".czl": 1, ".spx": 1, ".lic": 6, ".param": 1, ".ini": 3, ".tnnmodel": 4, ".tnnproto": 4, ".amr": 3, ".wav": 33, ".mf": 3, ".kotlin_metadata": 575, ".kotlin_builtins": 7, ".kotlin_module": 40, ".version": 64, ".rsa": 1, ".sf": 1, ".pro": 3, ".metainfocfg": 1, ".flywebtrackingservice": 1, ".downgradeapiservice": 1, ".bestgrayfloatservice": 1, ".bestgrayqueryservice": 1, ".bestnetrefreshsessioninterceptor$refreshsessioninterface": 1, ".inetcallbacklistener": 1, ".bestmgsadapterservice": 1, ".bestpermissionservice": 1, ".besthuiyanservice": 1, ".bestsecuritydetectservice": 1, ".identifyverificationservice": 1, ".userabilityusageservice": 1, ".bestpayappinfoservice": 1, ".bestpaychildappsinfoservice": 1, ".bestpaycustomfunctionone": 1, ".bestpaydeviceinfoservice": 1, ".bestpayh5service": 1, ".bestpaynetservice": 1, ".bestpayrouterservice": 1, ".bestpaytoolsservice": 1, ".bestpayuserinfoservice": 1, ".trackbackservice": 1, ".iappupdateservice": 1, ".crashprotect$interceptedexceptiontrackservice": 1, ".crashprotectconfigservice": 1, ".bestinternationalbridgeservice": 1, ".ibestbridgeservice": 1, ".ipullish5service": 1, ".ipullshareservice": 1, ".ipullshopservice": 1, ".ipulluploginservice": 1, ".ipulluppermissionservice": 1, ".mpaascomponentspi": 1, ".chucklogdataservice": 1, ".whiteservice": 1, ".messagebodyreader": 1, ".messagebodywriter": 1, ".providers": 1, ".coroutineexceptionhandler": 1, ".maindispatcherfactory": 1, ".autodiscoverable": 1, ".gz": 1, ".jpg": 14, ".webp": 268, ".gif": 23, ".jpeg": 3, ".ogg": 1, ".mp3": 11, ".vm": 1}}, "dependencies": {"native_libraries": ["libagora-core.so", "libagora-fdkaac.so", "libagora-ffmpeg.so", "libagora-rtc-sdk.so", "libagora-soundtouch.so", "libAkSdk.so", "libAlipayBitmapNative.so", "libandfix.so", "libandfix_x86.so", "libantssm.so", "libAPMUOCPLIB.so", "libapm_bitmaps.so", "libapp.so", "libAPSE_8.0.0.so", "libAPSE_J.so", "libap_bitmaps.so", "libavmdl_lite.so", "libbestpaysheild.so", "libbestpaysheilddispatch.so", "libBifrost.so", "libBugly_Native_idasc.so", "libc++_shared.so", "libcipherdb.so", "libCoreCpt.so", "libcqyh_adConfig.so", "libcrashsdk.so", "libCryptoOperAd.so", "libCtaApiLib.so", "libcvenginelite.so", "libdatabase_sqlcrypto.so", "libdecode1002235b60ba.so", "libdevInfo.so", "libdex2oat10.so", "libdexpatch.so", "libdexpatch_x86.so", "libentryexpro.so", "libenvid-sdk.so", "libfbjni.so", "libflutter.so", "libh264encoder.so", "libhtsfx.so", "libhyphenate.so", "libijm-emulator.so", "libimage_processing_util_jni.so", "libimg-compress.so", "libIpStack.so", "libjdcard.so", "libjdt-idcard-core.so", "libjpeg.so", "libjsengine-api.so", "libjsengine-loadso.so", "libjsengine-platform.so", "libkeyboardGuard.so", "libkyctoolkit.so", "liblinearallocpatch.so", "libliteavsdk.so", "liblivenessdetection_ka_v2.4.7.so", "libmaparmor.so", "libMdSecurity.so", "libmmkv.so", "libmpaascpu.so", "libmpaas_crypto.so", "libnetsecsdk-4.1.3.so", "libnllvm1624349491.so", "libopenssl.so", "libpanglearmor.so", "libpangleflipped.so", "libpatcher.so", "libperf.so", "libPglbizssdk_ml.so", "libpl_droidsonroids_gif.so", "libpps-jni.so", "libqcOpenSSL.so", "libQPlayer.so", "libquickjs-android.so", "libquickjs.so", "librsjni.so", "librsjni_androidx.so", "libRSSupport.so", "libsecsdk.so", "libsgcore.so", "libsgmain.so", "libsgmiddletier.so", "libsgmiddletierso-5.5.53.so", "libsgmisc.so", "libsgmiscso-5.5.9.so", "libsgnocaptcha.so", "libsgnocaptchaso-5.5.8.so", "libsgsecuritybody.so", "libsgsecuritybodyso-5.5.61.so", "libssid_silent_jni.so", "libsta.so", "libstidocr_stream.so", "libstidocr_stream_jni.so", "libstlport_shared.so", "libtanx.so", "libTencentSM.so", "libtobEmbedEncrypt.so", "libtraeimp-rtmp.so", "libttmplayer_lite.so", "libtt_ugen_yoga.so", "libturbojpeg.so", "libturingmfa.so", "libtxffmpeg.so", "libutility.so", "libv8worker-native.so", "libWebViewCore_3.22.2.72.240709175818_7z_uc.so", "libweconvert.so", "libweibosdkcore.so", "libweyuv.so", "libwind.so", "libxmcore.so", "libxnn.so", "libxnnloader.so", "libyoga.so", "libyolov5.so", "libYTCommonLiveness.so", "libYTLiveness.so", "libYTWBCommonLiveness.so", "liby_bbc.so", "libzeus_direct_dex.so", "libzstd.so"], "frameworks": ["android.arch.navigation.navigation-common", "android.arch.navigation.navigation-fragment", "android.arch.navigation.navigation-runtime", "android.arch.navigation.navigation-ui", "android.arch.work.work-runtime", "androidx.activity.activity", "androidx.annotation.annotation-experimental", "androidx.appcompat.appcompat-resources", "androidx.appcompat.appcompat", "androidx.arch.core.core-runtime", "androidx.asynclayoutinflater.asynclayoutinflater", "androidx.camera.camera-camera2", "androidx.camera.camera-core", "androidx.camera.camera-lifecycle", "androidx.camera.camera-video", "androidx.camera.camera-view", "androidx.cardview.cardview", "androidx.coordinatorlayout.coordinatorlayout", "androidx.core.core-ktx", "androidx.core.core", "androidx.cursoradapter.cursoradapter", "androidx.customview.customview", "androidx.databinding.baseAdapters", "androidx.databinding.library", "androidx.documentfile.documentfile", "androidx.drawerlayout.drawerlayout", "androidx.dynamicanimation.dynamicanimation", "androidx.exifinterface.exifinterface", "androidx.fragment.fragment", "androidx.interpolator.interpolator", "androidx.legacy.legacy-support-core-ui", "androidx.legacy.legacy-support-core-utils", "androidx.legacy.legacy-support-v4", "androidx.lifecycle.lifecycle-extensions", "androidx.lifecycle.lifecycle-livedata-core-ktx", "androidx.lifecycle.lifecycle-livedata-core", "androidx.lifecycle.lifecycle-livedata-ktx", "androidx.lifecycle.lifecycle-livedata", "androidx.lifecycle.lifecycle-process", "androidx.lifecycle.lifecycle-runtime-ktx", "androidx.lifecycle.lifecycle-runtime", "androidx.lifecycle.lifecycle-service", "androidx.lifecycle.lifecycle-viewmodel-ktx", "androidx.lifecycle.lifecycle-viewmodel-savedstate", "androidx.lifecycle.lifecycle-viewmodel", "androidx.loader.loader", "androidx.localbroadcastmanager.localbroadcastmanager", "androidx.media.media", "androidx.print.print", "androidx.recyclerview.recyclerview", "androidx.room.room-runtime", "androidx.savedstate.savedstate", "androidx.slidingpanelayout.slidingpanelayout", "androidx.sqlite.sqlite-framework", "androidx.sqlite.sqlite", "androidx.swiperefreshlayout.swiperefreshlayout", "androidx.tracing.tracing", "androidx.transition.transition", "androidx.vectordrawable.vectordrawable-animated", "androidx.vectordrawable.vectordrawable", "androidxedparcelable.versionedparcelable", "androidx.viewpager2.viewpager2", "androidx.viewpager.viewpager", "com.google.android.material.material"], "third_party_sdks": ["HMS Core", "Alipay", "WeChat", "ByteDance", "<PERSON><PERSON><PERSON>", "mPaaS"]}, "features": ["人脸识别", "生物识别", "通讯录访问", "网络访问", "相机功能", "录音功能", "文件存储", "蓝牙功能", "二维码扫描", "OCR识别", "网络定位", "银行卡识别", "GPS定位", "指纹识别", "NFC支付"], "security_analysis": {"encryption_libraries": ["libCryptoOperAd.so", "libdatabase_sqlcrypto.so", "libMdSecurity.so", "libmpaas_crypto.so", "libopenssl.so", "libqcOpenSSL.so", "libsgsecuritybody.so", "libsgsecuritybodyso-5.5.61.so", "libtobEmbedEncrypt.so"], "security_features": ["指纹认证", "生物识别认证"], "potential_risks": ["系统级窗口权限", "外部存储写入权限"]}, "architecture": {"ui_framework": ["Flutter"], "hybrid_technologies": ["H5 Bridge"], "architecture_patterns": ["mPaaS移动开发平台"]}}