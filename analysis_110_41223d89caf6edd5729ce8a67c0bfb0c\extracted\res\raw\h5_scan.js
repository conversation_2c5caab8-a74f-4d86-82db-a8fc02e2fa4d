!function(){var defaultHandler=function(a,i){window.WebViewJavascriptBridge._messageHandler&&window.WebViewJavascriptBridge._messageHandler(a,i)},scanAppCallFunMap={alipay_sc_alert:function(param,callback){"string"==typeof param&&(param=eval("("+param+")"));var buttons=[];if(param.buttons){for(var i=0,len=param.buttons.length;i<len;i++)buttons.push(param.buttons[i].title);param.buttons=buttons}else param.buttons=["确定"];window.AlipayJSBridge.call("showAlert",param,function(a){if(callback){var i,e=a.index;i=param.buttons[e]?param.buttons[e]:"",callback(JSON.stringify({title:i}))}else defaultHandler(a,function(a){})})},alipay_sc_pay:function(param,callback){"string"==typeof param&&(param=eval("("+param+")")),param.tradeNo&&(param.tradeNO=param.tradeNo),window.AlipayJSBridge.call("tradePay",param,function(a){callback?(a&&(a.resultStatus=a.resultCode),callback(JSON.stringify(a))):defaultHandler(a)})},alipay_sc_thirdpay:function(a,i){a&&window.AlipayJSBridge.call("tradePay",{orderStr:a},function(a){if(i)if(a&&(a.resultStatus=a.resultCode),"9000"==a.resultStatus){if("function"==typeof i)try{i(JSON.stringify(a))}catch(a){}}else window.AlipayJSBridge.call("startApp",{appId:"20000003",param:{sourceId:"MobApp",category:"ALL",actionType:"toBillList"}},function(a){}),window.AlipayJSBridge.call("closeWebview");else defaultHandler(a)})},alipay_sc_authcode:function(a,i){window.AlipayJSBridge.call("getThirdPartyAuthcode",{appId:a},function(a){i?i(JSON.stringify(a)):defaultHandler(a)})},alipay_sc_clearcookie:"clearAllCookie",alipay_sc_clientinfo:function(a,i){if(a){var e={};e[a]="",window.AlipayJSBridge.call("getClientInfo",e,function(e){if(i){var t={};t[a]=e[a],i(JSON.stringify(t))}else defaultHandler(e)})}else i()},alipay_sc_exit:function(a,i){window.AlipayJSBridge.call("startApp",{appId:"20000001",param:{actionType:"20000002"}},function(a){i?i(JSON.stringify(a)):defaultHandler(a)})},alipay_sc_sinasso:function(a,i){AlipayJSBridge.call("sinasso",function(a){a&&"object"==typeof a&&(a=JSON.stringify(a)),i(a)})}};window.bridge=window.WebViewJavascriptBridge={init:function(a){self._messageHandler||(self._messageHandler=a)},callHandler:function(a,i,e){if(a&&"string"==typeof a){var t=scanAppCallFunMap[a];if(!t)return;t&&"function"==typeof t?t(i,e):"string"==typeof t&&AlipayJSBridge.call(t,i,e)}},_messageHandler:null},document.addEventListener("AlipayJSBridgeReady",function(){var a=document,i=a.createEvent("Events");i.initEvent("WebViewJavascriptBridgeReady"),i.bridge=WebViewJavascriptBridge,a.dispatchEvent(i)})}();
// do not modify
