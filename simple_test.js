
console.log("🧪 开始Hook测试");

setTimeout(function() {
    console.log("⏰ 延迟执行测试");
    
    Java.perform(function() {
        console.log("✅ Java环境可用");
        
        try {
            var System = Java.use("java.lang.System");
            console.log("✅ 系统类Hook成功");
            
            console.log("🎉 Hook测试完成 - 环境正常");
        } catch(e) {
            console.log("❌ Hook测试失败: " + e);
        }
    });
}, 2000);
