#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Frida Server隐蔽模式工具
修改Frida Server二进制文件名和端口以绕过检测
"""

import subprocess
import time
import os
import random
import string

class FridaServerStealth:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        self.original_name = "frida-server"
        self.stealth_name = None
        self.stealth_port = None
        
        print("🥷 Frida Server隐蔽模式工具")
        print("🎯 修改二进制文件名和端口以绕过检测")
        print("=" * 60)
    
    def run_command(self, cmd, timeout=15):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='ignore')
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def generate_stealth_name(self):
        """生成隐蔽的文件名"""
        # 生成看起来像系统进程的名字
        system_names = [
            "system_server",
            "media_server", 
            "audio_server",
            "camera_server",
            "sensor_service",
            "location_service",
            "network_service",
            "security_service",
            "update_service",
            "backup_service"
        ]
        
        # 随机选择一个基础名字
        base_name = random.choice(system_names)
        
        # 添加随机数字
        random_num = random.randint(10, 99)
        
        self.stealth_name = f"{base_name}_{random_num}"
        print(f"   🎭 生成隐蔽名称: {self.stealth_name}")
        
        return self.stealth_name
    
    def generate_stealth_port(self):
        """生成隐蔽的端口"""
        # 避免使用默认的27042端口
        # 选择一个不常用的高端口
        port_ranges = [
            (28000, 28999),  # 自定义范围1
            (29000, 29999),  # 自定义范围2
            (30000, 30999),  # 自定义范围3
            (31000, 31999),  # 自定义范围4
        ]
        
        port_range = random.choice(port_ranges)
        self.stealth_port = random.randint(port_range[0], port_range[1])
        
        print(f"   🔌 生成隐蔽端口: {self.stealth_port}")
        
        return self.stealth_port
    
    def backup_original_server(self):
        """备份原始Frida Server"""
        print("\n💾 备份原始Frida Server...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ls /data/local/tmp/frida-server")
        if success:
            print("   ✅ 找到原始frida-server")
            
            # 创建备份
            backup_name = f"frida-server.backup.{int(time.time())}"
            success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'cp /data/local/tmp/frida-server /data/local/tmp/{backup_name}'")
            
            if success:
                print(f"   ✅ 备份创建成功: {backup_name}")
                return True
            else:
                print(f"   ❌ 备份创建失败: {stderr}")
                return False
        else:
            print("   ❌ 未找到原始frida-server")
            return False
    
    def create_stealth_server(self):
        """创建隐蔽的Frida Server"""
        print(f"\n🥷 创建隐蔽Frida Server: {self.stealth_name}...")
        
        # 复制并重命名
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'cp /data/local/tmp/frida-server /data/local/tmp/{self.stealth_name}'")
        
        if success:
            print("   ✅ 隐蔽文件创建成功")
            
            # 设置权限
            success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'chmod 755 /data/local/tmp/{self.stealth_name}'")
            
            if success:
                print("   ✅ 权限设置成功")
                return True
            else:
                print(f"   ❌ 权限设置失败: {stderr}")
                return False
        else:
            print(f"   ❌ 隐蔽文件创建失败: {stderr}")
            return False
    
    def stop_original_server(self):
        """停止原始Frida Server"""
        print("\n⏹️ 停止原始Frida Server...")
        
        # 杀死所有frida相关进程
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'pkill -f frida'")
        if success:
            print("   ✅ 原始Frida进程已停止")
        
        time.sleep(2)
        
        # 验证是否停止
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ps | grep frida")
        if not success or not stdout.strip():
            print("   ✅ 确认原始Frida已停止")
            return True
        else:
            print("   ⚠️ 仍有Frida进程运行")
            return False
    
    def start_stealth_server(self):
        """启动隐蔽Frida Server"""
        print(f"\n🚀 启动隐蔽Frida Server (端口: {self.stealth_port})...")
        
        # 启动隐蔽服务器
        cmd = f"{self.adb_cmd} shell su -c 'cd /data/local/tmp && ./{self.stealth_name} -l 0.0.0.0:{self.stealth_port} &'"
        
        try:
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            time.sleep(3)  # 等待启动
            
            if process.poll() is None:
                print("   ✅ 隐蔽Frida Server启动成功")
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"   ❌ 隐蔽Frida Server启动失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"   ❌ 启动失败: {e}")
            return False
    
    def setup_port_forwarding(self):
        """设置端口转发"""
        print(f"\n🔧 设置端口转发: {self.stealth_port}...")
        
        # 清除旧的端口转发
        self.run_command(f"{self.adb_cmd} forward --remove-all")
        
        # 设置新的端口转发
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} forward tcp:{self.stealth_port} tcp:{self.stealth_port}")
        
        if success:
            print("   ✅ 端口转发设置成功")
            return True
        else:
            print(f"   ❌ 端口转发设置失败: {stderr}")
            return False
    
    def test_stealth_connection(self):
        """测试隐蔽连接"""
        print(f"\n🧪 测试隐蔽连接...")
        
        # 使用自定义端口连接
        success, stdout, stderr = self.run_command(f"frida-ps -H 127.0.0.1:{self.stealth_port}")
        
        if success:
            lines = stdout.strip().split('\n')
            process_count = len(lines) - 1
            print(f"   ✅ 隐蔽连接成功！检测到 {process_count} 个进程")
            return True
        else:
            print(f"   ❌ 隐蔽连接失败: {stderr}")
            return False
    
    def create_stealth_script(self):
        """创建隐蔽启动脚本"""
        print("\n📝 创建隐蔽启动脚本...")
        
        script_content = f'''#!/usr/bin/env python3
# 隐蔽Frida连接脚本
# 使用隐蔽的服务器名称和端口

import subprocess
import sys

STEALTH_NAME = "{self.stealth_name}"
STEALTH_PORT = {self.stealth_port}

def connect_stealth_frida(target, script_file=None):
    """连接到隐蔽Frida Server"""
    
    if script_file:
        cmd = f"frida -H 127.0.0.1:{{STEALTH_PORT}} {{target}} -l {{script_file}}"
    else:
        cmd = f"frida -H 127.0.0.1:{{STEALTH_PORT}} {{target}}"
    
    print(f"🥷 使用隐蔽连接: {{cmd}}")
    
    try:
        subprocess.run(cmd, shell=True)
    except KeyboardInterrupt:
        print("\\n⏹️ 隐蔽调试会话结束")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python stealth_connect.py <目标应用> [Hook脚本]")
        print("示例: python stealth_connect.py com.chinatelecom.bestpayclient anti_debug_bypass.js")
        sys.exit(1)
    
    target = sys.argv[1]
    script_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    connect_stealth_frida(target, script_file)
'''
        
        with open("stealth_connect.py", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("   ✅ 隐蔽连接脚本已创建: stealth_connect.py")
    
    def create_restore_script(self):
        """创建恢复脚本"""
        print("\n📝 创建恢复脚本...")
        
        restore_content = f'''#!/usr/bin/env python3
# Frida Server恢复脚本

import subprocess

def restore_original_frida():
    """恢复原始Frida Server"""
    adb_cmd = r".\\android-tools\\platform-tools\\adb.exe"
    
    print("🔄 恢复原始Frida Server...")
    
    # 停止隐蔽服务器
    subprocess.run(f"{{adb_cmd}} shell su -c 'pkill -f {self.stealth_name}'", shell=True)
    
    # 删除隐蔽文件
    subprocess.run(f"{{adb_cmd}} shell su -c 'rm /data/local/tmp/{self.stealth_name}'", shell=True)
    
    # 恢复端口转发
    subprocess.run(f"{{adb_cmd}} forward --remove-all", shell=True)
    subprocess.run(f"{{adb_cmd}} forward tcp:27042 tcp:27042", shell=True)
    
    # 启动原始服务器
    subprocess.run(f"{{adb_cmd}} shell su -c 'cd /data/local/tmp && ./frida-server &'", shell=True)
    
    print("✅ 原始Frida Server已恢复")

if __name__ == "__main__":
    restore_original_frida()
'''
        
        with open("restore_frida.py", "w", encoding="utf-8") as f:
            f.write(restore_content)
        
        print("   ✅ 恢复脚本已创建: restore_frida.py")
    
    def run_stealth_setup(self):
        """运行隐蔽设置"""
        print("🎯 开始Frida Server隐蔽设置...")
        
        # 生成隐蔽参数
        self.generate_stealth_name()
        self.generate_stealth_port()
        
        # 备份原始服务器
        if not self.backup_original_server():
            print("❌ 备份失败，中止操作")
            return False
        
        # 创建隐蔽服务器
        if not self.create_stealth_server():
            print("❌ 隐蔽服务器创建失败")
            return False
        
        # 停止原始服务器
        if not self.stop_original_server():
            print("⚠️ 原始服务器停止可能不完全")
        
        # 启动隐蔽服务器
        if not self.start_stealth_server():
            print("❌ 隐蔽服务器启动失败")
            return False
        
        # 设置端口转发
        if not self.setup_port_forwarding():
            print("❌ 端口转发设置失败")
            return False
        
        # 测试连接
        if not self.test_stealth_connection():
            print("❌ 隐蔽连接测试失败")
            return False
        
        # 创建辅助脚本
        self.create_stealth_script()
        self.create_restore_script()
        
        print("\n" + "=" * 60)
        print("🎉 Frida Server隐蔽设置完成!")
        print(f"🥷 隐蔽名称: {self.stealth_name}")
        print(f"🔌 隐蔽端口: {self.stealth_port}")
        print("\n📁 生成的文件:")
        print("   - stealth_connect.py (隐蔽连接脚本)")
        print("   - restore_frida.py (恢复脚本)")
        print("\n🚀 使用方法:")
        print(f"   python stealth_connect.py com.chinatelecom.bestpayclient anti_debug_bypass.js")
        print("\n🔄 恢复方法:")
        print("   python restore_frida.py")
        print("=" * 60)
        
        return True

def main():
    print("🥷 Frida Server隐蔽模式工具")
    print("⚠️ 警告: 此工具会修改Frida Server配置")
    print("💡 建议: 在测试环境中使用")
    
    confirm = input("\n是否继续设置隐蔽模式? (y/n): ").strip().lower()
    if confirm != 'y':
        print("👋 操作已取消")
        return
    
    stealth = FridaServerStealth()
    success = stealth.run_stealth_setup()
    
    if success:
        print("\n💡 下一步建议:")
        print("   1. 使用隐蔽连接测试反调试绕过")
        print("   2. 运行: python stealth_connect.py com.chinatelecom.bestpayclient anti_debug_bypass.js")
        print("   3. 在手机上操作翼支付应用")
    else:
        print("\n❌ 隐蔽设置失败")
        print("💡 可以尝试运行 restore_frida.py 恢复原始设置")

if __name__ == "__main__":
    main()
