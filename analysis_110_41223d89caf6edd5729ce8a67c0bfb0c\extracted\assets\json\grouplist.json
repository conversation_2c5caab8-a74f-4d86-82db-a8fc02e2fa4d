{"groupList": [{"appAttachDTOList": [{"appId": "801", "appPackageName": "jiaofei1.0.0", "appRecommened": "17500", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800246", "fileSize": "0", "fileType": "4", "fileURL": "bestpay://miniapp?id=2022001200010002", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mEuAam_PAAAyP-zgPCo523.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "手机充值", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mFSARNe2AAAyP-zgPCo787.png", "version": "3.7.4"}, {"appId": "51003800", "appPackageName": "生活交费", "appRecommened": "17000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800619", "fileSize": "0", "fileType": "4", "fileURL": "bestpay://miniapp?id=20**************", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mG6AQlxvAABArOEJB2U401.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "生活缴费", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mHSAYFm3AABArOEJB2U392.png", "version": "4.0.5"}, {"appId": "51007024", "appPackageName": "借钱", "appRecommened": "11000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/personalfin-loan-h5/loan/index.html?v=1", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mIqAF6gQAAA061GJP8c459.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙借钱", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mJiAGjgnAAA061GJP8c802.png", "version": "1.0.0"}, {"appId": "51005400", "appPackageName": "orangePay", "appRecommened": "10200", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "0", "fileType": "2", "fileURL": "https://creditcard.bestpay.com.cn/main.html?v20190906", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mLCADWB2AAAwTr9jcMM725.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "还信用卡", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mLaAd3StAAAwTr9jcMM846.png", "version": "2.5.6"}, {"appId": "481", "appPackageName": "com.bestpay.transferaccount", "appRecommened": "10100", "authLogin": "Y", "checkMd5": "AA4678D8AF5E2FB5BD8187753202868204A5BB1056B3FA0E4901F1A9D9005F5E", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "200", "fileType": "3", "fileURL": "https://ctcdn.bestpay.cn/u1/mepf/mems/upload/vm/app/file/*************.zip", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mNaAO2wuAAAwWQn5xYo528.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "转账", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mNyAP-7MAAAwWQn5xYo920.png", "version": "2.1.6"}, {"appId": "4200", "appPackageName": "insurance-mobile", "appRecommened": "10000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "1_1685980800629", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.tcbx.com.cn/subapps/insurance-h5/index.html#/homes?utm_source=khdicon001&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kAOAZ7KmAAAWamOy9Uc308.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙保", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kAaAKmMuAAAWamOy9Uc278.png", "version": "2.2.3"}, {"appId": "881", "appPackageName": "abc", "appRecommened": "9900", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800244", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/financial/index.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mPSAHyEfAAAbZh-Ma8k947.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙理财", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mP-AbsKCAAAbZh-Ma8k875.png", "version": "3.8.4"}, {"appId": "********", "appPackageName": "com.bestpay", "appRecommened": "9799", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800722", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/health-services-h5/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mV6AbWm3AAAPhcLtfbs079.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "健康服务", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mWKAXGUPAAAPhcLtfbs631.png", "version": "1.0.1"}, {"appId": "51007603", "appPackageName": "红包套餐APP子应用", "appRecommened": "9750", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/telecom-member-h5/rights-prefecture/main.html?hybridVersion=3.0&isTab=0&utm_source=app&utm_medium=ZYY", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mSuAVhcZAAARt6rbQtg576.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "权益专区", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mTKAbwOhAAARt6rbQtg945.png", "version": "1.0.1"}, {"appId": "51012829", "appPackageName": "com.bestpay", "appRecommened": "9701", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800978", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/nearby-shops-h5/alpha-nearby/index.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i06AKq5cAAAOLoT-Rg8222.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "附近好店", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i1OABy0UAAAOLoT-Rg8949.png", "version": "1.0.0"}, {"appId": "51007021", "appPackageName": "党费交纳", "appRecommened": "9700", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800621", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/party-dues-h5/main.html#/partyDuesIndex", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9inSABSr9AAAQpTDDGQ8133.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "党费交纳", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ineAT03FAAAQpTDDGQ8815.png", "version": "1.0.0"}, {"appId": "961", "appPackageName": "com.xindaoapp.miaodaiepay", "appRecommened": "9300", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800245", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/publicstage/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ieKAGlw_AAAUC00cmgw444.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "橙分期", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ieuAemlqAAAUC00cmgw911.png", "version": "2.5.7"}, {"appId": "********", "appPackageName": "com.bestpay", "appRecommened": "9160", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800994", "fileSize": "0", "fileType": "2", "fileURL": "https://bestpaylife.bianwa.com/vbankprod-webapp/locallife/loginall.html?directpage=index.html&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kIuAYpH4AAAOj4R8yU4500.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "团爆款", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kI-ALHsTAAAOj4R8yU4691.png", "version": "1.0.0"}, {"appId": "********", "appPackageName": "京东", "appRecommened": "9120", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800757", "fileSize": "0", "fileType": "2", "fileURL": "https://cloudpayment.dajke.com/#/?code=yizhifu&v=1.1.2&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jNqAYJPcAAAUScoiwjs199.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "京东优选", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jN6AesjjAAAUScoiwjs186.png", "version": "1.1.0"}, {"appId": "2900", "appPackageName": "com.orangePay.common", "appRecommened": "9090", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800245", "fileSize": "0", "fileType": "2", "fileURL": "https://orangebite.bestpay.com.cn/main.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j-iAeUxXAAAO84P10bY802.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙白条", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j-uAQEhdAAAO84P10bY161.png", "version": "4.6.0"}, {"appId": "581", "appPackageName": "com.cienet.android.campuscard", "appRecommened": "9060", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800248", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/campus/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kYeAWAg2AAARpTGYN1E853.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "校园卡", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kYqAatbPAAARpTGYN1E305.png", "version": "3.1.0"}, {"appId": "51012830", "appPackageName": "com.bestpay", "appRecommened": "9020", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800247", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/automake-h5/Travel/index.html#/homepage?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jQyAKImjAAASIFO0g3c453.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "旅游出行", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jRmALA41AAASIFO0g3c324.png", "version": "1.0.0"}, {"appId": "51012742", "appPackageName": "com.bestpay", "appRecommened": "8960", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800878", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/financial/index.html#/fundMain?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jEeAeUAvAAASGGrbZbw408.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "基金", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jEyAQ36eAAASGGrbZbw374.png", "version": "1.0.0"}, {"appId": "********", "appPackageName": "com.bestpay", "appRecommened": "8910", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800621", "fileSize": "0", "fileType": "4", "fileURL": "https://bestpayprod.bianwa.com/vbankprod-webapp/bestpaymorecpv3/login.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9keuAZkHjAAAPj3_uXCk236.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "娱乐充值", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9ke6AJ1amAAAPj3_uXCk659.png", "version": "1.0.0"}, {"appId": "********", "appPackageName": "电影票", "appRecommened": "8860", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800875", "fileSize": "0", "fileType": "2", "fileURL": "https://t.zrfilm.com/eR063aKe?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iqKAHD39AAASM2dsKVc813.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "电影票", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iqWAFu0HAAASM2dsKVc820.png", "version": "1.0.1"}], "groupId": "1909", "groupName": "推荐", "showSeq": "1", "status": "1"}, {"appAttachDTOList": [{"appId": "801", "appPackageName": "jiaofei1.0.0", "appRecommened": "17500", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800246", "fileSize": "0", "fileType": "4", "fileURL": "bestpay://miniapp?id=2022001200010002", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mEuAam_PAAAyP-zgPCo523.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "手机充值", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mFSARNe2AAAyP-zgPCo787.png", "version": "3.7.4"}, {"appId": "51003800", "appPackageName": "生活交费", "appRecommened": "17000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800619", "fileSize": "0", "fileType": "4", "fileURL": "bestpay://miniapp?id=20**************", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mG6AQlxvAABArOEJB2U401.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "生活缴费", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mHSAYFm3AABArOEJB2U392.png", "version": "4.0.5"}, {"appId": "481", "appPackageName": "com.bestpay.transferaccount", "appRecommened": "10100", "authLogin": "Y", "checkMd5": "AA4678D8AF5E2FB5BD8187753202868204A5BB1056B3FA0E4901F1A9D9005F5E", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "200", "fileType": "3", "fileURL": "https://ctcdn.bestpay.cn/u1/mepf/mems/upload/vm/app/file/*************.zip", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mNaAO2wuAAAwWQn5xYo528.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "转账", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mNyAP-7MAAAwWQn5xYo920.png", "version": "2.1.6"}, {"appId": "********", "appPackageName": "com.bestpay", "appRecommened": "9799", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800722", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/health-services-h5/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mV6AbWm3AAAPhcLtfbs079.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "健康服务", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mWKAXGUPAAAPhcLtfbs631.png", "version": "1.0.1"}, {"appId": "51012829", "appPackageName": "com.bestpay", "appRecommened": "9701", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800978", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/nearby-shops-h5/alpha-nearby/index.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i06AKq5cAAAOLoT-Rg8222.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "附近好店", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i1OABy0UAAAOLoT-Rg8949.png", "version": "1.0.0"}, {"appId": "51007021", "appPackageName": "党费交纳", "appRecommened": "9700", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800621", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/party-dues-h5/main.html#/partyDuesIndex", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9inSABSr9AAAQpTDDGQ8133.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "党费交纳", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ineAT03FAAAQpTDDGQ8815.png", "version": "1.0.0"}, {"appId": "51006602", "appPackageName": "话费卡转让", "appRecommened": "9160", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800896", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/ecurrency-cardtransfer-h5/main.html#/resell", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i8aAULQaAAAPoSo-KiA054.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "话费卡转让", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i8mAANFRAAAPoSo-KiA346.png", "version": "1.0.3"}, {"appId": "811", "appPackageName": "com.red", "appRecommened": "9089", "authLogin": "Y", "checkMd5": "A8A8971B7971C752CE45F5D99B4E1BF7594624291FA92A997DD58FD9A91B7C7C", "cornerMarkDesc": "", "envType": "0_1685980800249", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/cash/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i6OAUKhRAAASABN1Wv8176.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "红包", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i6aAaH6SAAASABN1Wv8167.png", "version": "2.0.1"}, {"appId": "581", "appPackageName": "com.cienet.android.campuscard", "appRecommened": "9060", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800248", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/campus/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kYeAWAg2AAARpTGYN1E853.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "校园卡", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kYqAatbPAAARpTGYN1E305.png", "version": "3.1.0"}, {"appId": "51009602", "appPackageName": "乘车码", "appRecommened": "8700", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800249", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/bus-qr-h5/main.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iaeASz0dAAASQ-Dp5es283.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "乘车码", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iaqALTfoAAASQ-Dp5es197.png", "version": "1.0.0"}, {"appId": "51003200", "appPackageName": "加油卡app子应用", "appRecommened": "8200", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800622", "fileSize": "0", "fileType": "2", "fileURL": "https://syt.qianxingniwo.com/mtxyzf/hwpay.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jHGAbOrvAAAR-rFUdoY815.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "加油卡", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jHSAUsPZAAAR-rFUdoY472.png", "version": "2.0.6"}, {"appId": "51006403", "appPackageName": "airticket", "appRecommened": "8000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800622", "fileSize": "0", "fileType": "2", "fileURL": "https://m.ctrip.com/webapp/market-app/wechat/tianyiLoginPageNew?popup=close&h5Container=BEST", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9jBaAQbRuAAAPr-un98E223.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "火车票", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9jBmAA0FkAAAPr-un98E744.png", "version": "1.2.0"}, {"appId": "3800", "appPackageName": "qichepiao_android_1.0.0", "appRecommened": "7000", "authLogin": "Y", "checkMd5": "97B1E06E0994DF2162DDEA72265636F252DFBF2C3400316CB9415C23C4F90432", "cornerMarkDesc": "", "envType": "0_1685980800679", "fileSize": "0", "fileType": "2", "fileURL": "https://m.ctrip.com/webapp/market-app/wechat/tianyiLoginPageNew?popup=close&type=bus&h5Container=BEST", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/49/rBwwImR9pRWAb3hnAAAT88f4IyA970.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "汽票", "appState": "5", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/49/rBwwImR9pR2AEnyDAAAT88f4IyA242.png", "version": "1.0.4"}, {"appId": "51012614", "appPackageName": "飞机票", "appRecommened": "6700", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800622", "fileSize": "0", "fileType": "2", "fileURL": "https://m.ctrip.com/webapp/market-app/wechat/tianyiLoginPageNew?popup=close&type=flight&h5Container=BEST", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iwiALw1vAAAM1CgZJpk302.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "飞机票", "appState": "5", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ixOAeFWZAAAM1CgZJpk314.png", "version": "1.0.0"}, {"appId": "51012621", "appPackageName": "一码通", "appRecommened": "6600", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800250", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/scene-overallcode-h5/main.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kaOASZqqAAANji6m7tQ134.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "一码通", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kaaAdh03AAANji6m7tQ735.png", "version": "1.0.0"}, {"appId": "51012759", "appPackageName": "团费交纳", "appRecommened": "400", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800940", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/handyservice-payment-h5/league-fee/index.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kK2APPg2AAAUAySJass699.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "团费交纳", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kLCAbVNhAAAUAySJass364.png", "version": "1.0.0"}, {"appId": "912", "appPackageName": "cn.com.ctbri.pptoc", "appRecommened": "84", "authLogin": "Y", "checkMd5": "b238f8c99b77fe9788cea558730d3d78", "cornerMarkDesc": "", "envType": "0_1685980800864", "fileSize": "11639", "fileType": "0", "fileURL": "https://ctcdn.bestpay.cn/group2/M00/77/61/rBwwH2CckTaAEeW8ALXf8ZpoY3s870.apk", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/25/00/rBwwImOQgq6AC7XqAAADGCdZ3Z8238.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "公交充值", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/25/00/rBwwImOQgrGAcnSdAAADGCdZ3Z8180.png", "version": "2.1.6"}], "groupId": "1910", "groupName": "便民服务", "showSeq": "2", "status": "1"}, {"appAttachDTOList": [{"appId": "51008402", "appPackageName": "吃喝玩乐", "appRecommened": "9800", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800680", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/mall-shopping-h5/index.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mRCATrDmAAARw1-4WOs387.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "电商购物", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mRWABtMuAAARw1-4WOs924.png", "version": "1.0.0"}, {"appId": "********", "appPackageName": "com.bestpay", "appRecommened": "9160", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800994", "fileSize": "0", "fileType": "2", "fileURL": "https://bestpaylife.bianwa.com/vbankprod-webapp/locallife/loginall.html?directpage=index.html&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kIuAYpH4AAAOj4R8yU4500.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "团爆款", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kI-ALHsTAAAOj4R8yU4691.png", "version": "1.0.0"}, {"appId": "********", "appPackageName": "京东", "appRecommened": "9120", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800757", "fileSize": "0", "fileType": "2", "fileURL": "https://cloudpayment.dajke.com/#/?code=yizhifu&v=1.1.2&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jNqAYJPcAAAUScoiwjs199.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "京东优选", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jN6AesjjAAAUScoiwjs186.png", "version": "1.1.0"}, {"appId": "51012828", "appPackageName": "饿了么外卖", "appRecommened": "9090", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800250", "fileSize": "0", "fileType": "2", "fileURL": "https://bestpay.otosaas.com/waimai_ele/?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iu-ANg60AAAV74s_daU131.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "饿了么外卖", "appState": "5", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ivKAPtl4AAAV74s_daU767.png", "version": "1.1.0"}, {"appId": "********", "appPackageName": "com.bestpay", "appRecommened": "8910", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800621", "fileSize": "0", "fileType": "4", "fileURL": "https://bestpayprod.bianwa.com/vbankprod-webapp/bestpaymorecpv3/login.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9keuAZkHjAAAPj3_uXCk236.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "娱乐充值", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9ke6AJ1amAAAPj3_uXCk659.png", "version": "1.0.0"}, {"appId": "********", "appPackageName": "手机商城(bestmobiles)", "appRecommened": "8900", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800251", "fileSize": "0", "fileType": "2", "fileURL": "https://bestmobiles.guanwangbao.com/yzfEntry.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j0iAVK5eAAAMb-qw1AQ675.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "手机商城", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j0uAH-5UAAAMb-qw1AQ798.png", "version": "2.0.2"}, {"appId": "********", "appPackageName": "电影票", "appRecommened": "8860", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800875", "fileSize": "0", "fileType": "2", "fileURL": "https://t.zrfilm.com/eR063aKe?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iqKAHD39AAASM2dsKVc813.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "电影票", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iqWAFu0HAAASM2dsKVc820.png", "version": "1.0.1"}, {"appId": "51007008", "appPackageName": "网易严选APP子应用", "appRecommened": "8600", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800680", "fileSize": "0", "fileType": "2", "fileURL": "https://cps.you.163.com/track/index.do?unionid=yizhifu&target=http%3a%2f%2fm.you.163.com%2fu%2flogin2%3fcallback%3dhttp%253a%252f%252fm.you.163.com%252f%253fchannel_type%253d1_10001110_10%2526from%253dout_cps_yizhifu_0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kNaAOF7hAAAXp7twEXU805.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "网易严选", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kNmAJYPMAAAXp7twEXU728.png", "version": "1.3.0"}, {"appId": "51012616", "appPackageName": "美团", "appRecommened": "8500", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800623", "fileSize": "0", "fileType": "2", "fileURL": "https://m-sqt.meituan.com/open/bestpay/toLogin?productType=dp_canyin&version=product", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jTSAQ2f_AAAUV6xVw24552.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "美团餐饮", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jTeAIM_9AAAUV6xVw24997.png", "version": "1.0.0"}, {"appId": "51012825", "appPackageName": "com.bestpay", "appRecommened": "8450", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800623", "fileSize": "0", "fileType": "2", "fileURL": "https://m.ourtour.com/index.htm#/market/list/4333?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kgSAJxXLAAAR7i9fS5w469.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "中旅旅行", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kgiABB_iAAAR7i9fS5w639.png", "version": "1.0.0"}, {"appId": "51006805", "appPackageName": "小米", "appRecommened": "8400", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800251", "fileSize": "0", "fileType": "2", "fileURL": "https://m.mi.com/?client_id=180100031058&masid=17412.0056&callapp=0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kUaAeg4tAAASPfrGl4c562.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "小米商城", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kVGAG9a_AAASPfrGl4c890.png", "version": "1.2.0"}, {"appId": "51011002", "appPackageName": "天猫优购", "appRecommened": "8300", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800725", "fileSize": "0", "fileType": "2", "fileURL": "https://yzfmall.huabokeji.com", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j6eATSFpAAASnATiLHM605.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "天猫优购", "appState": "1", "secureKey": "woma<PERSON><PERSON>", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j6qAGIjUAAASnATiLHM870.png", "version": "1.0.0"}, {"appId": "51007025", "appPackageName": "唯品会", "appRecommened": "8100", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800633", "fileSize": "0", "fileType": "2", "fileURL": "https://dmp-data.vip.com/ndl?tra_from=tra%3Aigiritep%3A%3A%3A%3A", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kPaAbjWJAAAWCt5Sulk637.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "唯品会", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kPmAQynsAAAWCt5Sulk711.png", "version": "1.0.0"}], "groupId": "1912", "groupName": "娱乐购物", "showSeq": "3", "status": "1"}, {"appAttachDTOList": [{"appId": "51007024", "appPackageName": "借钱", "appRecommened": "11000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/personalfin-loan-h5/loan/index.html?v=1", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mIqAF6gQAAA061GJP8c459.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙借钱", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mJiAGjgnAAA061GJP8c802.png", "version": "1.0.0"}, {"appId": "51005400", "appPackageName": "orangePay", "appRecommened": "10200", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "0", "fileType": "2", "fileURL": "https://creditcard.bestpay.com.cn/main.html?v20190906", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mLCADWB2AAAwTr9jcMM725.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "还信用卡", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mLaAd3StAAAwTr9jcMM846.png", "version": "2.5.6"}, {"appId": "4200", "appPackageName": "insurance-mobile", "appRecommened": "10000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "1_1685980800629", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.tcbx.com.cn/subapps/insurance-h5/index.html#/homes?utm_source=khdicon001&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kAOAZ7KmAAAWamOy9Uc308.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙保", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kAaAKmMuAAAWamOy9Uc278.png", "version": "2.2.3"}, {"appId": "881", "appPackageName": "abc", "appRecommened": "9900", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800244", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/financial/index.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mPSAHyEfAAAbZh-Ma8k947.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙理财", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mP-AbsKCAAAbZh-Ma8k875.png", "version": "3.8.4"}, {"appId": "961", "appPackageName": "com.xindaoapp.miaodaiepay", "appRecommened": "9300", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800245", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/publicstage/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ieKAGlw_AAAUC00cmgw444.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "橙分期", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9ieuAemlqAAAUC00cmgw911.png", "version": "2.5.7"}, {"appId": "51001200", "appPackageName": "zqkh", "appRecommened": "9250", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800245", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/stock-h5/index.html#/manage/stoMain?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kBiAOFjaAAAP6Du_yis248.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙股票", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kCKAHgE9AAAP6Du_yis279.png", "version": "1.6.3"}, {"appId": "2900", "appPackageName": "com.orangePay.common", "appRecommened": "9090", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800245", "fileSize": "0", "fileType": "2", "fileURL": "https://orangebite.bestpay.com.cn/main.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j-iAeUxXAAAO84P10bY802.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙白条", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j-uAQEhdAAAO84P10bY161.png", "version": "4.6.0"}, {"appId": "1131", "appPackageName": "cnm.77", "appRecommened": "9000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800624", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/tyb/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j8mAF1vOAAARTmORQWw995.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "添益宝", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j8uAXgjaAAARTmORQWw803.png", "version": "2.4.0"}, {"appId": "51012742", "appPackageName": "com.bestpay", "appRecommened": "8960", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800878", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/financial/index.html#/fundMain?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jEeAeUAvAAASGGrbZbw408.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "基金", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jEyAQ36eAAASGGrbZbw374.png", "version": "1.0.0"}, {"appId": "51004600", "appPackageName": "理财电商APP子应用", "appRecommened": "8200", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800625", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/golden/main.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kDmAAUrCAAAQB9DpacE236.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙黄金", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kDyABPViAAAQB9DpacE793.png", "version": "1.4.9"}, {"appId": "51012607", "appPackageName": "活期保", "appRecommened": "200", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800246", "fileSize": "0", "fileType": "2", "fileURL": "bestpay://app?id=881&client_to_page=hqbIndex&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i_WAVNQ0AAATICqtj4g000.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "活期保", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9i_iAXtelAAATICqtj4g570.png", "version": "1.0.0"}, {"appId": "51012798", "appPackageName": "com.bestpay", "appRecommened": "100", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800625", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/tyb/index.html#/billPage?hfbChannelCode=yzfkhd", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kWuAWfeLAAATe0G5Qj0378.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "小赢家活期", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kW6AH2geAAATe0G5Qj0806.png", "version": "1.0.0"}, {"appId": "861", "appPackageName": "111", "appRecommened": "90", "authLogin": "Y", "checkMd5": "4E542C4A2131B717BA76EF7B21CE4135AFB7D260B92F3D902CFA8B11F83E34AC", "cornerMarkDesc": "", "envType": "0_1685980800641", "fileSize": "0", "fileType": "2", "fileURL": "https://wx.tycredit.com/index.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kHGARLIuAAAWhLEg4u8467.png", "isAutoDlOnWifi": "1", "loadMode": "0", "loginIntercept": 1, "appName": "甜橙信用", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9kHOATJcdAAAWhLEg4u8879.png", "version": "2.3.5"}, {"appId": "51012625", "appPackageName": "消费分期", "appRecommened": "50", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800625", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/cf-consumestage-h5/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kTGAddQeAAAQuu8ujbM408.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "消费分期", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kTSAAQw4AAAQuu8ujbM356.png", "version": "1.0.0"}], "groupId": "1919", "groupName": "金融理财", "showSeq": "4", "status": "1"}, {"appAttachDTOList": [{"appId": "51012650", "appPackageName": "本地专属", "appRecommened": "10000", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800942", "fileSize": "0", "fileType": "4", "fileURL": "bestpay://miniapp?id=2022001200010004&page=pages/home/<USER>", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iXyALlpZAAATxWuDrk8747.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "本地频道", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9iX-ANNhAAAATxWuDrk8511.png", "version": "2.0.0"}, {"appId": "51007603", "appPackageName": "红包套餐APP子应用", "appRecommened": "9750", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800620", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/telecom-member-h5/rights-prefecture/main.html?hybridVersion=3.0&isTab=0&utm_source=app&utm_medium=ZYY", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mSuAVhcZAAARt6rbQtg576.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "权益专区", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/47/rBwwImR9mTKAbwOhAAARt6rbQtg945.png", "version": "1.0.1"}, {"appId": "51012785", "appPackageName": "com.bestpay", "appRecommened": "190", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800867", "fileSize": "0", "fileType": "4", "fileURL": "https://h5.bestpay.com.cn/subapps/myprivilege/couponCenter/main.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/25/02/rBwwImOQhT-ABikyAAADB5mp3bg990.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "领券中心", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/25/02/rBwwImOQhUGAAuQhAAADB5mp3bg450.png", "version": "1.0.0"}, {"appId": "51012743", "appPackageName": "com.bestpay", "appRecommened": "180", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800867", "fileSize": "0", "fileType": "4", "fileURL": "https://wap.yinuotech.com/app1/#/?fm=1028&hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jsCAdi2oAAAVq644MGc253.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "手机回收", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jsOAYWTpAAAVq644MGc894.png", "version": "1.0.1"}], "groupId": "1920", "groupName": "绿色普惠", "showSeq": "5", "status": "1"}, {"appAttachDTOList": [{"appId": "51012819", "appPackageName": "com.bestpay", "appRecommened": "9020", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800867", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/nft-h5/index.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j2iAd4dCAAAUL1Vq2Kc768.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "数字藏品", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j2uAPc6NAAAUL1Vq2Kc090.png", "version": "1.0.0"}, {"appId": "51012830", "appPackageName": "com.bestpay", "appRecommened": "9020", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800247", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/automake-h5/Travel/index.html#/homepage?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jQyAKImjAAASIFO0g3c453.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "旅游出行", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jRmALA41AAASIFO0g3c324.png", "version": "1.0.0"}, {"appId": "51012794", "appPackageName": "com.bestpay", "appRecommened": "6500", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800626", "fileSize": "0", "fileType": "2", "fileURL": "https://m.ddky.com/partners/yzfddky/empower.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9isWAT7LSAAAcTA7nQF0965.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "叮当快药", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/44/rBwwImR9isiAUqV8AAAcTA7nQF0471.png", "version": "1.0.0"}, {"appId": "51012824", "appPackageName": "com.bestpay", "appRecommened": "401", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800727", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.cn/subapps/digital-currency-h5/index.html#/digitalwallet?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j4SAC61wAAAVMGW1QmQ923.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "数字人民币", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9j4eAeRo3AAAVMGW1QmQ550.png", "version": "1.0.0"}, {"appId": "51012757", "appPackageName": "com.bestpay", "appRecommened": "401", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800868", "fileSize": "0", "fileType": "2", "fileURL": "https://cashbox.boc.cn/h5/app/bocWallet/index.html", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "1", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group1/M00/07/50/rBwwjGJBCQSATrCgAAAVTSiWPfE248.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "数字人民币(旧）", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group1/M00/07/50/rBwwjGJBCQuAdx0wAAAVTSiWPfE108.png", "version": "1.0.0"}, {"appId": "51002000", "appPackageName": "com.bestpay.customerservice", "appRecommened": "82", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800247", "fileSize": "0", "fileType": "4", "fileURL": "bestpay://miniapp?id=2022001200100001", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jPmAAswMAAAXmKHwKco309.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "客服中心", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/45/rBwwImR9jPyARHIYAAAXmKHwKco730.png", "version": "2.0.1"}, {"appId": "51002400", "appPackageName": "我的店铺", "appRecommened": "74", "authLogin": "Y", "checkMd5": "", "cornerMarkDesc": "", "envType": "0_1685980800635", "fileSize": "0", "fileType": "2", "fileURL": "https://h5.bestpay.com.cn/subapps/businessapp/index.html?hybridVersion=3.0", "hasBradgeTag": false, "hasRedTag": false, "hideAppHead": "0", "hideAppStatus": "0", "hybridVersion": "3.0", "iconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kROAPwiTAAARVcjIEM4370.png", "isAutoDlOnWifi": "0", "loadMode": "0", "loginIntercept": 1, "appName": "我要加盟", "appState": "1", "secureKey": "", "touchIconURL": "https://ctcdn.bestpay.cn/group4/M00/5E/46/rBwwImR9kReAaQs1AAARVcjIEM4431.png", "version": "1.8.6"}], "groupId": "1917", "groupName": "其他", "showSeq": "9", "status": "1"}], "version": "20230606000000"}