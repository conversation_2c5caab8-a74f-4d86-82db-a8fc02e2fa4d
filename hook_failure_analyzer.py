#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hook失败原因分析工具
分析Frida Hook失败的具体原因并提供解决方案
"""

import subprocess
import time
import re
import json
from datetime import datetime

class HookFailureAnalyzer:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        self.analysis_results = {
            "timestamp": datetime.now().isoformat(),
            "device_info": {},
            "app_info": {},
            "frida_info": {},
            "hook_attempts": [],
            "failure_reasons": [],
            "recommendations": []
        }
        
        print("🔍 Hook失败原因分析工具")
        print("🎯 深度分析Frida Hook失败的具体原因")
        print("=" * 60)
    
    def analyze_device_environment(self):
        """分析设备环境"""
        print("\n📱 分析设备环境...")
        
        # 获取设备信息
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell getprop")
        if success:
            props = self.parse_device_properties(stdout)
            self.analysis_results["device_info"] = props
            
            print(f"   📱 设备型号: {props.get('ro.product.model', '未知')}")
            print(f"   🤖 Android版本: {props.get('ro.build.version.release', '未知')}")
            print(f"   🏗️ API级别: {props.get('ro.build.version.sdk', '未知')}")
            print(f"   🔧 架构: {props.get('ro.product.cpu.abi', '未知')}")
            
            # 检查SELinux状态
            selinux_status = props.get('ro.boot.selinux', 'unknown')
            print(f"   🔒 SELinux状态: {selinux_status}")
            
            if selinux_status == 'enforcing':
                self.analysis_results["failure_reasons"].append({
                    "category": "安全限制",
                    "reason": "SELinux处于强制模式",
                    "impact": "可能阻止Frida注入",
                    "solution": "尝试在permissive模式下运行"
                })
        
        # 检查Root状态
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'id'")
        if success and 'uid=0' in stdout:
            print("   ✅ Root权限: 已获取")
        else:
            print("   ❌ Root权限: 未获取")
            self.analysis_results["failure_reasons"].append({
                "category": "权限问题",
                "reason": "设备未Root或Root权限不可用",
                "impact": "无法启动Frida Server",
                "solution": "确保设备已正确Root并授权"
            })
    
    def analyze_app_protection(self):
        """分析应用保护机制"""
        print("\n🛡️ 分析应用保护机制...")
        
        # 检查应用是否在运行
        success, stdout, stderr = self.run_command("frida-ps -U")
        if success:
            if "bestpay" in stdout.lower() or "chinatelecom" in stdout.lower():
                print("   ✅ 翼支付应用: 正在运行")
                
                # 分析进程信息
                lines = stdout.strip().split('\n')
                for line in lines[1:]:  # 跳过标题行
                    if 'bestpay' in line.lower() or 'chinatelecom' in line.lower():
                        parts = line.split()
                        if len(parts) >= 2:
                            pid = parts[0]
                            name = ' '.join(parts[1:])
                            print(f"      进程ID: {pid}, 名称: {name}")
            else:
                print("   ❌ 翼支付应用: 未运行")
                self.analysis_results["failure_reasons"].append({
                    "category": "应用状态",
                    "reason": "目标应用未运行",
                    "impact": "无法进行Hook",
                    "solution": "手动启动翼支付应用"
                })
        
        # 检查应用的反调试特征
        self.check_anti_debug_features()
    
    def check_anti_debug_features(self):
        """检查反调试特征"""
        print("   🔍 检查反调试特征...")
        
        # 检查应用包信息
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell pm dump com.chinatelecom.bestpayclient")
        if success:
            # 检查权限
            if 'android.permission.READ_LOGS' in stdout:
                print("      ⚠️ 应用有日志读取权限 (可能用于检测调试)")
            
            if 'debuggable=true' in stdout:
                print("      ✅ 应用可调试")
            else:
                print("      ⚠️ 应用不可调试 (Release版本)")
                self.analysis_results["failure_reasons"].append({
                    "category": "应用保护",
                    "reason": "应用为Release版本，不可调试",
                    "impact": "增加Hook难度",
                    "solution": "使用更高级的绕过技术"
                })
        
        # 检查SO库中的反调试函数
        self.check_native_protection()
    
    def check_native_protection(self):
        """检查Native层保护"""
        print("   🔍 检查Native层保护...")
        
        # 从APK分析结果中获取SO库信息
        try:
            with open("simple_analysis_110_41223d89caf6edd5729ce8a67c0bfb0c/so_libraries.json", "r", encoding="utf-8") as f:
                so_data = json.load(f)
                
                anti_debug_libs = []
                for lib in so_data.get("all_libraries", []):
                    lib_name = lib["name"].lower()
                    if any(keyword in lib_name for keyword in ['protect', 'guard', 'anti', 'detect', 'ijm']):
                        anti_debug_libs.append(lib["name"])
                
                if anti_debug_libs:
                    print(f"      ⚠️ 发现可能的保护库: {', '.join(anti_debug_libs)}")
                    self.analysis_results["failure_reasons"].append({
                        "category": "Native保护",
                        "reason": f"检测到保护库: {', '.join(anti_debug_libs)}",
                        "impact": "可能检测并阻止Frida",
                        "solution": "使用反反调试技术或模拟器环境"
                    })
                else:
                    print("      ✅ 未发现明显的保护库")
                    
        except FileNotFoundError:
            print("      ⚠️ 无法读取SO库分析结果")
    
    def analyze_frida_environment(self):
        """分析Frida环境"""
        print("\n🔧 分析Frida环境...")
        
        # 检查Frida版本
        success, stdout, stderr = self.run_command("frida --version")
        if success:
            frida_version = stdout.strip()
            print(f"   📦 Frida版本: {frida_version}")
            self.analysis_results["frida_info"]["version"] = frida_version
        
        # 检查Frida Server状态
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ps | grep frida")
        if success and stdout.strip():
            print("   ✅ Frida Server: 正在运行")
            
            # 解析进程信息
            for line in stdout.strip().split('\n'):
                if 'frida-server' in line:
                    parts = line.split()
                    if len(parts) >= 2:
                        pid = parts[1] if parts[0] == 'root' else parts[0]
                        print(f"      进程ID: {pid}")
        else:
            print("   ❌ Frida Server: 未运行")
            self.analysis_results["failure_reasons"].append({
                "category": "Frida环境",
                "reason": "Frida Server未运行",
                "impact": "无法进行Hook",
                "solution": "启动Frida Server"
            })
        
        # 测试Frida连接
        self.test_frida_connection()
    
    def test_frida_connection(self):
        """测试Frida连接"""
        print("   🔍 测试Frida连接...")
        
        success, stdout, stderr = self.run_command("frida-ps -U", timeout=10)
        if success:
            lines = stdout.strip().split('\n')
            process_count = len(lines) - 1  # 减去标题行
            print(f"      ✅ 连接成功，检测到 {process_count} 个进程")
            self.analysis_results["frida_info"]["connection"] = "成功"
            self.analysis_results["frida_info"]["process_count"] = process_count
        else:
            print(f"      ❌ 连接失败: {stderr}")
            self.analysis_results["frida_info"]["connection"] = "失败"
            self.analysis_results["frida_info"]["error"] = stderr
            
            self.analysis_results["failure_reasons"].append({
                "category": "Frida连接",
                "reason": f"连接失败: {stderr}",
                "impact": "无法执行Hook脚本",
                "solution": "检查端口转发和Frida Server状态"
            })
    
    def perform_hook_test(self):
        """执行Hook测试"""
        print("\n🎯 执行Hook测试...")
        
        # 创建测试Hook脚本
        test_script = '''
console.log("🧪 Hook测试脚本启动");

Java.perform(function() {
    console.log("✅ Java环境可用");
    
    try {
        var System = Java.use("java.lang.System");
        console.log("✅ 基础类Hook成功");
        
        var Base64 = Java.use("android.util.Base64");
        console.log("✅ Base64类Hook成功");
        
        console.log("🎉 Hook测试完成 - 环境正常");
    } catch(e) {
        console.log("❌ Hook测试失败: " + e);
    }
});
'''
        
        with open("hook_test.js", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        # 执行测试
        print("   🚀 执行Hook测试脚本...")
        try:
            process = subprocess.Popen(
                "frida -U com.chinatelecom.bestpayclient -l hook_test.js --no-pause",
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待5秒
            try:
                stdout, stderr = process.communicate(timeout=5)
                
                if "Hook测试完成" in stdout:
                    print("      ✅ Hook测试成功")
                    self.analysis_results["hook_attempts"].append({
                        "type": "测试Hook",
                        "result": "成功",
                        "output": stdout
                    })
                else:
                    print("      ❌ Hook测试失败")
                    self.analysis_results["hook_attempts"].append({
                        "type": "测试Hook",
                        "result": "失败",
                        "error": stderr,
                        "output": stdout
                    })
                    
                    # 分析失败原因
                    self.analyze_hook_failure(stdout, stderr)
                    
            except subprocess.TimeoutExpired:
                process.kill()
                print("      ⏰ Hook测试超时")
                self.analysis_results["hook_attempts"].append({
                    "type": "测试Hook",
                    "result": "超时",
                    "reason": "5秒内无响应"
                })
                
        except Exception as e:
            print(f"      ❌ Hook测试执行失败: {e}")
    
    def analyze_hook_failure(self, stdout, stderr):
        """分析Hook失败原因"""
        print("   🔍 分析Hook失败原因...")
        
        failure_patterns = [
            {
                "pattern": r"Process terminated",
                "reason": "应用进程被终止",
                "category": "反调试保护",
                "solution": "应用检测到调试器并自动退出，尝试使用反反调试技术"
            },
            {
                "pattern": r"Failed to attach",
                "reason": "无法附加到进程",
                "category": "权限问题",
                "solution": "检查Root权限和SELinux设置"
            },
            {
                "pattern": r"Unable to find process",
                "reason": "找不到目标进程",
                "category": "应用状态",
                "solution": "确保应用正在运行"
            },
            {
                "pattern": r"ReferenceError.*Java",
                "reason": "Java环境不可用",
                "category": "环境问题",
                "solution": "应用可能还未完全启动或使用了特殊的运行时"
            }
        ]
        
        combined_output = stdout + stderr
        
        for pattern_info in failure_patterns:
            if re.search(pattern_info["pattern"], combined_output, re.IGNORECASE):
                print(f"      🎯 检测到: {pattern_info['reason']}")
                self.analysis_results["failure_reasons"].append({
                    "category": pattern_info["category"],
                    "reason": pattern_info["reason"],
                    "impact": "Hook失败",
                    "solution": pattern_info["solution"]
                })
    
    def generate_recommendations(self):
        """生成建议"""
        print("\n💡 生成解决建议...")
        
        recommendations = []
        
        # 基于失败原因生成建议
        failure_categories = [reason["category"] for reason in self.analysis_results["failure_reasons"]]
        
        if "反调试保护" in failure_categories:
            recommendations.extend([
                "使用Charles代理抓包 (绕过应用层检测)",
                "在模拟器环境中调试 (更容易绕过保护)",
                "使用Xposed + JustTrustMe绕过SSL Pinning",
                "分析libijmDataEncryption_arm64.so反调试机制"
            ])
        
        if "权限问题" in failure_categories:
            recommendations.extend([
                "确保设备已正确Root",
                "检查Magisk或SuperSU权限设置",
                "尝试在ADB shell中手动启动frida-server"
            ])
        
        if "应用状态" in failure_categories:
            recommendations.extend([
                "手动启动翼支付应用并保持前台运行",
                "等待应用完全加载后再进行Hook",
                "尝试Hook应用的子进程"
            ])
        
        if "Frida环境" in failure_categories:
            recommendations.extend([
                "重新下载匹配的frida-server版本",
                "检查端口转发设置 (tcp:27042)",
                "尝试不同的Frida版本"
            ])
        
        # 通用建议
        recommendations.extend([
            "使用静态分析 (IDA Pro分析SO库)",
            "网络层抓包分析 (tcpdump + Wireshark)",
            "查看系统日志获取更多信息 (logcat)"
        ])
        
        self.analysis_results["recommendations"] = recommendations
        
        print("   📋 推荐解决方案:")
        for i, rec in enumerate(recommendations[:8], 1):  # 显示前8个
            print(f"      {i}. {rec}")
    
    def save_analysis_report(self):
        """保存分析报告"""
        report_file = "hook_failure_analysis.json"
        
        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 分析报告已保存: {report_file}")
        
        # 生成简化报告
        self.generate_simple_report()
    
    def generate_simple_report(self):
        """生成简化报告"""
        report_file = "hook_failure_summary.md"
        
        with open(report_file, "w", encoding="utf-8") as f:
            f.write("# Hook失败分析报告\n\n")
            f.write(f"## 分析时间\n{self.analysis_results['timestamp']}\n\n")
            
            f.write("## 设备信息\n")
            device_info = self.analysis_results.get("device_info", {})
            f.write(f"- 设备型号: {device_info.get('ro.product.model', '未知')}\n")
            f.write(f"- Android版本: {device_info.get('ro.build.version.release', '未知')}\n")
            f.write(f"- API级别: {device_info.get('ro.build.version.sdk', '未知')}\n\n")
            
            f.write("## 失败原因\n")
            for reason in self.analysis_results["failure_reasons"]:
                f.write(f"### {reason['category']}\n")
                f.write(f"- **原因**: {reason['reason']}\n")
                f.write(f"- **影响**: {reason['impact']}\n")
                f.write(f"- **解决方案**: {reason['solution']}\n\n")
            
            f.write("## 推荐解决方案\n")
            for i, rec in enumerate(self.analysis_results["recommendations"], 1):
                f.write(f"{i}. {rec}\n")
        
        print(f"💾 简化报告已保存: {report_file}")
    
    def run_command(self, cmd, timeout=30):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def parse_device_properties(self, prop_output):
        """解析设备属性"""
        props = {}
        for line in prop_output.split('\n'):
            if ': [' in line:
                try:
                    key = line.split(': [')[0].strip('[]')
                    value = line.split(': [')[1].strip('[]')
                    props[key] = value
                except:
                    continue
        return props
    
    def run_analysis(self):
        """运行完整分析"""
        self.analyze_device_environment()
        self.analyze_app_protection()
        self.analyze_frida_environment()
        self.perform_hook_test()
        self.generate_recommendations()
        self.save_analysis_report()
        
        print("\n" + "=" * 60)
        print("🎉 Hook失败分析完成!")
        print("📁 生成文件:")
        print("   - hook_failure_analysis.json (详细分析)")
        print("   - hook_failure_summary.md (简化报告)")
        print("   - hook_test.js (测试脚本)")
        print("\n🎯 建议优先尝试Charles代理抓包方案!")
        print("=" * 60)

def main():
    analyzer = HookFailureAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
