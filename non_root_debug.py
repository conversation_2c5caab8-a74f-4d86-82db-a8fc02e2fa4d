#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
非Root设备调试方案
使用其他方法分析翼支付加密
"""

import subprocess
import time
import json
import re

class NonRootDebugger:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        print("📱 非Root设备调试方案")
        print("=" * 50)
    
    def run_command(self, cmd):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def check_device(self):
        """检查设备状态"""
        print("🔍 检查设备状态...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} devices")
        if success and "device" in stdout:
            print("   ✅ 设备连接正常")
            return True
        else:
            print("   ❌ 设备连接失败")
            return False
    
    def capture_network_traffic(self):
        """捕获网络流量"""
        print("\n🌐 开始捕获网络流量...")
        print("💡 请在手机上操作翼支付应用，进入权益商城并刷新页面")
        
        # 启动网络监控
        cmd = f"{self.adb_cmd} shell tcpdump -i any -s 0 -w - | strings"
        
        print("🔍 网络监控已启动，按 Ctrl+C 停止...")
        print("📱 现在请在手机上:")
        print("   1. 打开翼支付应用")
        print("   2. 进入权益商城")
        print("   3. 刷新页面或点击活动")
        print("   4. 观察下方输出...")
        print("-" * 50)
        
        try:
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            captured_data = []
            start_time = time.time()
            
            while time.time() - start_time < 60:  # 监控60秒
                try:
                    line = process.stdout.readline()
                    if line:
                        line = line.strip()
                        if self.is_interesting_data(line):
                            print(f"🎯 发现可疑数据: {line[:100]}...")
                            captured_data.append(line)
                    
                    if process.poll() is not None:
                        break
                        
                except KeyboardInterrupt:
                    print("\n⏹️ 用户停止监控")
                    break
            
            process.terminate()
            return captured_data
            
        except Exception as e:
            print(f"❌ 网络监控失败: {e}")
            return []
    
    def is_interesting_data(self, data):
        """判断是否是感兴趣的数据"""
        keywords = [
            "data", "key", "sign", "encrypt", "decrypt",
            "bestpay", "mapi-h5", "queryActivityInfo",
            "AES", "RSA", "SM2", "base64"
        ]
        
        data_lower = data.lower()
        return any(keyword.lower() in data_lower for keyword in keywords)
    
    def analyze_app_logs(self):
        """分析应用日志"""
        print("\n📋 分析应用日志...")
        
        # 清除旧日志
        self.run_command(f"{self.adb_cmd} logcat -c")
        
        print("🔍 开始监控应用日志...")
        print("📱 请在手机上操作翼支付应用")
        print("-" * 50)
        
        cmd = f"{self.adb_cmd} logcat | findstr -i bestpay"
        
        try:
            process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE, text=True)
            
            start_time = time.time()
            interesting_logs = []
            
            while time.time() - start_time < 30:  # 监控30秒
                try:
                    line = process.stdout.readline()
                    if line:
                        line = line.strip()
                        print(f"📱 {line}")
                        
                        if self.is_crypto_related_log(line):
                            interesting_logs.append(line)
                    
                    if process.poll() is not None:
                        break
                        
                except KeyboardInterrupt:
                    print("\n⏹️ 用户停止日志监控")
                    break
            
            process.terminate()
            return interesting_logs
            
        except Exception as e:
            print(f"❌ 日志监控失败: {e}")
            return []
    
    def is_crypto_related_log(self, log):
        """判断是否是加密相关日志"""
        crypto_keywords = [
            "encrypt", "decrypt", "cipher", "crypto", "aes", "rsa", "sm2",
            "key", "iv", "hash", "sign", "base64"
        ]
        
        log_lower = log.lower()
        return any(keyword in log_lower for keyword in crypto_keywords)
    
    def dump_app_data(self):
        """导出应用数据"""
        print("\n💾 尝试导出应用数据...")
        
        # 检查应用是否可调试
        cmd = f"{self.adb_cmd} shell dumpsys package com.chinatelecom.bestpayclient | findstr debuggable"
        success, stdout, stderr = self.run_command(cmd)
        
        if "debuggable=true" in stdout:
            print("   ✅ 应用可调试")
            
            # 尝试导出数据库
            print("   🔍 查找应用数据库...")
            cmd = f"{self.adb_cmd} shell ls /data/data/com.chinatelecom.bestpayclient/databases/"
            success, stdout, stderr = self.run_command(cmd)
            
            if success and stdout.strip():
                print(f"   📁 发现数据库文件:")
                for db in stdout.strip().split('\n'):
                    print(f"      - {db}")
            else:
                print("   ⚠️ 无法访问应用数据目录")
        else:
            print("   ⚠️ 应用不可调试，无法导出数据")
    
    def monitor_file_system(self):
        """监控文件系统变化"""
        print("\n📁 监控文件系统变化...")
        
        # 监控临时文件
        temp_dirs = [
            "/sdcard/Android/data/com.chinatelecom.bestpayclient/",
            "/data/local/tmp/",
            "/sdcard/Download/"
        ]
        
        for temp_dir in temp_dirs:
            print(f"🔍 检查目录: {temp_dir}")
            cmd = f"{self.adb_cmd} shell ls -la {temp_dir}"
            success, stdout, stderr = self.run_command(cmd)
            
            if success:
                files = stdout.strip().split('\n')
                for file_line in files:
                    if any(keyword in file_line.lower() for keyword in ['crypto', 'key', 'encrypt', 'cache']):
                        print(f"   🎯 发现可疑文件: {file_line}")
    
    def alternative_analysis(self):
        """替代分析方法"""
        print("\n🔧 替代分析方法...")
        
        print("💡 由于设备限制，建议以下方法:")
        print("   1. 使用网络抓包工具 (如Charles、Burp Suite)")
        print("   2. 在路由器层面抓包分析")
        print("   3. 使用Xposed框架 (需要Root)")
        print("   4. 分析APK文件进行静态分析")
        
        print("\n📋 网络抓包设置:")
        print("   1. 在手机WiFi设置中配置代理")
        print("   2. 代理地址: 电脑IP")
        print("   3. 代理端口: 8080 (Charles) 或 8080 (Burp)")
        print("   4. 安装证书以抓取HTTPS流量")
        
        print("\n🎯 关键URL监控:")
        print("   - https://mapi-h5.bestpay.com.cn/gapi/equitymall/client/Activity/queryActivityInfo")
        print("   - 寻找包含 data、key、sign 字段的POST请求")
    
    def run_analysis(self):
        """运行完整分析"""
        if not self.check_device():
            return
        
        print("\n🎯 选择分析方法:")
        print("   1. 分析应用日志")
        print("   2. 监控文件系统")
        print("   3. 查看替代方案")
        print("   4. 全部执行")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1' or choice == '4':
            self.analyze_app_logs()
        
        if choice == '2' or choice == '4':
            self.monitor_file_system()
            
        if choice == '3' or choice == '4':
            self.alternative_analysis()
        
        print("\n" + "=" * 50)
        print("🎉 分析完成!")
        print("💡 如果没有获得理想结果，建议使用网络抓包工具")
        print("=" * 50)

def main():
    debugger = NonRootDebugger()
    debugger.run_analysis()

if __name__ == "__main__":
    main()
