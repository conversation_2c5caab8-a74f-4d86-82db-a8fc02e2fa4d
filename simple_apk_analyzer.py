#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化APK分析工具
不依赖Java，直接分析APK内容
"""

import os
import zipfile
import re
import json
import xml.etree.ElementTree as ET
from pathlib import Path

class SimpleAPKAnalyzer:
    def __init__(self, apk_path):
        self.apk_path = apk_path
        self.apk_name = Path(apk_path).stem
        self.output_dir = f"simple_analysis_{self.apk_name}"
        
        print(f"🔍 简化APK分析工具")
        print(f"📱 目标APK: {apk_path}")
        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)
    
    def extract_and_analyze(self):
        """解压并分析APK"""
        print(f"📂 解压APK文件...")
        
        os.makedirs(self.output_dir, exist_ok=True)
        extract_dir = os.path.join(self.output_dir, "extracted")
        
        try:
            with zipfile.ZipFile(self.apk_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            print(f"   ✅ APK解压完成: {extract_dir}")
            return extract_dir
            
        except Exception as e:
            print(f"   ❌ APK解压失败: {e}")
            return None
    
    def analyze_manifest(self, extract_dir):
        """分析AndroidManifest.xml"""
        print(f"\n📋 分析AndroidManifest.xml...")
        
        manifest_path = os.path.join(extract_dir, "AndroidManifest.xml")
        if not os.path.exists(manifest_path):
            print(f"   ❌ AndroidManifest.xml不存在")
            return
        
        try:
            # 读取二进制XML文件（需要特殊处理）
            with open(manifest_path, 'rb') as f:
                data = f.read()
            
            # 搜索文本字符串
            text_strings = []
            current_string = b""
            
            for byte in data:
                if 32 <= byte <= 126:  # 可打印ASCII字符
                    current_string += bytes([byte])
                else:
                    if len(current_string) > 3:
                        text_strings.append(current_string.decode('ascii'))
                    current_string = b""
            
            # 分析提取的字符串
            package_name = None
            permissions = []
            activities = []
            
            for string in text_strings:
                if 'com.chinatelecom' in string and not package_name:
                    package_name = string
                elif 'permission' in string.lower():
                    permissions.append(string)
                elif 'activity' in string.lower() or 'Activity' in string:
                    activities.append(string)
            
            print(f"   📦 可能的包名: {package_name}")
            print(f"   🔐 权限数量: {len(permissions)}")
            print(f"   📱 活动数量: {len(activities)}")
            
            # 保存分析结果
            manifest_analysis = {
                "package_name": package_name,
                "permissions": permissions[:20],  # 前20个
                "activities": activities[:20],    # 前20个
                "all_strings": text_strings[:100]  # 前100个字符串
            }
            
            with open(os.path.join(self.output_dir, "manifest_analysis.json"), 'w', encoding='utf-8') as f:
                json.dump(manifest_analysis, f, indent=2, ensure_ascii=False)
            
        except Exception as e:
            print(f"   ❌ Manifest分析失败: {e}")
    
    def analyze_so_libraries(self, extract_dir):
        """分析SO库文件"""
        print(f"\n📚 分析SO库文件...")
        
        so_files = []
        lib_dirs = [
            os.path.join(extract_dir, "lib"),
            os.path.join(extract_dir, "assets")
        ]
        
        for lib_dir in lib_dirs:
            if os.path.exists(lib_dir):
                for root, dirs, files in os.walk(lib_dir):
                    for file in files:
                        if file.endswith('.so'):
                            file_path = os.path.join(root, file)
                            rel_path = os.path.relpath(file_path, extract_dir)
                            file_size = os.path.getsize(file_path)
                            so_files.append({
                                "path": rel_path,
                                "name": file,
                                "size": file_size
                            })
        
        print(f"   📊 找到 {len(so_files)} 个SO库文件:")
        
        # 按大小排序
        so_files.sort(key=lambda x: x['size'], reverse=True)
        
        crypto_related = []
        for so_file in so_files:
            name = so_file['name'].lower()
            if any(keyword in name for keyword in ['crypto', 'ssl', 'encrypt', 'mpaas', 'security']):
                crypto_related.append(so_file)
            
            print(f"      - {so_file['name']} ({so_file['size']/1024:.1f} KB) - {so_file['path']}")
        
        print(f"\n   🔐 加密相关SO库 ({len(crypto_related)}个):")
        for so_file in crypto_related:
            print(f"      ⭐ {so_file['name']} ({so_file['size']/1024:.1f} KB)")
        
        # 保存SO库分析
        with open(os.path.join(self.output_dir, "so_libraries.json"), 'w', encoding='utf-8') as f:
            json.dump({
                "total_count": len(so_files),
                "crypto_related_count": len(crypto_related),
                "all_libraries": so_files,
                "crypto_libraries": crypto_related
            }, f, indent=2, ensure_ascii=False)
    
    def analyze_assets(self, extract_dir):
        """分析assets目录"""
        print(f"\n📁 分析assets目录...")
        
        assets_dir = os.path.join(extract_dir, "assets")
        if not os.path.exists(assets_dir):
            print(f"   ❌ assets目录不存在")
            return
        
        interesting_files = []
        
        for root, dirs, files in os.walk(assets_dir):
            for file in files:
                file_path = os.path.join(root, file)
                rel_path = os.path.relpath(file_path, assets_dir)
                file_size = os.path.getsize(file_path)
                
                # 检查是否是感兴趣的文件
                ext = os.path.splitext(file)[1].lower()
                name = file.lower()
                
                if any(keyword in name for keyword in ['config', 'key', 'cert', 'crypto', 'mpaas']) or \
                   ext in ['.properties', '.json', '.xml', '.txt', '.config']:
                    interesting_files.append({
                        "path": rel_path,
                        "name": file,
                        "size": file_size,
                        "type": "config"
                    })
        
        print(f"   📊 找到 {len(interesting_files)} 个配置文件:")
        for file_info in interesting_files[:20]:  # 显示前20个
            print(f"      - {file_info['name']} ({file_info['size']} bytes) - {file_info['path']}")
        
        # 分析配置文件内容
        self.analyze_config_files(assets_dir, interesting_files)
    
    def analyze_config_files(self, assets_dir, config_files):
        """分析配置文件内容"""
        print(f"   🔍 分析配置文件内容...")
        
        crypto_keywords = [
            'encrypt', 'decrypt', 'cipher', 'crypto', 'aes', 'rsa', 'des',
            'md5', 'sha', 'hash', 'sign', 'key', 'iv', 'base64',
            'mpaas', 'bestpay', 'sm2', 'sm3', 'sm4', 'C005',
            'data', 'encyType', 'fromChannelId'
        ]
        
        config_analysis = []
        
        for file_info in config_files:
            file_path = os.path.join(assets_dir, file_info['path'])
            
            try:
                # 尝试读取文件内容
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                # 搜索关键词
                found_keywords = []
                for keyword in crypto_keywords:
                    if keyword.lower() in content.lower():
                        found_keywords.append(keyword)
                
                if found_keywords:
                    config_analysis.append({
                        "file": file_info['path'],
                        "keywords": found_keywords,
                        "content_preview": content[:500]  # 前500字符
                    })
                    
                    print(f"      🎯 {file_info['name']}: {', '.join(found_keywords)}")
            
            except Exception as e:
                continue
        
        # 保存配置文件分析
        with open(os.path.join(self.output_dir, "config_analysis.json"), 'w', encoding='utf-8') as f:
            json.dump(config_analysis, f, indent=2, ensure_ascii=False)
    
    def search_dex_strings(self, extract_dir):
        """搜索DEX文件中的字符串"""
        print(f"\n🔍 搜索DEX文件中的字符串...")
        
        dex_files = []
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                if file.endswith('.dex'):
                    dex_files.append(os.path.join(root, file))
        
        print(f"   📊 找到 {len(dex_files)} 个DEX文件")
        
        all_strings = []
        crypto_strings = []
        
        crypto_keywords = [
            'encrypt', 'decrypt', 'cipher', 'crypto', 'aes', 'rsa',
            'md5', 'sha', 'hash', 'sign', 'base64',
            'mpaas', 'bestpay', 'sm2', 'sm3', 'sm4',
            'queryActivityInfo', 'mapi-h5.bestpay.com.cn',
            'C005', 'encyType', 'fromChannelId'
        ]
        
        for dex_file in dex_files:
            try:
                with open(dex_file, 'rb') as f:
                    data = f.read()
                
                # 提取字符串
                strings = self.extract_strings_from_binary(data)
                all_strings.extend(strings)
                
                # 查找加密相关字符串
                for string in strings:
                    for keyword in crypto_keywords:
                        if keyword.lower() in string.lower():
                            crypto_strings.append({
                                "string": string,
                                "keyword": keyword,
                                "file": os.path.basename(dex_file)
                            })
                            break
            
            except Exception as e:
                print(f"   ❌ 处理 {dex_file} 失败: {e}")
        
        print(f"   📝 提取字符串总数: {len(all_strings)}")
        print(f"   🔐 加密相关字符串: {len(crypto_strings)}")
        
        # 显示关键字符串
        for item in crypto_strings[:20]:  # 显示前20个
            print(f"      🎯 {item['keyword']}: {item['string'][:100]}...")
        
        # 保存字符串分析
        with open(os.path.join(self.output_dir, "dex_strings.json"), 'w', encoding='utf-8') as f:
            json.dump({
                "total_strings": len(all_strings),
                "crypto_strings_count": len(crypto_strings),
                "crypto_strings": crypto_strings,
                "sample_strings": all_strings[:1000]  # 保存前1000个字符串样本
            }, f, indent=2, ensure_ascii=False)
    
    def extract_strings_from_binary(self, data):
        """从二进制数据中提取字符串"""
        strings = []
        current_string = b""
        
        for byte in data:
            if 32 <= byte <= 126:  # 可打印ASCII字符
                current_string += bytes([byte])
            else:
                if len(current_string) > 4:  # 最少4个字符
                    try:
                        string = current_string.decode('ascii')
                        if len(string) < 200:  # 过滤过长的字符串
                            strings.append(string)
                    except:
                        pass
                current_string = b""
        
        return strings
    
    def generate_summary_report(self):
        """生成总结报告"""
        print(f"\n📋 生成总结报告...")
        
        report_file = os.path.join(self.output_dir, "analysis_summary.md")
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"# 翼支付APK简化分析报告\n\n")
                f.write(f"## 基本信息\n")
                f.write(f"- APK文件: {self.apk_path}\n")
                f.write(f"- 文件大小: {os.path.getsize(self.apk_path) / (1024*1024):.1f} MB\n")
                f.write(f"- 分析输出: {self.output_dir}\n\n")
                
                f.write(f"## 分析文件\n")
                f.write(f"- manifest_analysis.json - AndroidManifest分析\n")
                f.write(f"- so_libraries.json - SO库文件分析\n")
                f.write(f"- config_analysis.json - 配置文件分析\n")
                f.write(f"- dex_strings.json - DEX字符串分析\n\n")
                
                f.write(f"## 关键发现\n")
                f.write(f"### 加密相关SO库\n")
                f.write(f"- 查看 so_libraries.json 中的 crypto_libraries 部分\n")
                f.write(f"- 重点关注包含 crypto、ssl、mpaas 的库文件\n\n")
                
                f.write(f"### 配置文件\n")
                f.write(f"- 查看 config_analysis.json 中包含加密关键词的文件\n")
                f.write(f"- 重点关注 mpaas 相关配置\n\n")
                
                f.write(f"### 字符串分析\n")
                f.write(f"- 查看 dex_strings.json 中的 crypto_strings\n")
                f.write(f"- 寻找 API URL、加密类型、密钥相关字符串\n\n")
                
                f.write(f"## 下一步建议\n")
                f.write(f"1. 安装Java环境后使用JADX进行完整反编译\n")
                f.write(f"2. 重点分析加密相关的SO库文件\n")
                f.write(f"3. 查找mPaaS框架的加密实现\n")
                f.write(f"4. 分析网络请求的构建过程\n")
                f.write(f"5. 寻找C005加密类型的具体实现\n")
            
            print(f"   ✅ 总结报告已生成: {report_file}")
        except Exception as e:
            print(f"   ❌ 生成报告失败: {e}")
    
    def run_analysis(self):
        """运行完整分析"""
        # 解压APK
        extract_dir = self.extract_and_analyze()
        if not extract_dir:
            return False
        
        # 分析各个组件
        self.analyze_manifest(extract_dir)
        self.analyze_so_libraries(extract_dir)
        self.analyze_assets(extract_dir)
        self.search_dex_strings(extract_dir)
        
        # 生成报告
        self.generate_summary_report()
        
        print(f"\n🎉 APK简化分析完成!")
        print(f"📁 查看结果目录: {self.output_dir}")
        print(f"📋 查看总结报告: {os.path.join(self.output_dir, 'analysis_summary.md')}")
        
        return True

def main():
    # 查找APK文件
    apk_files = [f for f in os.listdir('.') if f.endswith('.apk')]
    
    if not apk_files:
        print("❌ 当前目录没有找到APK文件")
        return
    
    apk_path = apk_files[0]  # 使用第一个APK文件
    print(f"🎯 分析APK: {apk_path}")
    
    # 开始分析
    analyzer = SimpleAPKAnalyzer(apk_path)
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
