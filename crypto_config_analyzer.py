#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付加密配置分析工具
基于APK分析结果，深入分析加密机制
"""

import base64
import json
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import ec
from cryptography.hazmat.backends import default_backend

class CryptoConfigAnalyzer:
    def __init__(self):
        print("🔐 翼支付加密配置分析工具")
        print("=" * 60)
        
        # mPaaS网络配置
        self.mpaas_config = {
            "Crypt": True,
            "Algorithm": "RSA/ECC/SM2=RMK",
            "PubKey": """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4RAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----END PUBLIC KEY-----""",
            "GWWhiteList": "https://spanner.bestpay.com.cn:10081"
        }
        
        # 关键SO库
        self.crypto_libraries = [
            "libmpaas_crypto.so",
            "libantssm.so", 
            "libTencentSM.so",
            "libopenssl.so",
            "libCryptoOperAd.so",
            "libijmDataEncryption_arm64.so"
        ]
        
        # API信息
        self.api_info = {
            "base_url": "mapi-h5.bestpay.com.cn",
            "endpoint": "/gapi/equitymall/client/Activity/queryActivityInfo",
            "encryption_type": "C005",
            "channel": "yzf_app"
        }
    
    def analyze_public_key(self):
        """分析公钥信息"""
        print("\n🔑 分析mPaaS公钥...")
        
        try:
            # 解析公钥
            public_key = serialization.load_pem_public_key(
                self.mpaas_config["PubKey"].encode(),
                backend=default_backend()
            )
            
            if isinstance(public_key, ec.EllipticCurvePublicKey):
                curve = public_key.curve
                print(f"   ✅ 椭圆曲线公钥")
                print(f"   📊 曲线类型: {curve.name}")
                print(f"   📏 密钥长度: {curve.key_size} bits")
                
                # 获取公钥坐标
                numbers = public_key.public_numbers()
                print(f"   🔢 X坐标: {hex(numbers.x)}")
                print(f"   🔢 Y坐标: {hex(numbers.y)}")
                
                # 判断是否为国密SM2曲线
                if curve.name == "secp256r1":
                    print(f"   ⚠️ 标准P-256曲线，但配置显示支持SM2")
                    print(f"   💡 可能使用SM2算法但兼容P-256格式")
                
                return True
            else:
                print(f"   ❌ 未知公钥类型: {type(public_key)}")
                return False
                
        except Exception as e:
            print(f"   ❌ 公钥解析失败: {e}")
            return False
    
    def analyze_encryption_flow(self):
        """分析加密流程"""
        print("\n🔄 分析加密流程...")
        
        print("   📋 基于配置推断的加密流程:")
        print("   1️⃣ 客户端生成随机AES密钥")
        print("   2️⃣ 使用AES密钥加密请求数据 → data字段")
        print("   3️⃣ 使用SM2/ECC公钥加密AES密钥 → key字段")
        print("   4️⃣ 计算请求签名(MD5/SM3) → sign字段")
        print("   5️⃣ 添加加密类型标识 → encyType: C005")
        print("   6️⃣ 发送到API端点")
        
        print("\n   🎯 关键参数:")
        print(f"      - 加密类型: {self.api_info['encryption_type']}")
        print(f"      - 渠道标识: {self.api_info['channel']}")
        print(f"      - API端点: {self.api_info['endpoint']}")
        print(f"      - 网关白名单: {self.mpaas_config['GWWhiteList']}")
    
    def analyze_so_libraries(self):
        """分析SO库功能"""
        print("\n📚 分析关键SO库...")
        
        library_functions = {
            "libmpaas_crypto.so": [
                "mPaaS框架核心加密库",
                "可能包含SM2/SM3/SM4国密算法",
                "AES/RSA标准算法实现",
                "密钥管理和签名验证"
            ],
            "libantssm.so": [
                "蚂蚁金服国密算法库",
                "SM2椭圆曲线加密",
                "SM3哈希算法",
                "SM4对称加密"
            ],
            "libTencentSM.so": [
                "腾讯国密算法实现",
                "可能用于备用加密方案",
                "国密算法标准实现"
            ],
            "libijmDataEncryption_arm64.so": [
                "IJM数据加密库",
                "可能用于反调试保护",
                "数据混淆和加密"
            ]
        }
        
        for lib, functions in library_functions.items():
            print(f"\n   📖 {lib}:")
            for func in functions:
                print(f"      - {func}")
    
    def generate_decryption_strategy(self):
        """生成解密策略"""
        print("\n🎯 生成解密策略...")
        
        strategies = [
            {
                "name": "静态分析法",
                "description": "反编译SO库，查找加密函数",
                "steps": [
                    "使用IDA Pro分析libmpaas_crypto.so",
                    "查找SM2/AES相关函数",
                    "分析密钥生成逻辑",
                    "提取硬编码密钥或种子"
                ],
                "difficulty": "高",
                "success_rate": "中"
            },
            {
                "name": "动态Hook法",
                "description": "Hook加密函数获取密钥",
                "steps": [
                    "使用Frida Hook libmpaas_crypto.so",
                    "拦截AES加密函数",
                    "记录密钥和明文数据",
                    "分析密钥生成规律"
                ],
                "difficulty": "中",
                "success_rate": "高"
            },
            {
                "name": "网络抓包法",
                "description": "抓取加密请求分析",
                "steps": [
                    "使用Charles抓取HTTPS请求",
                    "收集多个data/key/sign样本",
                    "分析加密数据规律",
                    "尝试暴力破解或字典攻击"
                ],
                "difficulty": "低",
                "success_rate": "中"
            },
            {
                "name": "模拟器调试法",
                "description": "在模拟器中调试分析",
                "steps": [
                    "使用Genymotion模拟器",
                    "安装Xposed框架",
                    "使用SSL Kill Switch绕过证书绑定",
                    "Hook Java层加密调用"
                ],
                "difficulty": "中",
                "success_rate": "高"
            }
        ]
        
        for i, strategy in enumerate(strategies, 1):
            print(f"\n   {i}️⃣ {strategy['name']}")
            print(f"      📝 {strategy['description']}")
            print(f"      🎯 难度: {strategy['difficulty']} | 成功率: {strategy['success_rate']}")
            print(f"      📋 步骤:")
            for step in strategy['steps']:
                print(f"         - {step}")
    
    def create_hook_script(self):
        """创建针对性Hook脚本"""
        print("\n📝 生成针对性Hook脚本...")
        
        hook_script = '''
// 翼支付mPaaS加密Hook脚本
// 基于APK分析结果定制

Java.perform(function() {
    console.log("🔐 启动翼支付mPaaS加密Hook...");
    
    // Hook mPaaS加密相关类
    try {
        // 1. Hook AES加密
        var Cipher = Java.use("javax.crypto.Cipher");
        var originalDoFinal = Cipher.doFinal.overload('[B');
        
        originalDoFinal.implementation = function(input) {
            var result = originalDoFinal.call(this, input);
            
            var algorithm = this.getAlgorithm();
            if (algorithm.includes("AES")) {
                console.log("\\n🔑 [AES加密] 算法: " + algorithm);
                console.log("输入数据: " + bytesToHex(input));
                console.log("输出数据: " + bytesToHex(result));
                
                // 尝试获取密钥
                try {
                    var key = this.getKey ? this.getKey() : null;
                    if (key) {
                        console.log("AES密钥: " + bytesToHex(key.getEncoded()));
                    }
                } catch(e) {}
            }
            
            return result;
        };
        
        // 2. Hook SM2相关函数
        var ECPublicKey = Java.use("java.security.interfaces.ECPublicKey");
        // ... SM2 Hook代码
        
        // 3. Hook Base64编码
        var Base64 = Java.use("android.util.Base64");
        var originalEncode = Base64.encodeToString.overload('[B', 'int');
        
        originalEncode.implementation = function(input, flags) {
            var result = originalEncode.call(this, input, flags);
            
            if (input.length > 16) {  // 过滤短数据
                console.log("\\n📝 [Base64编码]");
                console.log("原始数据: " + bytesToHex(input));
                console.log("编码结果: " + result);
            }
            
            return result;
        };
        
        console.log("✅ mPaaS Hook脚本加载完成");
        
    } catch(e) {
        console.log("❌ Hook失败: " + e);
    }
});

function bytesToHex(bytes) {
    var hex = "";
    for (var i = 0; i < bytes.length; i++) {
        hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2);
    }
    return hex;
}
'''
        
        # 保存Hook脚本
        with open("mpaas_targeted_hook.js", "w", encoding="utf-8") as f:
            f.write(hook_script)
        
        print("   ✅ Hook脚本已保存: mpaas_targeted_hook.js")
        print("   🚀 使用方法: frida -U com.chinatelecom.bestpayclient -l mpaas_targeted_hook.js")
    
    def create_decryption_tool(self):
        """创建解密工具模板"""
        print("\n🛠️ 创建解密工具模板...")
        
        decryption_tool = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付数据解密工具
基于APK分析结果
"""

import base64
import json
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

class BestPayDecryptor:
    def __init__(self):
        # mPaaS公钥 (从APK提取)
        self.public_key_pem = """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4RAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----END PUBLIC KEY-----"""
    
    def decrypt_data(self, encrypted_data, encrypted_key, sign):
        """
        解密翼支付数据
        
        Args:
            encrypted_data: Base64编码的加密数据
            encrypted_key: Base64编码的加密密钥
            sign: MD5签名
        """
        print(f"🔓 开始解密翼支付数据...")
        print(f"加密数据长度: {len(encrypted_data)}")
        print(f"加密密钥长度: {len(encrypted_key)}")
        print(f"签名: {sign}")
        
        try:
            # 1. 解码Base64
            data_bytes = base64.b64decode(encrypted_data)
            key_bytes = base64.b64decode(encrypted_key)
            
            print(f"解码后数据长度: {len(data_bytes)}")
            print(f"解码后密钥长度: {len(key_bytes)}")
            
            # 2. 解密密钥 (需要私钥，这里是占位符)
            # aes_key = self.decrypt_key_with_sm2(key_bytes)
            
            # 3. 解密数据 (需要AES密钥)
            # decrypted_data = self.decrypt_with_aes(data_bytes, aes_key)
            
            print("⚠️ 需要私钥才能完成解密")
            return None
            
        except Exception as e:
            print(f"❌ 解密失败: {e}")
            return None
    
    def decrypt_key_with_sm2(self, encrypted_key):
        """使用SM2解密AES密钥"""
        # TODO: 实现SM2解密
        pass
    
    def decrypt_with_aes(self, encrypted_data, aes_key):
        """使用AES解密数据"""
        # TODO: 实现AES解密
        pass

# 使用示例
if __name__ == "__main__":
    decryptor = BestPayDecryptor()
    
    # 示例数据 (从抓包获取)
    sample_data = "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA2..."
    sample_key = "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOb..."
    sample_sign = "751C0392CAB814248DBB7A4B1BCE34CD"
    
    result = decryptor.decrypt_data(sample_data, sample_key, sample_sign)
'''
        
        with open("bestpay_decryptor.py", "w", encoding="utf-8") as f:
            f.write(decryption_tool)
        
        print("   ✅ 解密工具模板已保存: bestpay_decryptor.py")
    
    def run_analysis(self):
        """运行完整分析"""
        self.analyze_public_key()
        self.analyze_encryption_flow()
        self.analyze_so_libraries()
        self.generate_decryption_strategy()
        self.create_hook_script()
        self.create_decryption_tool()
        
        print("\n" + "=" * 60)
        print("🎉 加密配置分析完成!")
        print("\n📋 生成的文件:")
        print("   - mpaas_targeted_hook.js (针对性Hook脚本)")
        print("   - bestpay_decryptor.py (解密工具模板)")
        print("\n🎯 下一步建议:")
        print("   1. 使用Charles抓包获取更多样本数据")
        print("   2. 尝试Hook脚本获取AES密钥")
        print("   3. 分析libmpaas_crypto.so获取私钥")
        print("   4. 实现完整的解密工具")
        print("=" * 60)

def main():
    analyzer = CryptoConfigAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()'''
        
        with open("crypto_config_analyzer.py", "w", encoding="utf-8") as f:
            f.write(decryption_tool)
        
        print("   ✅ 解密工具模板已保存: bestpay_decryptor.py")
    
    def run_analysis(self):
        """运行完整分析"""
        self.analyze_public_key()
        self.analyze_encryption_flow()
        self.analyze_so_libraries()
        self.generate_decryption_strategy()
        self.create_hook_script()
        self.create_decryption_tool()
        
        print("\n" + "=" * 60)
        print("🎉 加密配置分析完成!")
        print("\n📋 生成的文件:")
        print("   - mpaas_targeted_hook.js (针对性Hook脚本)")
        print("   - bestpay_decryptor.py (解密工具模板)")
        print("\n🎯 下一步建议:")
        print("   1. 使用Charles抓包获取更多样本数据")
        print("   2. 尝试Hook脚本获取AES密钥")
        print("   3. 分析libmpaas_crypto.so获取私钥")
        print("   4. 实现完整的解密工具")
        print("=" * 60)

def main():
    analyzer = CryptoConfigAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
