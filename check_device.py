#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备连接检查工具
专门用于真机调试环境检查
"""

import subprocess
import sys
import os

def run_command(cmd):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_frida():
    """检查Frida"""
    print("🔍 检查Frida...")
    
    success, stdout, stderr = run_command("frida --version")
    if success:
        version = stdout.strip()
        print(f"   ✅ Frida: {version}")
        return True
    else:
        print("   ❌ Frida未安装")
        print("   💡 运行: pip install frida-tools")
        return False

def check_adb():
    """检查ADB"""
    print("🔍 检查ADB...")
    
    # 检查便携版ADB
    portable_adb = r".\android-tools\platform-tools\adb.exe"
    if os.path.exists(portable_adb):
        print(f"   ✅ 便携版ADB存在: {portable_adb}")
        return portable_adb
    
    # 检查系统ADB
    success, stdout, stderr = run_command("adb version")
    if success:
        print("   ✅ 系统ADB可用")
        return "adb"
    
    print("   ❌ ADB不可用")
    print("   💡 需要下载Android Platform Tools")
    return None

def check_device_connection(adb_cmd):
    """检查设备连接"""
    print("📱 检查设备连接...")
    
    success, stdout, stderr = run_command(f"{adb_cmd} devices")
    if not success:
        print("   ❌ 无法执行adb devices")
        return False
    
    lines = stdout.strip().split('\n')[1:]  # 跳过标题行
    devices = [line for line in lines if line.strip() and '\t' in line]
    
    if not devices:
        print("   ❌ 没有检测到设备")
        print("   💡 请确保:")
        print("      - 手机已连接USB")
        print("      - 已开启USB调试")
        print("      - 已授权此计算机")
        return False
    
    print(f"   ✅ 检测到 {len(devices)} 个设备:")
    for device in devices:
        device_id, status = device.split('\t')
        print(f"      {device_id}: {status}")
        
        if status == "unauthorized":
            print("   ⚠️ 设备未授权，请在手机上点击'始终允许'")
        elif status == "device":
            print("   ✅ 设备连接正常")
    
    return True

def check_target_app(adb_cmd):
    """检查翼支付应用"""
    print("📱 检查翼支付应用...")
    
    # 检查应用是否安装
    success, stdout, stderr = run_command(f"{adb_cmd} shell pm list packages | findstr bestpay")
    if success and stdout.strip():
        print("   ✅ 翼支付应用已安装")
        packages = stdout.strip().split('\n')
        for pkg in packages:
            print(f"      {pkg}")
        return True
    else:
        print("   ❌ 翼支付应用未安装")
        print("   💡 请在手机应用商店安装翼支付")
        return False

def test_frida_connection():
    """测试Frida连接"""
    print("🔧 测试Frida连接...")
    
    success, stdout, stderr = run_command("frida-ps -U")
    if success:
        lines = stdout.strip().split('\n')
        process_count = len(lines) - 1  # 减去标题行
        print(f"   ✅ Frida连接成功，检测到 {process_count} 个进程")
        
        # 检查是否有翼支付进程
        if "bestpay" in stdout.lower():
            print("   🎯 检测到翼支付进程正在运行")
        
        return True
    else:
        print("   ❌ Frida连接失败")
        print(f"   错误: {stderr}")
        return False

def setup_port_forward(adb_cmd):
    """设置端口转发"""
    print("🔧 设置端口转发...")
    
    success, stdout, stderr = run_command(f"{adb_cmd} forward tcp:27042 tcp:27042")
    if success:
        print("   ✅ 端口转发设置成功")
        return True
    else:
        print("   ❌ 端口转发设置失败")
        print(f"   错误: {stderr}")
        return False

def main():
    print("📱 真机调试环境检查")
    print("=" * 40)
    
    all_good = True
    
    # 检查Frida
    if not check_frida():
        all_good = False
    
    print()
    
    # 检查ADB
    adb_cmd = check_adb()
    if not adb_cmd:
        all_good = False
        print("\n❌ 无法继续，需要先配置ADB")
        print("\n💡 解决方案:")
        print("   1. 访问: https://developer.android.com/studio/releases/platform-tools")
        print("   2. 下载 'SDK Platform-Tools for Windows'")
        print("   3. 解压到当前目录的 android-tools 文件夹")
        print("   4. 重新运行此脚本")
        return
    
    print()
    
    # 检查设备连接
    if not check_device_connection(adb_cmd):
        all_good = False
    
    print()
    
    # 检查翼支付应用
    if not check_target_app(adb_cmd):
        all_good = False
    
    print()
    
    # 设置端口转发
    if adb_cmd and not setup_port_forward(adb_cmd):
        all_good = False
    
    print()
    
    # 测试Frida连接
    if not test_frida_connection():
        print("   ⚠️ Frida连接失败，但可能仍可以进行调试")
    
    print("\n" + "=" * 40)
    
    if all_good:
        print("🎉 环境检查完成！所有条件都满足")
        print("\n🚀 下一步:")
        print("   运行: real_device_debug.bat")
        print("   开始Hook调试")
    else:
        print("❌ 环境检查发现问题")
        print("\n🔧 请根据上述提示解决问题后重新检查")
    
    print("=" * 40)

if __name__ == "__main__":
    main()
