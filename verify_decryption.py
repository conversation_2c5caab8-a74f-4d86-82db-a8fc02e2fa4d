#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付解密验证工具
使用Hook获取的密钥验证解密过程
"""

import base64
import json
import argparse
import sys
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

class BestPayDecryptor:
    def __init__(self):
        print("🔓 翼支付解密验证工具")
        print("=" * 50)
    
    def safe_base64_decode(self, data):
        """安全的Base64解码"""
        try:
            missing_padding = len(data) % 4
            if missing_padding:
                data += '=' * (4 - missing_padding)
            return base64.b64decode(data)
        except Exception as e:
            print(f"❌ Base64解码错误: {e}")
            return None
    
    def decrypt_data(self, encrypted_data_b64, aes_key_hex, iv_hex=None):
        """解密data字段"""
        try:
            # 解码输入数据
            encrypted_bytes = self.safe_base64_decode(encrypted_data_b64)
            if encrypted_bytes is None:
                return None
            
            aes_key = bytes.fromhex(aes_key_hex)
            
            print(f"📊 解密参数:")
            print(f"   加密数据长度: {len(encrypted_bytes)} 字节")
            print(f"   AES密钥长度: {len(aes_key)} 字节")
            
            # 确定IV和加密数据
            if iv_hex:
                # 使用提供的IV
                iv = bytes.fromhex(iv_hex)
                encrypted_data = encrypted_bytes
                print(f"   使用提供的IV: {iv.hex()}")
            else:
                # 从数据前16字节提取IV
                iv = encrypted_bytes[:16]
                encrypted_data = encrypted_bytes[16:]
                print(f"   从数据提取IV: {iv.hex()}")
            
            print(f"   IV长度: {len(iv)} 字节")
            print(f"   待解密数据长度: {len(encrypted_data)} 字节")
            
            # AES-256-CBC解密
            cipher = AES.new(aes_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            print(f"   解密后长度: {len(decrypted)} 字节")
            
            # 尝试去除填充
            try:
                unpadded = unpad(decrypted, 16)
                print(f"   去填充后长度: {len(unpadded)} 字节")
                
                # 尝试解析为JSON
                try:
                    json_str = unpadded.decode('utf-8')
                    json_data = json.loads(json_str)
                    
                    print(f"\n✅ 解密成功!")
                    print(f"📄 原始JSON数据:")
                    print(json.dumps(json_data, ensure_ascii=False, indent=2))
                    
                    return json_data
                    
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON解析失败: {e}")
                    print(f"原始文本: {unpadded[:100]}...")
                    return unpadded.decode('utf-8', errors='ignore')
                    
                except UnicodeDecodeError as e:
                    print(f"⚠️ UTF-8解码失败: {e}")
                    print(f"原始字节: {unpadded[:64].hex()}")
                    return unpadded
                    
            except ValueError as e:
                print(f"⚠️ 去填充失败: {e}")
                print(f"可能不需要去填充，原始解密数据:")
                print(f"前64字节: {decrypted[:64].hex()}")
                
                # 尝试直接解析
                try:
                    json_str = decrypted.decode('utf-8').rstrip('\x00')
                    json_data = json.loads(json_str)
                    print(f"✅ 无填充解密成功!")
                    print(json.dumps(json_data, ensure_ascii=False, indent=2))
                    return json_data
                except:
                    return decrypted
                    
        except Exception as e:
            print(f"❌ 解密失败: {e}")
            return None
    
    def analyze_sample_data(self):
        """分析示例数据"""
        print(f"\n🔍 分析示例数据:")
        
        # 使用我们之前分析的示例数据
        sample_data = "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA27VA51QMMQDe/RiVRdj0+UWr1g6Rd28afJX1JNy3SnfyB28swubBraklZQD94z0x5DutIu+yHYLHoSpE7bVt/yvA0zueZIhMeNntIfdduuzDcBL0CLMjU5fdqjWRK14rh9mDQXi2uN5kTTt2jlJ7qHw0c650VeaVRLy5oG/VbFRUKMwgW+NslTTZ9C/ijfOWcznv5nmBnZu1FrQAi2YaIDJ//rGt8DjB7mHaTaw8Kn2kMolMSrxIje8O5F8rDQQCkub9IHiGZ6ivT2d/PsUAsEy2TmjuMqMaq8oifhihr1WRK8v3sXDI1EB4dLGjsv1GRomMNys0nPgwmHB4iKYvJGS3hYFzWLUhyjDiIj+tZNSLp2T1GjJd3AKBdg3d3px1fZXte9g7QeXG1VhvRBuYAU6lIiSt44I/7M9kKoJ//31lhiv3vlcHsi3DNdHNTAMPbRxfHXlcAj0b4ObmQJULFcL+U8T8Es0TZfqgoNnEKeRA"
        
        # 解码并分析结构
        data_bytes = self.safe_base64_decode(sample_data)
        if data_bytes:
            print(f"   数据长度: {len(data_bytes)} 字节")
            print(f"   可能的IV: {data_bytes[:16].hex()}")
            print(f"   加密数据长度: {len(data_bytes[16:])} 字节")
            print(f"   16字节对齐: {'是' if len(data_bytes[16:]) % 16 == 0 else '否'}")
            
            # 提示用户需要真实密钥
            print(f"\n💡 要解密此数据，您需要:")
            print(f"   1. 使用Frida Hook获取真实的AES密钥")
            print(f"   2. 运行: python verify_decryption.py --data '{sample_data}' --key [Hook获取的密钥]")
    
    def test_with_common_keys(self, encrypted_data_b64):
        """使用常见密钥模式测试"""
        print(f"\n🧪 使用常见密钥模式测试:")
        
        # 一些可能的密钥模式（仅用于测试）
        test_keys = [
            "00" * 32,  # 全零密钥
            "ff" * 32,  # 全一密钥
            "0123456789abcdef" * 2,  # 重复模式
        ]
        
        for i, key_hex in enumerate(test_keys):
            print(f"\n   测试密钥 {i+1}: {key_hex[:32]}...")
            result = self.decrypt_data(encrypted_data_b64, key_hex)
            if result and isinstance(result, dict):
                print(f"   ✅ 测试密钥 {i+1} 可能有效!")
                return result
            else:
                print(f"   ❌ 测试密钥 {i+1} 无效")
        
        print(f"\n💡 所有测试密钥都无效，需要真实的Hook密钥")
        return None
    
    def interactive_mode(self):
        """交互模式"""
        print(f"\n🎯 交互式解密模式")
        print(f"请按照提示输入数据...")
        
        while True:
            try:
                print(f"\n" + "="*50)
                data = input("请输入data字段 (Base64): ").strip()
                if not data:
                    break
                
                key = input("请输入AES密钥 (十六进制): ").strip()
                if not key:
                    print("❌ 密钥不能为空")
                    continue
                
                iv = input("请输入IV (十六进制，可选): ").strip()
                if not iv:
                    iv = None
                
                print(f"\n🔄 开始解密...")
                result = self.decrypt_data(data, key, iv)
                
                if result:
                    print(f"\n✅ 解密完成!")
                    
                    # 询问是否保存结果
                    save = input("\n是否保存解密结果到文件? (y/n): ").strip().lower()
                    if save == 'y':
                        filename = f"decrypted_result_{len(data)}.json"
                        with open(filename, 'w', encoding='utf-8') as f:
                            if isinstance(result, dict):
                                json.dump(result, f, ensure_ascii=False, indent=2)
                            else:
                                f.write(str(result))
                        print(f"✅ 结果已保存到: {filename}")
                else:
                    print(f"❌ 解密失败")
                
                # 询问是否继续
                cont = input("\n是否继续解密其他数据? (y/n): ").strip().lower()
                if cont != 'y':
                    break
                    
            except KeyboardInterrupt:
                print(f"\n👋 退出程序")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='翼支付解密验证工具')
    parser.add_argument('--data', help='Base64编码的data字段')
    parser.add_argument('--key', help='十六进制AES密钥')
    parser.add_argument('--iv', help='十六进制IV (可选)')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')
    parser.add_argument('--analyze', '-a', action='store_true', help='分析示例数据')
    parser.add_argument('--test', '-t', action='store_true', help='使用常见密钥测试')
    
    args = parser.parse_args()
    
    decryptor = BestPayDecryptor()
    
    if args.interactive:
        decryptor.interactive_mode()
    elif args.analyze:
        decryptor.analyze_sample_data()
    elif args.data and args.key:
        result = decryptor.decrypt_data(args.data, args.key, args.iv)
        if result:
            print(f"\n🎉 解密成功!")
        else:
            print(f"\n❌ 解密失败")
    elif args.data and args.test:
        decryptor.test_with_common_keys(args.data)
    else:
        print(f"使用方法:")
        print(f"  python verify_decryption.py --data [Base64数据] --key [十六进制密钥]")
        print(f"  python verify_decryption.py --interactive")
        print(f"  python verify_decryption.py --analyze")
        print(f"  python verify_decryption.py --help")

if __name__ == "__main__":
    main()
