var AlipayJSBridge;function Event(e){this.type=e}function PromiseRejectionEvent(e){Event.call(this,e)}Event.prototype={preventDefault:function(){this._preventDefault=!0},isDefaultPrevented:function(){return!!this._preventDefault},stopPropagation:function(){},stopImmediatePropagation:function(){}},PromiseRejectionEvent.prototype=Object.create(Event.prototype),function(){var r=self.__nativeLog__,o=self.__nativeCreateTimer__,t=self.__nativeDeleteTimer__,l=self.__nativeFlushQueue__;function e(){}function n(){return[]}function d(){return"object"==typeof __appxStartupParams&&"framework"===__appxStartupParams.debug}function a(){try{for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];0<t&&(e+=" "),n instanceof Error?e+=n.message+"\n"+n.stack:e+="object"==typeof n?JSON.stringify(n):n}r(e)}catch(e){}}function u(e,t,n){if("function"==typeof t)try{t.apply(e,n)}catch(e){console.error(e),"function"==typeof self.onerror&&self.onerror(e)}}function i(e,t){var n=e[0];if("string"==typeof n)n=new Function(n);else if("function"!=typeof n)return-1;var r=0|e[1],a=Array.prototype.slice.call(e,2);return o(function(){u(self,n,a)},r<0?0:r,t)}r&&(self.__nativeLog__=null),self.__nativeCreateTimer__=null,self.__nativeDeleteTimer__=null,self.__nativeFlushQueue__=null,self.setTimeout=function(){return i(arguments,!1)},self.setInterval=function(){return i(arguments,!0)},self.clearTimeout=self.clearInterval=function(e){t(0|e)},self.close=e,self.postMessage=e,self.performance={clearMarks:e,clearMeasures:e,clearResourceTimings:e,getEntries:n,getEntriesByName:n,getEntriesByType:n,setResourceTimingBufferSize:e,mark:e,measure:e,now:function(){return Date.now()},toJSON:function(){return{}}},self.onerror=function(e){try{a("###############################################"),a(e),a("###############################################")}catch(e){}},r&&(self.console={log:a,info:a,debug:a,warn:a,error:a,exception:a,clear:e,count:e,countReset:e,dir:e,dirxml:e,group:e,groupCollapsed:e,groupEnd:e,profile:e,profileEnd:e,table:e,time:e,timeEnd:e,timeLog:e,timeStamp:e,trace:e,assert:function(){arguments[0]||console.warn("Assertion failed:",Array.prototype.slice.call(arguments).slice(1))}});var s={},p={};function f(e){return new Event(e)}function c(e,t){var n=f(e),r=!1,a=s[e];if(a)for(var o=0;o<a.length;o++)n.data=t,u(a[o],a[o].fn,[n]),n.isDefaultPrevented()&&(r=!0);return!r}var v="https://appx/",g={addEventListener:function(e,t){s[e]||(s[e]=[]);var n={};n.fn=t,s[e].push(n)},removeEventListener:function(e,t){var n=s[e];if(n)for(var r=0;r<n.length;r++)if(t===n[r].fn){n.splice(r,1),0===n.length&&delete s[e];break}},trigger:function(e,t){return!c(e,t)},sendMessageQueue:[],createEvent:f,dispatchEvent:c,location:{_href:v,search:"",hash:"",host:"appx",hostname:"appx",origin:v,pathname:"/",port:"",protocol:"https:",reload:function(){},replace:function(){},assign:function(){}},title:"",origin:v};self.addEventListener=function(e,t){p[e]||(p[e]=[]);var n={};n.fn=t,p[e].push(n)},self.removeEventListener=function(e,t){if(p[e])for(var n=0;n<p[e].length;n++)if(t===p[e][n].fn){p[e].splice(n,1),0===p[e].length&&delete p[e];break}};var _={};g.addEventListener("push",function(e){var t=e.data,n=p.push;if(d()&&console.log("onMessage push",e),n)for(var r=0;r<n.length;r++)e.data=t,u(n[r],n[r].fn,[e])}),g.addEventListener("message",function(e){if(e.data&&"messagePort"!=e.data.type&&!e.data.beforeunload){var t=e.data.data,n=e.data.eventPorts,r=e.data.viewId,a=e.data.pageId||0;n&&n[0]&&(n[0]=(l=r,f=a,(c=n[0]).postMessage=function(e){var t={data:e,type:"messagePort",msgPortId:c.id,viewId:l,pageId:f};AlipayJSBridge.call("postMessage",t)},_["m_"+l]||(_["m_"+l]={}),_["m_"+l]["p_"+f]||(_["m_"+l]["p_"+f]={}),_["m_"+l]["p_"+f][c.id]=c)),d()&&console.log("onMessage eventPorts");var o=p.message;if(o)for(var i=0;i<o.length;i++)e.data=t,e.ports=n,u(o[i],o[i].fn,[e])}else if(e.data&&"messagePort"==e.data.type&&e.data.msgPortId){r=e.data.viewId,a=e.data.pageId||0;if(!_["m_"+r]||!_["m_"+r]["p_"+a])return void console.error("unknown view",e,_);var s=_["m_"+r]["p_"+a][e.data.msgPortId];s&&s.onmessage&&s.onmessage({data:e.data.data})}else console.log("unknown event",e);var l,f,c}),Object.defineProperty(g.location,"href",{get:function(){return g.location._href},set:function(e){if("string"==typeof e){if(e=e.trim(),AlipayJSBridge._trimLocationHref){var t=AlipayJSBridge._trimLocationHref(e);if(t)for(var n in t)"href"!=n&&"function"!=typeof t[n]&&(g.location[n]=t[n])}g.location._href=e}}});var m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function h(e){this.message=e}(h.prototype=new Error).name="InvalidCharacterError",self.btoa=function(e){for(var t,n,r=String(e),a=0,o=m,i="";r.charAt(0|a)||(o="=",a%1);i+=o.charAt(63&t>>8-a%1*8)){if(255<(n=r.charCodeAt(a+=.75)))throw new h("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");t=t<<8|n}return i},self.atob=function(e){var t=String(e).replace(/=+$/,"");if(t.length%4==1)throw new h("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,r,a=0,o=0,i="";r=t.charAt(o++);~r&&(n=a%4?64*n+r:r,a++%4)?i+=String.fromCharCode(255&n>>(-2*a&6)):0)r=m.indexOf(r);return i};var y=Object.prototype.hasOwnProperty;function I(e,t){if(e instanceof ArrayBuffer)return!0;if("object"!=typeof e)return!1;if(500<=t)throw new TypeError("Converting circular structure to JSON");for(var n in e){if(y.call(e,n))if(I(e[n],t+1))return!0}return!1}var b={},w=1;AlipayJSBridge={call:function(e,t,n){if("string"==typeof e){"function"==typeof t?(n=t,t=null):"object"!=typeof t&&(t=null);var r=e+"##"+w++;if("function"==typeof n&&(b[r]=n),t&&t.callbackId){var a={responseId:t.callbackId,responseData:t};delete t.callbackId}else{var o=t||{},i=function(e){var t=null;for(var n in e)if(y.call(e,n)){var r=e[n];I(r,1)&&(delete e[n],null===t&&(t={}),t[n]=r)}return t}(o);a={callbackId:r,handlerName:e,data:o}}var s=t&&"viewId"in t?0|t.viewId:-1;l(e,s,JSON.stringify(a),i)}},_invokeJS:function(e,t,n){var r="string"==typeof e?JSON.parse(e):e;if(1===r.__FastPath__){if(delete r.__FastPath__,"call"===r.msgType){var a="postMessage"===r.func?"message":r.func,o=r.param||{};return r.clientId&&(o.callbackId=r.clientId),t&&(o.pageId=t),n&&(o.viewId=n),g.trigger(a,o)}a=b[r.clientId];return"boolean"==typeof r.keepCallback&&r.keepCallback||delete b[r.clientId],void u(self,a,[r.param])}if(r.responseId){a=b[r.responseId];"boolean"==typeof r.keepCallback&&r.keepCallback||delete b[r.responseId],u(self,a,[r.responseData])}else if(r.handlerName){if(r.data&&r.data.ariver_message){var i=JSON.parse(r.data.ariver_message);(t=r.data.pageId)&&(i.pageId=t),(n=r.data.viewId)&&(i.viewId=n),r.data=i}r.callbackId&&(r.data=r.data||{},r.data.callbackId=r.callbackId);var s=g.trigger(r.handlerName,r.data);return d()&&console.log("dispatchEvent "+r.handlerName+" isDefaultPrevented "+s),s}},_invokeCallback:function(e,t){var n="string"==typeof t?JSON.parse(t):t;u(self,e,[n])}},self.history={length:1},self.origin=g.origin,self.location=g.location,self.document=g,self.window=self,self.onunhandledrejection=e,self.__fire_promise_rejection_event=function(e,t){var n=new PromiseRejectionEvent("unhandledrejection");n.promise=e,n.reason=t,u(self,self.onunhandledrejection,[n]),n.isDefaultPrevented()||console.error("Uncaught",t)}}();