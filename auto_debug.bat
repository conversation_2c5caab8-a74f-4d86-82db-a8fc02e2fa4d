@echo off
chcp 65001 >nul
echo 🚀 翼支付自动调试脚本
echo ================================

echo 📱 第1步: 检查环境...
echo.

REM 检查ADB
adb version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ADB未安装或未添加到PATH
    echo 请安装Android SDK Platform Tools
    pause
    exit /b 1
)
echo ✅ ADB已安装

REM 检查Frida
frida --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Frida未安装
    echo 正在安装Frida...
    pip install frida-tools
    if %errorlevel% neq 0 (
        echo ❌ Frida安装失败
        pause
        exit /b 1
    )
)
echo ✅ Frida已安装

echo.
echo 📱 第2步: 检查设备连接...
adb devices
echo.
echo ⚠️  请确保：
echo    1. 手机已连接并开启USB调试
echo    2. 已授权此计算机进行调试
echo    3. 翼支付应用已安装
echo.
pause

echo.
echo 📱 第3步: 检查翼支付应用...
adb shell pm list packages | findstr bestpay
if %errorlevel% neq 0 (
    echo ❌ 未找到翼支付应用
    echo 请确保已安装翼支付应用
    pause
    exit /b 1
)
echo ✅ 翼支付应用已安装

echo.
echo 📱 第4步: 设置端口转发...
adb forward tcp:27042 tcp:27042
echo ✅ 端口转发已设置

echo.
echo 📱 第5步: 启动Hook调试...
echo.
echo 🔍 选择调试模式:
echo    1. 标准模式 (推荐)
echo    2. 绕过保护模式 (如果应用有反调试)
echo    3. 仅附加模式 (应用已运行)
echo.
set /p choice=请选择模式 (1-3): 

if "%choice%"=="1" (
    echo 🚀 启动标准Hook模式...
    frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause
) else if "%choice%"=="2" (
    echo 🛡️ 启动绕过保护模式...
    frida -U -f com.chinatelecom.bestpayclient -l bypass_detection.js -l bestpay_crypto_hook.js --no-pause
) else if "%choice%"=="3" (
    echo 📎 附加到运行中的应用...
    frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo 🎯 Hook已启动！
echo.
echo 📋 接下来请在手机上：
echo    1. 打开翼支付应用
echo    2. 登录您的账户
echo    3. 进入"权益商城"
echo    4. 刷新页面或点击活动
echo    5. 观察此窗口的输出
echo.
echo 💡 当看到包含AES密钥的输出时，请复制保存！
echo.
pause
