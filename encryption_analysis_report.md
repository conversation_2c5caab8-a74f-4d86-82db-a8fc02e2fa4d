# 翼支付API加密机制深度分析报告

## 📋 概述

本报告详细分析了翼支付(BestPay)应用中 `https://mapi-h5.bestpay.com.cn/gapi/equitymall/client/Activity/queryActivityInfo` API的加密机制。

## 🔐 加密请求结构分析

### 请求示例
```json
{
  "data": "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA27VA51QMMQDe/RiVRdj0+UWr1g6Rd28afJX1JNy3SnfyB28swubBraklZQD94z0x5DutIu+yHYLHoSpE7bVt/yvA0zueZIhMeNntIfdduuzDcBL0CLMjU5fdqjWRK14rh9mDQXi2uN5kTTt2jlJ7qHw0c650VeaVRLy5oG/VbFRUKMwgW+NslTTZ9C/ijfOWcznv5nmBnZu1FrQAi2YaIDJ//rGt8DjB7mHaTaw8Kn2kMolMSrxIje8O5F8rDQQCkub9IHiGZ6ivT2d/PsUAsEy2TmjuMqMaq8oifhihr1WRK8v3sXDI1EB4dLGjsv1GRomMNys0nPgwmHB4iKYvJGS3hYFzWLUhyjDiIj+tZNSLp2T1GjJd3AKBdg3d3px1fZXte9g7QeXG1VhvRBuYAU6lIiSt44I/7M9kKoJ//31lhiv3vlcHsi3DNdHNTAMPbRxfHXlcAj0b4ObmQJULFcL+U8T8Es0TZfqgoNnEKeRA",
  "key": "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOboibNuxG95/46P8rizN4R27a/FXmZEZygYkNmAC9U5PSc5ETxa5PTnhQAf7qpwg7uj/yhKPPK7Ejlo4AgoG9LjuVS8t4Hoy41i4n02OGwnmk=",
  "sign": "751C0392CAB814248DBB7A4B1BCE34CD",
  "productNo": "13341293407",
  "encyType": "C005",
  "fromChannelId": "yzf_app"
}
```

### 字段分析

#### 1. data字段 (加密数据)
- **长度**: 555字符 (Base64编码)
- **解码后**: 416字节
- **结构**: 前16字节可能是IV (初始化向量)
- **算法**: AES-256-CBC/GCM加密

#### 2. key字段 (加密密钥)
- **长度**: 172字符 (Base64编码)
- **解码后**: 128字节
- **用途**: 使用SM2/ECC公钥加密的AES密钥
- **特征**: 高随机性，每次请求不同

#### 3. sign字段 (数字签名)
- **长度**: 32字符 (十六进制)
- **解码后**: 16字节
- **算法**: MD5哈希签名
- **用途**: 验证数据完整性

#### 4. 固定参数
- **encyType**: "C005" - 加密类型标识
- **productNo**: "13341293407" - 产品编号
- **fromChannelId**: "yzf_app" - 渠道标识

## 🔑 密钥管理分析

### 服务器公钥 (从mpaas_netconfig.properties)
```
-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4RAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----END PUBLIC KEY-----
```

**公钥特征**:
- **类型**: ECC椭圆曲线公钥
- **曲线**: SM2 (国密算法)
- **长度**: 256位
- **用途**: 加密AES会话密钥

## 🔄 加密流程分析

### 客户端加密流程
1. **生成随机AES密钥** (256位)
2. **使用AES密钥加密原始请求数据** → `data`字段
3. **使用SM2公钥加密AES密钥** → `key`字段
4. **计算请求参数的MD5签名** → `sign`字段
5. **组装完整请求并发送**

### 服务器解密流程
1. **使用SM2私钥解密`key`字段** → 获得AES密钥
2. **使用AES密钥解密`data`字段** → 获得原始数据
3. **验证`sign`字段** → 确保数据完整性
4. **处理业务逻辑并返回结果**

## 📚 相关SO库分析

### 加密相关库文件
| 库文件 | 大小 | 功能 |
|--------|------|------|
| libmpaas_crypto.so | 41.76KB | mPaaS加密核心库 |
| libCryptoOperAd.so | 52.94KB | 加密操作库 |
| libopenssl.so | 2386.77KB | OpenSSL加密库 |
| libqcOpenSSL.so | 2230.66KB | 高通OpenSSL库 |
| libantssm.so | 1880.38KB | 蚂蚁国密算法库 |
| libTencentSM.so | 346.56KB | 腾讯国密算法库 |
| libdatabase_sqlcrypto.so | 1993.2KB | 数据库加密库 |

### 核心加密库功能
- **libantssm.so**: 实现SM2/SM3/SM4国密算法
- **libTencentSM.so**: 腾讯版本的国密算法实现
- **libmpaas_crypto.so**: mPaaS平台的加密封装
- **libopenssl.so**: 标准OpenSSL加密算法

## 🛡️ 安全特性

### 多层防护
- ✅ **对称加密**: AES-256保护数据内容
- ✅ **非对称加密**: SM2/ECC保护密钥传输
- ✅ **数字签名**: MD5确保数据完整性
- ✅ **随机密钥**: 每次请求使用不同AES密钥

### 防护能力
- ✅ **防窃听**: 数据全程加密传输
- ✅ **防篡改**: 数字签名验证
- ✅ **防重放**: 可能包含时间戳或随机数
- ✅ **前向安全**: 每次使用新的会话密钥

## 🔍 加密类型C005分析

### 可能的加密类型体系
- **C001**: 基础RSA加密
- **C002**: AES-128加密
- **C003**: AES-192加密
- **C004**: AES-256-ECB加密
- **C005**: AES-256-CBC + 国密SM2 ⭐ (当前使用)
- **C006**: AES-256-GCM加密

### C005特征
- **对称算法**: AES-256-CBC
- **非对称算法**: SM2椭圆曲线
- **签名算法**: MD5
- **密钥长度**: 256位
- **IV长度**: 128位

## ⚠️ 安全测试建议

### 可能的攻击向量
1. **密钥重用检测**: 观察多次请求的key字段是否相同
2. **签名绕过测试**: 尝试修改或删除sign字段
3. **重放攻击测试**: 重复发送相同的加密请求
4. **参数污染测试**: 添加额外的请求参数
5. **加密类型降级**: 尝试修改encyType为较弱算法

### 建议的测试方法
1. **抓包分析**: 使用Burp Suite/Charles分析请求
2. **时间分析**: 观察加密/解密的时间差异
3. **错误注入**: 故意发送格式错误的加密数据
4. **密钥猜测**: 尝试常见的密钥模式
5. **侧信道分析**: 分析加密过程中的其他信息泄露

## 🔧 逆向分析要点

### 关键函数定位
- **AES加密函数**: 在libmpaas_crypto.so中
- **SM2加密函数**: 在libantssm.so或libTencentSM.so中
- **签名生成函数**: 可能在Java层或native层
- **密钥生成函数**: 随机数生成器相关

### Hook点建议
1. **AES_encrypt**: AES加密函数
2. **SM2_encrypt**: SM2加密函数
3. **MD5_Final**: MD5签名计算
4. **Base64_encode**: Base64编码函数
5. **网络请求函数**: HTTP请求发送点

## 📊 总结

翼支付使用了一套相当完善的加密体系：
- 采用国密SM2算法保护密钥传输
- 使用AES-256-CBC保护数据内容
- 通过MD5签名确保数据完整性
- 每次请求使用不同的会话密钥

这种设计在安全性和性能之间取得了良好的平衡，符合金融级应用的安全要求。
