#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络抓包分析工具
通过抓包分析翼支付的加密请求
"""

import subprocess
import time
import re
import json

def capture_network_traffic():
    """抓取网络流量"""
    adb_cmd = r".\android-tools\platform-tools\adb.exe"
    
    print("🌐 翼支付网络流量抓包分析")
    print("=" * 50)
    
    print("\n📱 操作指南:")
    print("   1. 确保翼支付应用已打开")
    print("   2. 准备进入权益商城")
    print("   3. 我将开始抓包，然后您在手机上操作")
    print("   4. 刷新权益商城页面或点击活动")
    print("   5. 观察抓包结果")
    
    input("\n按回车开始抓包...")
    
    print("\n🔍 开始网络抓包...")
    print("💡 抓包进行中，请在手机上操作翼支付应用")
    print("📱 进入权益商城并刷新页面...")
    print("-" * 50)
    
    # 使用tcpdump抓包
    cmd = f"{adb_cmd} shell su -c 'tcpdump -i any -s 0 -A host mapi-h5.bestpay.com.cn'"
    
    try:
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE, text=True)
        
        captured_data = []
        start_time = time.time()
        
        print("🎯 监控目标: mapi-h5.bestpay.com.cn")
        print("⏰ 抓包时间: 60秒")
        print("🔍 寻找包含 data、key、sign 的请求...")
        print()
        
        while time.time() - start_time < 60:  # 抓包60秒
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    
                    # 检查是否包含关键信息
                    if any(keyword in line for keyword in ['data', 'key', 'sign', 'queryActivityInfo']):
                        print(f"🎯 发现关键数据: {line[:100]}...")
                        captured_data.append(line)
                    
                    # 检查是否是POST请求
                    if 'POST' in line and 'queryActivityInfo' in line:
                        print(f"🚀 发现目标请求: {line}")
                        captured_data.append(line)
                
                if process.poll() is not None:
                    break
                    
            except KeyboardInterrupt:
                print("\n⏹️ 用户停止抓包")
                break
        
        process.terminate()
        
        print(f"\n📊 抓包完成，共捕获 {len(captured_data)} 条相关数据")
        
        if captured_data:
            print("\n🎯 捕获的关键数据:")
            for i, data in enumerate(captured_data[:10], 1):  # 显示前10条
                print(f"   {i}. {data[:200]}...")
        else:
            print("\n⚠️ 没有捕获到相关数据")
            print("💡 可能的原因:")
            print("   - 应用使用了证书绑定")
            print("   - 网络请求被加密")
            print("   - 需要在应用中触发特定操作")
        
        return captured_data
        
    except Exception as e:
        print(f"❌ 抓包失败: {e}")
        return []

def analyze_logcat():
    """分析系统日志"""
    adb_cmd = r".\android-tools\platform-tools\adb.exe"
    
    print("\n📋 分析系统日志...")
    
    # 清除旧日志
    subprocess.run(f"{adb_cmd} logcat -c", shell=True)
    
    print("🔍 开始监控系统日志...")
    print("📱 请在手机上操作翼支付应用")
    print("-" * 30)
    
    cmd = f"{adb_cmd} logcat -s bestpay:* -s chinatelecom:* -s mpaas:* -s crypto:*"
    
    try:
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE, text=True)
        
        start_time = time.time()
        interesting_logs = []
        
        while time.time() - start_time < 30:  # 监控30秒
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    print(f"📱 {line}")
                    
                    # 检查是否包含加密相关信息
                    if any(keyword in line.lower() for keyword in ['encrypt', 'decrypt', 'crypto', 'aes', 'key']):
                        interesting_logs.append(line)
                
                if process.poll() is not None:
                    break
                    
            except KeyboardInterrupt:
                print("\n⏹️ 用户停止日志监控")
                break
        
        process.terminate()
        return interesting_logs
        
    except Exception as e:
        print(f"❌ 日志监控失败: {e}")
        return []

def alternative_methods():
    """替代分析方法"""
    print("\n🔧 替代分析方法建议:")
    print("=" * 50)
    
    print("\n💡 由于翼支付有强反调试保护，建议使用以下方法:")
    
    print("\n1️⃣ 网络代理抓包:")
    print("   - 在电脑上启动Charles或Burp Suite")
    print("   - 设置代理端口 (如8080)")
    print("   - 在手机WiFi设置中配置代理")
    print("   - 代理地址: 电脑IP地址")
    print("   - 安装证书以抓取HTTPS流量")
    
    print("\n2️⃣ 路由器抓包:")
    print("   - 在路由器上配置流量镜像")
    print("   - 使用Wireshark分析流量")
    print("   - 过滤目标域名: mapi-h5.bestpay.com.cn")
    
    print("\n3️⃣ 静态分析:")
    print("   - 下载翼支付APK文件")
    print("   - 使用jadx或apktool反编译")
    print("   - 分析加密相关代码")
    print("   - 查找密钥生成逻辑")
    
    print("\n4️⃣ 模拟器分析:")
    print("   - 使用夜神、雷电等模拟器")
    print("   - 安装Xposed框架")
    print("   - 使用JustTrustMe绕过SSL Pinning")
    print("   - 配合Frida进行Hook")
    
    print("\n🎯 推荐方案:")
    print("   使用Charles代理抓包是最简单有效的方法")
    print("   1. 下载Charles: https://www.charlesproxy.com/")
    print("   2. 启动Charles，记录代理端口")
    print("   3. 手机连接同一WiFi")
    print("   4. 手机WiFi设置 → 代理 → 手动")
    print("   5. 输入电脑IP和Charles端口")
    print("   6. 安装Charles证书")
    print("   7. 在翼支付中操作，观察Charles抓包结果")

def main():
    print("🎯 选择分析方法:")
    print("   1. 网络抓包分析")
    print("   2. 系统日志分析") 
    print("   3. 查看替代方案")
    print("   4. 全部执行")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1' or choice == '4':
        capture_network_traffic()
    
    if choice == '2' or choice == '4':
        analyze_logcat()
    
    if choice == '3' or choice == '4':
        alternative_methods()
    
    print("\n" + "=" * 50)
    print("🎉 分析完成!")
    print("💡 如果没有获得理想结果，建议使用Charles代理抓包")
    print("=" * 50)

if __name__ == "__main__":
    main()
