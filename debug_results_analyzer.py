#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终端调试结果综合分析工具
基于所有调试数据重新分析翼支付加解密机制
"""

import json
import os
import re
import base64
import hashlib
from datetime import datetime
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.backends import default_backend

class DebugResultsAnalyzer:
    def __init__(self):
        self.analysis_results = {
            "timestamp": datetime.now().isoformat(),
            "apk_analysis": {},
            "crypto_config": {},
            "encryption_flow": {},
            "key_findings": [],
            "decryption_strategy": {}
        }

        print("🔍 终端调试结果综合分析工具")
        print("🎯 基于所有调试数据重新分析翼支付加解密机制")
        print("=" * 70)

    def load_apk_analysis_results(self):
        """加载APK分析结果"""
        print("\n📱 加载APK分析结果...")

        apk_analysis_files = [
            "simple_analysis_110_41223d89caf6edd5729ce8a67c0bfb0c/so_libraries.json",
            "simple_analysis_110_41223d89caf6edd5729ce8a67c0bfb0c/config_analysis.json",
            "simple_analysis_110_41223d89caf6edd5729ce8a67c0bfb0c/dex_strings.json"
        ]

        for file_path in apk_analysis_files:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        file_name = os.path.basename(file_path).replace('.json', '')
                        self.analysis_results["apk_analysis"][file_name] = data
                        print(f"   ✅ 已加载: {file_name}")
                except Exception as e:
                    print(f"   ❌ 加载失败 {file_path}: {e}")
            else:
                print(f"   ⚠️ 文件不存在: {file_path}")

    def analyze_crypto_libraries(self):
        """分析加密库"""
        print("\n🔐 分析加密库...")

        so_data = self.analysis_results["apk_analysis"].get("so_libraries", {})
        crypto_libraries = so_data.get("crypto_libraries", [])

        print(f"   📊 发现 {len(crypto_libraries)} 个加密相关库:")

        key_libraries = {}
        for lib in crypto_libraries:
            lib_name = lib["name"]
            lib_size = lib["size"]

            print(f"      📚 {lib_name} ({lib_size/1024:.1f} KB)")

            # 分析库的功能
            if "mpaas_crypto" in lib_name:
                key_libraries["mpaas_crypto"] = {
                    "name": lib_name,
                    "size": lib_size,
                    "function": "mPaaS框架核心加密库",
                    "algorithms": ["AES", "SM2", "SM3", "SM4", "RSA"],
                    "importance": "critical"
                }
            elif "antssm" in lib_name:
                key_libraries["antssm"] = {
                    "name": lib_name,
                    "size": lib_size,
                    "function": "蚂蚁金服国密算法库",
                    "algorithms": ["SM2", "SM3", "SM4"],
                    "importance": "high"
                }
            elif "TencentSM" in lib_name:
                key_libraries["tencent_sm"] = {
                    "name": lib_name,
                    "size": lib_size,
                    "function": "腾讯国密算法实现",
                    "algorithms": ["SM2", "SM3", "SM4"],
                    "importance": "medium"
                }
            elif "openssl" in lib_name:
                key_libraries["openssl"] = {
                    "name": lib_name,
                    "size": lib_size,
                    "function": "标准OpenSSL加密库",
                    "algorithms": ["AES", "RSA", "MD5", "SHA"],
                    "importance": "high"
                }

        self.analysis_results["crypto_config"]["libraries"] = key_libraries

        print(f"\n   🎯 关键发现:")
        print(f"      - mPaaS是主要的加密框架")
        print(f"      - 支持国密算法 (SM2/SM3/SM4)")
        print(f"      - 有多重加密库备份")
        print(f"      - 总加密库大小: {sum(lib['size'] for lib in crypto_libraries)/1024:.1f} KB")

    def analyze_mpaas_config(self):
        """分析mPaaS配置"""
        print("\n🔧 分析mPaaS配置...")

        config_data = self.analysis_results["apk_analysis"].get("config_analysis", [])

        mpaas_config = None
        for config in config_data:
            if "mpaas_netconfig.properties" in config.get("file", ""):
                mpaas_config = config
                break

        if mpaas_config:
            content = mpaas_config.get("content_preview", "")
            print(f"   ✅ 找到mPaaS网络配置")

            # 提取关键配置
            config_lines = content.split('\n')
            parsed_config = {}

            for line in config_lines:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.split('=', 1)
                    parsed_config[key.strip()] = value.strip()

            # 分析配置
            if 'Crypt' in parsed_config:
                print(f"      🔐 加密启用: {parsed_config['Crypt']}")

            if 'Algorithm' in parsed_config:
                algorithm = parsed_config['Algorithm']
                print(f"      🧮 算法配置: {algorithm}")

                # 解析算法
                if "RSA/ECC/SM2=RMK" in algorithm:
                    print(f"         - 支持RSA、ECC、SM2算法")
                    print(f"         - 使用RMK密钥管理")

            if 'PubKey' in parsed_config:
                pubkey = parsed_config['PubKey']
                print(f"      🔑 公钥配置: 已找到")
                self.analyze_public_key(pubkey)

            self.analysis_results["crypto_config"]["mpaas"] = parsed_config
        else:
            print(f"   ❌ 未找到mPaaS配置")

    def analyze_public_key(self, pubkey_content):
        """分析公钥"""
        print(f"\n🔑 分析mPaaS公钥...")

        # 提取完整的公钥
        pubkey_lines = []
        in_key = False

        for line in pubkey_content.split('\n'):
            if "-----BEGIN PUBLIC KEY-----" in line:
                in_key = True
                pubkey_lines.append(line)
            elif "-----END PUBLIC KEY-----" in line:
                pubkey_lines.append(line)
                in_key = False
                break
            elif in_key:
                pubkey_lines.append(line)

        if pubkey_lines:
            pubkey_pem = '\n'.join(pubkey_lines)
            print(f"   📋 公钥内容:")
            print(f"      {pubkey_pem}")

            try:
                # 解析公钥
                public_key = serialization.load_pem_public_key(
                    pubkey_pem.encode(),
                    backend=default_backend()
                )

                # 获取公钥信息
                key_size = public_key.key_size
                print(f"   📊 公钥分析:")
                print(f"      - 密钥长度: {key_size} bits")
                print(f"      - 密钥类型: 椭圆曲线 (ECC)")

                # 判断是否为国密SM2
                if key_size == 256:
                    print(f"      - 可能的算法: SM2 (国密) 或 P-256")
                    print(f"      - 推荐解密方法: SM2私钥解密")

                self.analysis_results["crypto_config"]["public_key"] = {
                    "pem": pubkey_pem,
                    "key_size": key_size,
                    "algorithm": "SM2/ECC",
                    "usage": "加密AES密钥"
                }

            except Exception as e:
                print(f"   ❌ 公钥解析失败: {e}")

    def analyze_encryption_strings(self):
        """分析加密相关字符串"""
        print(f"\n📝 分析加密相关字符串...")

        dex_data = self.analysis_results["apk_analysis"].get("dex_strings", {})
        crypto_strings = dex_data.get("crypto_strings", [])

        print(f"   📊 找到 {len(crypto_strings)} 个加密相关字符串")

        # 分类分析
        categories = {
            "api_endpoints": [],
            "encryption_types": [],
            "algorithm_names": [],
            "key_management": [],
            "data_fields": []
        }

        for item in crypto_strings:
            string_content = item.get("string", "").lower()
            keyword = item.get("keyword", "").lower()

            if "queryactivityinfo" in string_content or "mapi-h5" in string_content:
                categories["api_endpoints"].append(string_content)
            elif "c005" in string_content or "encytype" in string_content:
                categories["encryption_types"].append(string_content)
            elif any(alg in string_content for alg in ["aes", "sm2", "sm3", "rsa"]):
                categories["algorithm_names"].append(string_content)
            elif any(key_word in string_content for key_word in ["key", "密钥", "secret"]):
                categories["key_management"].append(string_content)
            elif any(field in string_content for field in ["data", "sign", "fromchannelid"]):
                categories["data_fields"].append(string_content)

        # 显示分析结果
        for category, strings in categories.items():
            if strings:
                print(f"   🎯 {category} ({len(strings)}个):")
                for string in strings[:5]:  # 显示前5个
                    print(f"      - {string[:80]}...")

        self.analysis_results["crypto_config"]["strings"] = categories

    def reconstruct_encryption_flow(self):
        """重构加密流程"""
        print(f"\n🔄 重构加密流程...")

        # 基于分析结果重构完整的加密流程
        encryption_flow = {
            "step1": {
                "name": "数据准备",
                "description": "准备要加密的请求数据",
                "details": [
                    "构建JSON请求体",
                    "包含业务参数 (productNo, fromChannelId等)",
                    "序列化为字符串"
                ]
            },
            "step2": {
                "name": "AES密钥生成",
                "description": "生成随机AES密钥",
                "details": [
                    "生成256位随机AES密钥",
                    "可能使用系统随机数生成器",
                    "密钥用于对称加密数据"
                ]
            },
            "step3": {
                "name": "数据加密",
                "description": "使用AES加密请求数据",
                "details": [
                    "算法: AES-256-CBC 或 AES-256-ECB",
                    "输入: 原始请求数据",
                    "输出: 加密后的二进制数据",
                    "Base64编码后存储在data字段"
                ]
            },
            "step4": {
                "name": "密钥加密",
                "description": "使用SM2公钥加密AES密钥",
                "details": [
                    "算法: SM2椭圆曲线加密",
                    "公钥: mPaaS配置中的SM2公钥",
                    "输入: AES密钥",
                    "输出: 加密后的密钥",
                    "Base64编码后存储在key字段"
                ]
            },
            "step5": {
                "name": "签名计算",
                "description": "计算请求签名",
                "details": [
                    "算法: MD5 或 SM3",
                    "输入: data + key + 其他参数",
                    "可能包含时间戳和随机数",
                    "输出: 十六进制签名字符串"
                ]
            },
            "step6": {
                "name": "请求构建",
                "description": "构建最终的加密请求",
                "details": [
                    "data: Base64编码的加密数据",
                    "key: Base64编码的加密密钥",
                    "sign: MD5/SM3签名",
                    "encyType: C005 (加密类型标识)",
                    "productNo: 产品编号",
                    "fromChannelId: yzf_app (渠道标识)"
                ]
            }
        }

        self.analysis_results["encryption_flow"] = encryption_flow

        print(f"   ✅ 加密流程重构完成")
        for step_key, step_info in encryption_flow.items():
            print(f"      {step_key}: {step_info['name']}")

    def generate_decryption_strategy(self):
        """生成解密策略"""
        print(f"\n🔓 生成解密策略...")

        strategies = {
            "strategy1": {
                "name": "完整解密方案",
                "difficulty": "高",
                "success_rate": "高",
                "requirements": [
                    "获取SM2私钥",
                    "实现SM2解密算法",
                    "实现AES解密算法"
                ],
                "steps": [
                    "1. 从服务器或应用中提取SM2私钥",
                    "2. 使用SM2私钥解密key字段获得AES密钥",
                    "3. 使用AES密钥解密data字段获得原始数据",
                    "4. 验证sign字段确保数据完整性"
                ],
                "tools": [
                    "gmssl库 (国密算法)",
                    "cryptography库 (AES解密)",
                    "自定义解密工具"
                ]
            },
            "strategy2": {
                "name": "密钥推导方案",
                "difficulty": "中",
                "success_rate": "中",
                "requirements": [
                    "分析密钥生成算法",
                    "找到密钥生成种子",
                    "重现密钥生成过程"
                ],
                "steps": [
                    "1. 逆向libmpaas_crypto.so分析密钥生成",
                    "2. 找到密钥生成的种子或规律",
                    "3. 重现AES密钥生成过程",
                    "4. 直接解密data字段"
                ],
                "tools": [
                    "IDA Pro (静态分析)",
                    "Frida (动态分析)",
                    "自定义密钥生成器"
                ]
            },
            "strategy3": {
                "name": "网络重放方案",
                "difficulty": "低",
                "success_rate": "中",
                "requirements": [
                    "抓取完整的网络请求",
                    "分析请求参数规律",
                    "构建请求生成器"
                ],
                "steps": [
                    "1. 使用Charles抓取大量请求样本",
                    "2. 分析请求参数的变化规律",
                    "3. 识别固定参数和动态参数",
                    "4. 构建请求重放工具"
                ],
                "tools": [
                    "Charles Proxy",
                    "Burp Suite",
                    "自定义重放工具"
                ]
            },
            "strategy4": {
                "name": "Hook拦截方案",
                "difficulty": "中",
                "success_rate": "低",
                "requirements": [
                    "绕过反调试保护",
                    "Hook加密函数",
                    "实时获取密钥"
                ],
                "steps": [
                    "1. 使用反调试绕过技术",
                    "2. Hook AES加密函数获取密钥",
                    "3. Hook网络请求获取完整数据",
                    "4. 实时解密和分析"
                ],
                "tools": [
                    "Frida + 反调试绕过",
                    "Xposed框架",
                    "模拟器环境"
                ]
            }
        }

        self.analysis_results["decryption_strategy"] = strategies

        print(f"   ✅ 解密策略生成完成")
        for strategy_key, strategy_info in strategies.items():
            print(f"      {strategy_info['name']}: 难度{strategy_info['difficulty']}, 成功率{strategy_info['success_rate']}")

    def extract_key_findings(self):
        """提取关键发现"""
        print(f"\n🎯 提取关键发现...")

        key_findings = [
            {
                "category": "加密框架",
                "finding": "使用mPaaS框架进行加密",
                "importance": "critical",
                "details": "mPaaS是蚂蚁金服的移动开发框架，提供完整的加密解决方案"
            },
            {
                "category": "加密算法",
                "finding": "采用SM2+AES混合加密",
                "importance": "critical",
                "details": "SM2用于加密AES密钥，AES用于加密实际数据，符合国密标准"
            },
            {
                "category": "公钥信息",
                "finding": "SM2公钥已提取",
                "importance": "high",
                "details": "256位椭圆曲线公钥，用于加密AES密钥"
            },
            {
                "category": "API端点",
                "finding": "目标API已确认",
                "importance": "high",
                "details": "/gapi/equitymall/client/Activity/queryActivityInfo"
            },
            {
                "category": "加密类型",
                "finding": "C005加密类型",
                "importance": "medium",
                "details": "翼支付自定义的加密类型标识"
            },
            {
                "category": "反调试保护",
                "finding": "多层反调试机制",
                "importance": "high",
                "details": "包括ptrace检测、字符串检测、文件检测等"
            },
            {
                "category": "加密库",
                "finding": "多个加密库支持",
                "importance": "medium",
                "details": "libmpaas_crypto.so, libantssm.so, libTencentSM.so等"
            }
        ]

        self.analysis_results["key_findings"] = key_findings

        print(f"   ✅ 关键发现提取完成")
        for finding in key_findings:
            print(f"      {finding['category']}: {finding['finding']}")

    def save_analysis_results(self):
        """保存分析结果"""
        results_file = "comprehensive_crypto_analysis.json"

        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)

        print(f"\n💾 综合分析结果已保存: {results_file}")

    def generate_comprehensive_report(self):
        """生成综合报告"""
        report_file = "comprehensive_crypto_report.md"

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# 翼支付加密机制综合分析报告\n\n")
            f.write(f"## 分析时间\n{self.analysis_results['timestamp']}\n\n")

            # 关键发现
            f.write("## 关键发现\n")
            for finding in self.analysis_results.get("key_findings", []):
                f.write(f"### {finding['category']}\n")
                f.write(f"**发现**: {finding['finding']}\n\n")
                f.write(f"**重要性**: {finding['importance']}\n\n")
                f.write(f"**详情**: {finding['details']}\n\n")

            # 加密流程
            f.write("## 加密流程\n")
            encryption_flow = self.analysis_results.get("encryption_flow", {})
            for step_key, step_info in encryption_flow.items():
                f.write(f"### {step_info['name']}\n")
                f.write(f"{step_info['description']}\n\n")
                for detail in step_info['details']:
                    f.write(f"- {detail}\n")
                f.write("\n")

            # 解密策略
            f.write("## 解密策略\n")
            strategies = self.analysis_results.get("decryption_strategy", {})
            for strategy_key, strategy_info in strategies.items():
                f.write(f"### {strategy_info['name']}\n")
                f.write(f"**难度**: {strategy_info['difficulty']} | **成功率**: {strategy_info['success_rate']}\n\n")
                f.write("**要求**:\n")
                for req in strategy_info['requirements']:
                    f.write(f"- {req}\n")
                f.write("\n**步骤**:\n")
                for step in strategy_info['steps']:
                    f.write(f"- {step}\n")
                f.write("\n")

        print(f"💾 综合报告已保存: {report_file}")

    def run_comprehensive_analysis(self):
        """运行综合分析"""
        print("🎯 开始综合分析...")

        # 加载所有分析结果
        self.load_apk_analysis_results()

        # 分析加密库
        self.analyze_crypto_libraries()

        # 分析mPaaS配置
        self.analyze_mpaas_config()

        # 分析加密字符串
        self.analyze_encryption_strings()

        # 重构加密流程
        self.reconstruct_encryption_flow()

        # 生成解密策略
        self.generate_decryption_strategy()

        # 提取关键发现
        self.extract_key_findings()

        # 保存结果
        self.save_analysis_results()
        self.generate_comprehensive_report()

        print("\n" + "=" * 70)
        print("🎉 综合分析完成!")
        print("📁 生成文件:")
        print("   - comprehensive_crypto_analysis.json (详细分析)")
        print("   - comprehensive_crypto_report.md (综合报告)")
        print("=" * 70)

def main():
    analyzer = DebugResultsAnalyzer()
    analyzer.run_comprehensive_analysis()

if __name__ == "__main__":
    main()