#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SELinux宽容模式设置工具
自动检测并设置SELinux为宽容模式以便进行调试
"""

import subprocess
import time
import os

class SELinuxPermissiveSetter:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        print("🔧 SELinux宽容模式设置工具")
        print("🎯 设置SELinux为宽容模式以便进行Frida调试")
        print("=" * 60)
    
    def run_command(self, cmd, timeout=15):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='ignore')
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def check_current_selinux_status(self):
        """检查当前SELinux状态"""
        print("\n🔍 检查当前SELinux状态...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell getenforce")
        if success:
            status = stdout.strip()
            print(f"   📊 当前SELinux状态: {status}")
            
            if status == "Enforcing":
                print("   ⚠️ SELinux处于强制模式，可能阻止Frida Hook")
                return "enforcing"
            elif status == "Permissive":
                print("   ✅ SELinux已处于宽容模式")
                return "permissive"
            else:
                print(f"   ❓ SELinux状态未知: {status}")
                return "unknown"
        else:
            print(f"   ❌ 无法获取SELinux状态: {stderr}")
            return "error"
    
    def check_root_access(self):
        """检查Root权限"""
        print("\n🔑 检查Root权限...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'id'")
        if success and "uid=0" in stdout:
            print("   ✅ Root权限可用")
            return True
        else:
            print("   ❌ Root权限不可用")
            print("   💡 需要Root权限才能修改SELinux状态")
            return False
    
    def set_selinux_permissive_temporary(self):
        """临时设置SELinux为宽容模式"""
        print("\n🔧 临时设置SELinux为宽容模式...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'setenforce 0'")
        if success:
            print("   ✅ SELinux临时设置为宽容模式成功")
            
            # 验证设置
            time.sleep(1)
            success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell getenforce")
            if success and "Permissive" in stdout:
                print("   ✅ 验证成功: SELinux现在处于宽容模式")
                return True
            else:
                print("   ⚠️ 验证失败: SELinux状态未改变")
                return False
        else:
            print(f"   ❌ 设置失败: {stderr}")
            return False
    
    def check_magisk_availability(self):
        """检查Magisk是否可用"""
        print("\n🔍 检查Magisk可用性...")
        
        # 检查Magisk二进制文件
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell which magisk")
        if success and stdout.strip():
            print("   ✅ Magisk可用")
            return True
        
        # 检查su是否为Magisk版本
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -v")
        if success and "magisk" in stdout.lower():
            print("   ✅ Magisk SU可用")
            return True
        
        print("   ❌ Magisk不可用")
        return False
    
    def set_selinux_permissive_with_magisk(self):
        """使用Magisk设置SELinux宽容模式"""
        print("\n🔧 使用Magisk设置SELinux宽容模式...")
        
        # 方法1: 使用magiskpolicy
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'magiskpolicy --live \"allow untrusted_app untrusted_app process execmem\"'")
        if success:
            print("   ✅ Magisk策略设置成功")
        
        # 方法2: 直接设置宽容模式
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'magisk --selinux permissive'")
        if success:
            print("   ✅ Magisk SELinux设置成功")
            return True
        else:
            print(f"   ⚠️ Magisk SELinux设置失败: {stderr}")
            return False
    
    def create_selinux_script(self):
        """创建SELinux设置脚本"""
        print("\n📝 创建SELinux设置脚本...")
        
        script_content = '''#!/system/bin/sh
# SELinux宽容模式设置脚本

echo "🔧 设置SELinux为宽容模式..."

# 检查当前状态
current_status=$(getenforce)
echo "当前SELinux状态: $current_status"

# 设置宽容模式
setenforce 0

# 验证设置
new_status=$(getenforce)
echo "新的SELinux状态: $new_status"

if [ "$new_status" = "Permissive" ]; then
    echo "✅ SELinux宽容模式设置成功"
else
    echo "❌ SELinux宽容模式设置失败"
fi
'''
        
        # 保存脚本到本地
        with open("set_selinux_permissive.sh", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        # 推送脚本到设备
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} push set_selinux_permissive.sh /data/local/tmp/")
        if success:
            print("   ✅ 脚本已推送到设备")
            
            # 设置执行权限
            success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'chmod 755 /data/local/tmp/set_selinux_permissive.sh'")
            if success:
                print("   ✅ 脚本权限设置成功")
                return True
        
        print("   ❌ 脚本推送失败")
        return False
    
    def execute_selinux_script(self):
        """执行SELinux设置脚本"""
        print("\n🚀 执行SELinux设置脚本...")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c '/data/local/tmp/set_selinux_permissive.sh'")
        if success:
            print("   📊 脚本执行结果:")
            for line in stdout.strip().split('\n'):
                if line.strip():
                    print(f"      {line}")
            return True
        else:
            print(f"   ❌ 脚本执行失败: {stderr}")
            return False
    
    def provide_alternative_methods(self):
        """提供替代方法"""
        print("\n💡 替代方法和建议...")
        
        methods = [
            {
                "name": "使用init.d脚本",
                "description": "创建开机自动执行的SELinux设置脚本",
                "commands": [
                    "adb shell su -c 'mount -o remount,rw /system'",
                    "adb shell su -c 'echo \"setenforce 0\" > /system/etc/init.d/99selinux'",
                    "adb shell su -c 'chmod 755 /system/etc/init.d/99selinux'"
                ]
            },
            {
                "name": "使用Magisk模块",
                "description": "安装专门的Magisk模块来管理SELinux",
                "commands": [
                    "下载SELinux Permissive模块",
                    "通过Magisk Manager安装",
                    "重启设备"
                ]
            },
            {
                "name": "修改内核参数",
                "description": "通过内核参数设置SELinux",
                "commands": [
                    "需要自定义内核支持",
                    "添加 androidboot.selinux=permissive 到内核参数"
                ]
            },
            {
                "name": "使用Xposed模块",
                "description": "通过Xposed框架绕过SELinux限制",
                "commands": [
                    "安装Xposed框架",
                    "安装SELinux Mode Changer模块",
                    "在模块中设置宽容模式"
                ]
            }
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"\n   {i}️⃣ {method['name']}")
            print(f"      📝 {method['description']}")
            print(f"      📋 步骤:")
            for cmd in method['commands']:
                print(f"         - {cmd}")
    
    def test_frida_after_selinux_change(self):
        """测试SELinux更改后的Frida功能"""
        print("\n🧪 测试SELinux更改后的Frida功能...")
        
        # 检查Frida连接
        success, stdout, stderr = self.run_command("frida-ps -U")
        if success:
            print("   ✅ Frida连接正常")
            
            # 创建简单测试脚本
            test_script = '''
console.log("🧪 SELinux测试脚本");
console.log("✅ Frida注入成功");

Java.perform(function() {
    console.log("✅ Java环境可用");
    
    try {
        var System = Java.use("java.lang.System");
        console.log("✅ 基础Hook成功");
        console.log("🎉 SELinux设置有效!");
    } catch(e) {
        console.log("❌ Hook仍然失败: " + e);
    }
});
'''
            
            with open("selinux_test.js", "w", encoding="utf-8") as f:
                f.write(test_script)
            
            print("   🚀 执行Frida测试...")
            
            # 检查翼支付是否在运行
            success, stdout, stderr = self.run_command("frida-ps -U | findstr -i bestpay")
            if success and stdout.strip():
                print("   📱 翼支付应用正在运行，执行Hook测试...")
                
                try:
                    process = subprocess.Popen(
                        "frida -U com.chinatelecom.bestpayclient -l selinux_test.js",
                        shell=True,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        encoding='utf-8',
                        errors='ignore'
                    )
                    
                    stdout, stderr = process.communicate(timeout=10)
                    
                    if "SELinux设置有效" in stdout:
                        print("   ✅ SELinux设置有效，Hook功能正常")
                        return True
                    elif "Process terminated" in stdout:
                        print("   ⚠️ 应用仍然检测到调试器")
                        return False
                    else:
                        print("   ⚠️ 测试结果不明确")
                        return False
                        
                except subprocess.TimeoutExpired:
                    process.kill()
                    print("   ⏰ 测试超时")
                    return False
            else:
                print("   ⚠️ 翼支付应用未运行，无法进行完整测试")
                return False
        else:
            print(f"   ❌ Frida连接失败: {stderr}")
            return False
    
    def run_selinux_setup(self):
        """运行SELinux设置流程"""
        print("🎯 开始SELinux宽容模式设置...")
        
        # 检查当前状态
        current_status = self.check_current_selinux_status()
        
        if current_status == "permissive":
            print("\n✅ SELinux已经处于宽容模式，无需设置")
            return True
        
        if current_status == "error":
            print("\n❌ 无法获取SELinux状态，可能设备不支持")
            return False
        
        # 检查Root权限
        if not self.check_root_access():
            print("\n❌ 需要Root权限才能修改SELinux状态")
            self.provide_alternative_methods()
            return False
        
        # 尝试临时设置
        print("\n🔧 尝试临时设置SELinux宽容模式...")
        if self.set_selinux_permissive_temporary():
            print("   ✅ 临时设置成功")
            
            # 测试Frida功能
            if self.test_frida_after_selinux_change():
                print("\n🎉 SELinux设置成功，Frida功能正常!")
                
                print("\n⚠️ 重要提醒:")
                print("   - 当前设置为临时性，重启后会恢复")
                print("   - 如需永久设置，请使用Magisk模块")
                print("   - 宽容模式会降低系统安全性")
                
                return True
            else:
                print("\n⚠️ SELinux已设置，但Hook仍有问题")
                print("💡 可能还有其他反调试保护机制")
        
        # 尝试Magisk方法
        if self.check_magisk_availability():
            print("\n🔧 尝试使用Magisk设置...")
            if self.set_selinux_permissive_with_magisk():
                print("   ✅ Magisk设置成功")
                return True
        
        # 创建和执行脚本
        if self.create_selinux_script():
            if self.execute_selinux_script():
                print("   ✅ 脚本执行成功")
                return True
        
        # 提供替代方法
        print("\n❌ 自动设置失败")
        self.provide_alternative_methods()
        
        return False

def main():
    print("🔧 SELinux宽容模式设置工具")
    print("⚠️ 警告: 设置SELinux为宽容模式会降低系统安全性")
    print("💡 建议: 仅在调试时使用，调试完成后恢复强制模式")
    
    confirm = input("\n是否继续设置SELinux宽容模式? (y/n): ").strip().lower()
    if confirm != 'y':
        print("👋 操作已取消")
        return
    
    setter = SELinuxPermissiveSetter()
    success = setter.run_selinux_setup()
    
    if success:
        print("\n" + "=" * 60)
        print("🎉 SELinux宽容模式设置完成!")
        print("💡 现在可以尝试运行Frida Hook脚本")
        print("🚀 建议执行: python simple_hook_analyzer.py")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ SELinux设置失败")
        print("💡 建议使用Charles代理抓包作为替代方案")
        print("=" * 60)

if __name__ == "__main__":
    main()
