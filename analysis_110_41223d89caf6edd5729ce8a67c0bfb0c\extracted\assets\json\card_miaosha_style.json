{"type": "container", "layout": {"display": "flex", "flex-direction": "column", "justify-content": "center", "align-items": "center", "width": "100%"}, "style": {"border-radius": "24rpx", "background": "#ffffff"}, "background-image": "${backgroundUrl}", "children": [{"type": "container", "layout": {"display": "flex", "flex-direction": "row", "justify-content": "space-between", "align-items": "center", "padding-horizontal": "16rpx", "padding-vertical": "5rpx", "width": "100%", "height": "56rpx"}, "children": [{"type": "container", "layout": {"display": "flex", "flex-direction": "row", "align-items": "center", "height": "100%"}, "children": [{"type": "text", "m-if": "$fb{ '${templateName}'.includes('font') == false }", "text": [{"content": "${templateName}", "color": "#1F2229", "font-size": "15px", "i-font-family": "HYYaKuHei-75J", "a-font-family": "HYYaKuHei"}]}, {"type": "text", "m-if": "$fb{ '${templateName}'.includes('font') == true }", "text": [{"body": "${templateName}"}]}, {"type": "text", "layout": {"margin-left": "8rpx", "padding-horizontal": "8rpx", "height": "32rpx"}, "style": {"border-radius": "16rpx 16rpx 16rpx 4rpx", "background": "linear-gradient(to right, #FF2A38, #FF705C)"}, "m-if": "$fb{ '${cornerLabelType}' == 'TEXT' }", "text": [{"content": "${cornerLabelText}", "font-size": "10px", "color": "#FFFFFF"}]}, {"type": "img", "layout": {"margin-left": "8rpx", "height": "32rpx"}, "m-if": "$fb{ '${cornerLabelType}' == 'IMAGE' }", "src": "${cornerLabelImgUrl}", "keep-actual-ratio": "true"}]}, {"type": "container", "layout": {"display": "flex", "flex-direction": "row", "height": "100%"}, "m-if": "$fb{ ${titleShowMore} == true }", "children": [{"type": "text", "action": {"click-event": "$f{doMoreClick()}"}, "text": [{"content": "${moreName}", "font-size": "11px", "color": "#999DA6"}]}, {"type": "img", "layout": {"width": "16rpx"}, "src": "https://cdn.bestpay.cn/group4/M00/BD/07/rBwwIme2nnOAQA7GAAABG0sv25E189.png"}]}]}, {"type": "container", "layout": {"display": "flex", "flex-direction": "row", "justify-content": "space-between", "flex": "1", "padding-bottom": "16rpx", "padding-horizontal": "16rpx", "width": "100%"}, "children": [{"type": "container", "layout": {"display": "flex", "flex-direction": "column", "align-items": "center", "width": "148rpx", "height": "148rpx"}, "children": [{"type": "switch-img", "layout": {"width": "148rpx", "height": "148rpx"}, "style": {"border-radius": "16rpx", "background": "#ffffff"}, "action": {"click-event": "$f{doFirstCarouselClickEvent()}"}, "src": "${first_carousel_img}", "animated": "${first_carousel_animated}"}, {"type": "img", "layout": {"width": "100%", "height": "40rpx", "position": "absolute", "bottom": "0rpx"}, "m-if": "$fb{ '${first_carousel_price_type}' == 'IMAGE' }", "src": "${first_carousel_price_img}"}, {"type": "container", "layout": {"display": "flex", "flex-direction": "row", "height": "32rpx", "position": "absolute", "bottom": "0rpx"}, "m-if": "$fb{ '${first_carousel_price_type}' == 'TEXT' }", "children": [{"type": "img", "layout": {"width": "20rpx", "height": "32rpx"}, "src": "https://ctcdn.bestpay.cn/html/h5-page/img_lobby_matts_item_light.png"}, {"type": "text", "layout": {"padding-horizontal": "4rpx", "padding-vertical": "4rpx"}, "style": {"border-radius": "0rpx 16rpx 16rpx 0rpx", "background": "linear-gradient(to right, #FF2A38, #FF705C)"}, "text": [{"content": "${first_carousel_price_text}", "font-size": "24rpx", "color": "#ffffff", "font-family": "Yzf_num"}]}]}]}, {"type": "container", "layout": {"display": "flex", "flex-direction": "column", "align-items": "center", "width": "148rpx", "height": "148rpx"}, "children": [{"type": "switch-img", "layout": {"width": "148rpx", "height": "148rpx"}, "style": {"border-radius": "16rpx", "background": "#ffffff"}, "action": {"click-event": "$f{doSecondCarouselClickEvent()}"}, "src": "${second_carousel_img}", "animated": "${second_carousel_animated}"}, {"type": "img", "layout": {"width": "100%", "height": "40rpx", "position": "absolute", "bottom": "0rpx"}, "m-if": "$fb{ '${second_carousel_price_type}' == 'IMAGE' }", "src": "${second_carousel_price_img}"}, {"type": "container", "layout": {"display": "flex", "flex-direction": "row", "height": "32rpx", "position": "absolute", "bottom": "0rpx"}, "m-if": "$fb{ '${second_carousel_price_type}' == 'TEXT' }", "children": [{"type": "img", "layout": {"width": "20rpx", "height": "32rpx"}, "src": "https://ctcdn.bestpay.cn/html/h5-page/img_lobby_matts_item_light.png"}, {"type": "text", "layout": {"padding-horizontal": "4rpx", "padding-vertical": "4rpx"}, "style": {"border-radius": "0rpx 16rpx 16rpx 0rpx", "background": "linear-gradient(to right, #FF2A38, #FF705C)"}, "text": [{"content": "${second_carousel_price_text}", "font-size": "24rpx", "color": "#ffffff", "font-family": "Yzf_num"}]}]}]}]}], "lifecycle": true, "logic": "(function(){'use strict';var main={data:{message:\"hello <PERSON><PERSON><PERSON>\",originalData:{templateName:\"超级秒杀\",backgroundUrl:\"https://h5.bestpay.cn/subapps/mall-shopping-h5/index.html#/pagesB/channelPage/main?hybridVersion=3.0&channel=dt9k9\",carouselType:\"0\",carouselFlag:\"1\",contentList:[{description:\"\",materialId:\"241009134806046293\",subTitle:\"241009134806046293\",backgroundUrl:\"https://oss.bestpay.cn/gz/mall-11/8024_95/mall_95/20241009/unABf1dc4b35c09a4f1db68c1f249b07034f09408578.jpg\",title:\"佳霖68g万用香脆炸粉油炸鸡腿鸡翅鸡排脆皮裹粉炸粉金黄香酥鱼鳞\",linkUrl:\"https://h5.bestpay.cn/subapps/mall-shopping-h5/index.html#/pagesB/channelPage/main?hybridVersion=3.0&skuNo=241009134806046293&channel=dtdzg9k9\",originalPrice:\"3.90\",price:\"1.00\",questionPrice:\"1.00\",iconUrl:\"https://oss.bestpay.cn/gz/mall-11/8024_95/mall_95/20241009/unABf1dc4b35c09a4f1db68c1f249b07034f09408578.jpg\",oneFlashSaleImg:\"https://ctcdn.bestpay.cn/group4/M00/B1/F1/rBwwImcM3DGAFJ60AAA96ANSb00523.png\",name:\"佳霖68g万用香脆炸粉油炸鸡腿鸡翅鸡排脆皮裹粉炸粉金黄香酥鱼鳞\",oneFlashSale:true,},],extraConfig:{cornerLabel:{cornerText:\"1元起\",type:\"TEXT\",cornerImgUrl:\"\",},},moreName:\"秒杀\",moreUrl:\"https://h5.test.bestpay.net/subapps/provincial-channel-h5/index.html\",},templateName:\"\",backgroundUrl:\"\",track_element_index:\"0\",track_block_name:\"首页_推荐\",carousel_play:\"false\",first_content_list:[],first_carousel_img:[],first_carousel_animated:\"false\",first_carousel_play:\"false\",first_carousel_index:0,first_carousel_price_type:\"\",first_carousel_price_img:\"\",first_carousel_price_text:\"\",second_content_list:[],second_carousel_img:[],second_carousel_animated:\"false\",second_carousel_play:\"false\",second_carousel_index:0,second_carousel_price_type:\"\",second_carousel_price_img:\"\",second_carousel_price_text:\"\",cornerLabelType:\"\",cornerLabelText:\"\",cornerLabelImgUrl:\"\",titleShowMore:false,moreName:\"\",moreUrl:\"\"},beforeCreate(){this.handleOriginalData(false);this.data.first_carousel_index=0;this.data.second_carousel_index=0;this.data.first_carousel_animated=\"false\";this.data.second_carousel_animated=\"false\";},onCreated(){if(this.data.first_content_list.length>0){let content=this.data.first_content_list[0];let element_index=this.data.track_element_index+\".1.1\";this.doTrackExposureEvent(content,element_index);}\nif(this.data.second_content_list.length>0){let content=this.data.second_content_list[0];let element_index=this.data.track_element_index+\".2.1\";this.doTrackExposureEvent(content,element_index);}},onUpdated(){this.handleOriginalData(true);},getValueNotNullish(value,defaultValue){return value!==undefined&&value!==null?value:defaultValue;},safeAccess(obj,...keys){return keys.reduce((acc,key)=>(acc&&acc[key]!==undefined?acc[key]:undefined),obj);},handleOriginalData(needRefresh){try{let originalData=this.getValueNotNullish(this.data.originalData,{});var templateName=this.getValueNotNullish(originalData.templateName,\"\");if(templateName.includes(\"font\")==true){}else{if(templateName.length>5){templateName=templateName.substring(0,5)+\"...\";}}\nthis.data.templateName=templateName;this.data.backgroundUrl=this.getValueNotNullish(originalData.backgroundUrl,\"\");let cornerLabel=this.getValueNotNullish(this.safeAccess(originalData,\"extraConfig\",\"cornerLabel\"),{});let cornerLabelType=this.getValueNotNullish(cornerLabel.type,\"\");let cornerLabelText=this.getValueNotNullish(cornerLabel.cornerText,\"\");let cornerLabelImgUrl=this.getValueNotNullish(cornerLabel.cornerImgUrl,\"\");if(cornerLabelType==\"TEXT\"&&cornerLabelText.length>0){if(cornerLabelText.length>6){cornerLabelText=cornerLabelText.substring(0,6);}\nthis.data.cornerLabelType=cornerLabelType;this.data.cornerLabelText=cornerLabelText;}else if(cornerLabelType==\"IMAGE\"&&cornerLabelImgUrl.length>0){this.data.cornerLabelType=cornerLabelType;this.data.cornerLabelImgUrl=cornerLabelImgUrl;}else{this.data.cornerLabelType=\"\";this.data.cornerLabelText=\"\";this.data.cornerLabelImgUrl=\"\";}\nvar moreName=this.getValueNotNullish(originalData.moreName,\"\");if(moreName.length>4){moreName=moreName.substring(0,4)+\"...\";}\nthis.data.moreName=moreName;this.data.moreUrl=this.getValueNotNullish(originalData.moreUrl,\"\");if(this.data.moreName.length>0&&this.data.moreUrl.length>0&&this.data.cornerLabelType==\"\"){this.data.titleShowMore=true;}else{this.data.titleShowMore=false;}\nthis.getContentList();if(needRefresh){this.wk.setState({});}}catch(error){console.log(\"[Test] handleOriginalData error \"+error);}},getContentList(){var first_content_list=[];var second_content_list=[];let originalData=this.getValueNotNullish(this.data.originalData,{});let contentListSize=originalData.contentList.length;for(var i=0;i<contentListSize;i++){var item=originalData.contentList[i];if(i%2==0){first_content_list.push(item);}else{second_content_list.push(item);}}\nthis.data.first_content_list=first_content_list;this.data.second_content_list=second_content_list;if(first_content_list.length>0){this.data.first_carousel_img=first_content_list[0].backgroundUrl;}\nif(second_content_list.length>0){this.data.second_carousel_img=second_content_list[0].backgroundUrl;}\nthis.getFirstCarouselPrice(this.data.first_content_list.length>0?this.data.first_content_list[0]:undefined);this.getSecondCarouselPrice(this.data.second_content_list.length>0?this.data.second_content_list[0]:undefined);let carouselFlag=originalData.carouselFlag;let carouselType=originalData.carouselType;if(carouselFlag==\"1\"){if(carouselType==\"1\"){this.data.first_carousel_play=\"true\";this.data.second_carousel_play=\"false\";}else if(carouselType==\"2\"){this.data.first_carousel_play=\"false\";this.data.second_carousel_play=\"true\";}else{this.data.first_carousel_play=\"true\";this.data.second_carousel_play=\"true\";}}else{this.data.first_carousel_play=\"false\";this.data.second_carousel_play=\"false\";}},doMoreClick(){var url=this.data.moreUrl;if(url.length==0){return;}\nthis.doOpenRouter(url);this.doCallNativeTrack(\"click\",{requirement_id:1920,biz_name:\"翼支付基础\",track_all_name:\"翼支付基础-大厅-田字格_点击\",track_sign:\"ayzfjc.b21452.c21619_30618.click\",position_name:\"田字格\",position_type:\"区块\",block_type:\"普通区块\",product_name:this.getValueNotNullish(this.data.templateName,\"\"),block_name:this.getValueNotNullish(this.data.track_block_name,\"\"),content_type:\"more\",content_id:\"无\",type:\"标准-单标题\",content_name:\"无\",element_index:this.data.track_element_index});},getFirstCarouselPrice(content){if(content==undefined){(this.data.first_carousel_price_type=\"\"),(this.data.first_carousel_price_img=\"\");this.data.first_carousel_price_text=\"\";return;}\nlet oneFlashSale=content.oneFlashSale;let oneFlashSaleImg=content.oneFlashSaleImg;if(oneFlashSale==true&&oneFlashSaleImg.length>0){(this.data.first_carousel_price_type=\"IMAGE\"),(this.data.first_carousel_price_img=oneFlashSaleImg);this.data.first_carousel_price_text=\"\";return;}\nvar priceNum=Number(this.getValueNotNullish(content.buyPrice,\"0\"));if(priceNum>0){priceNum=Number((priceNum/100).toFixed(2));this.data.first_carousel_price_type=\"TEXT\";this.data.first_carousel_price_text=\"到手¥\"+priceNum.toString();this.data.first_carousel_price_img=\"\";return;}\npriceNum=Number(this.getValueNotNullish(content.price,\"0\"));if(priceNum>0){console.log(\"显示price\");priceNum=Number(priceNum.toFixed(2));this.data.first_carousel_price_type=\"TEXT\";this.data.first_carousel_price_text=\"到手¥\"+priceNum.toString();this.data.first_carousel_price_img=\"\";return;}\npriceNum=Number(this.getValueNotNullish(content.originalPrice,\"0\"));if(priceNum>0){console.log(\"显示originalPrice\");priceNum=Number(priceNum.toFixed(2));this.data.first_carousel_price_type=\"TEXT\";this.data.first_carousel_price_text=\"¥\"+priceNum.toString();this.data.first_carousel_price_img=\"\";return;}\n(this.data.first_carousel_price_type=\"\"),(this.data.first_carousel_price_img=\"\");this.data.first_carousel_price_text=\"\";},getSecondCarouselPrice(content){if(content==undefined){(this.data.second_carousel_price_type=\"\"),(this.data.second_carousel_price_text=\"\");this.data.second_carousel_price_img=\"\";return;}\nlet oneFlashSale=content.oneFlashSale;let oneFlashSaleImg=content.oneFlashSaleImg;if(oneFlashSale==true&&oneFlashSaleImg.length>0){(this.data.second_carousel_price_type=\"IMAGE\"),(this.data.second_carousel_price_img=oneFlashSaleImg);this.data.second_carousel_price_text=\"\";return;}\nvar priceNum=Number(this.getValueNotNullish(content.buyPrice,\"0\"));if(priceNum>0){priceNum=Number((priceNum/100).toFixed(2));this.data.second_carousel_price_type=\"TEXT\";this.data.second_carousel_price_text=\"到手¥\"+priceNum.toString();this.data.second_carousel_price_img=\"\";return;}\npriceNum=Number(this.getValueNotNullish(content.price,\"0\"));if(priceNum>0){console.log(\"显示price\");priceNum=Number(priceNum.toFixed(2));this.data.second_carousel_price_type=\"TEXT\";this.data.second_carousel_price_text=\"到手¥\"+priceNum.toString();this.data.second_carousel_price_img=\"\";return;}\npriceNum=Number(this.getValueNotNullish(content.originalPrice,\"0\"));if(priceNum>0){console.log(\"显示originalPrice\");priceNum=Number(priceNum.toFixed(2));this.data.second_carousel_price_type=\"TEXT\";this.data.second_carousel_price_text=\"¥\"+priceNum.toString();this.data.second_carousel_price_img=\"\";return;}\n(this.data.second_carousel_price_type=\"\"),(this.data.second_carousel_price_text=\"\");this.data.second_carousel_price_img=\"\";},getUserInfo(){return new Promise((resolve,reject)=>{try{BestpayCardBridge.callNativeEvent({name:BestpayCardBridgeEventGetUserInfo,parameters:{},callback:(result)=>{if(result.code==BestpayCardBridgeResultCodeSuccess&&result.data.hasOwnProperty(\"productNo\")){this.data.productNo=result.data[\"productNo\"];}\nresolve();}});}catch(error){resolve();}});},async doTrackExposureEvent(content,element_index){await this.getUserInfo();let track_all_name=\"翼支付基础-大厅-田字格_曝光\";let content_name=this.getValueNotNullish(content.name,\"\");let content_id=this.getValueNotNullish(content.subTitle,\"无\");let product_name=this.getValueNotNullish(this.data.templateName,\"\");let block_name=this.getValueNotNullish(this.data.track_block_name,\"\");let identifier=track_all_name+\"#\"+product_name+\"#\"+block_name+\"#\"+content_name+\"#\"+content_id+\"#\"+this.data.productNo;this.doCallNativeTrack(\"exposure\",{requirement_id:1920,biz_name:\"翼支付基础\",track_all_name:track_all_name,track_sign:\"ayzfjc.b21452.c21619_30617.exposure\",position_name:\"田字格\",position_type:\"区块\",block_type:\"普通区块\",product_name:this.getValueNotNullish(this.data.templateName,\"\"),block_name:this.getValueNotNullish(this.data.track_block_name,\"\"),content_type:\"物料\",type:\"标准-单标题\",content_name:this.getValueNotNullish(content.name,\"\"),content_id:this.getValueNotNullish(content.subTitle,\"无\"),element_index:element_index,},identifier,true);},doFirstCarouselClickEvent(){let content=this.data.first_content_list[this.data.first_carousel_index];let i=this.data.first_carousel_index+1;let element_index=this.data.track_element_index+\".1.\"+i;this.doCarouselClick(content,element_index);},doSecondCarouselClickEvent(){let content=this.data.second_content_list[this.data.second_carousel_index];let i=this.data.second_carousel_index+1;let element_index=this.data.track_element_index+\".2.\"+i;this.doCarouselClick(content,element_index);},doCarouselClick(content,element_index){content=this.getValueNotNullish(content,{});let url=this.getValueNotNullish(content.linkUrl,\"\");if(url.length==0){return;}\nthis.doOpenRouter(url);this.doCallNativeTrack(\"click\",{requirement_id:1920,biz_name:\"翼支付基础\",track_all_name:\"翼支付基础-大厅-田字格_点击\",track_sign:\"ayzfjc.b21452.c21619_30618.click\",position_name:\"田字格\",position_type:\"区块\",block_type:\"普通区块\",product_name:this.getValueNotNullish(this.data.templateName,\"\"),block_name:this.getValueNotNullish(this.data.track_block_name,\"\"),content_type:\"物料\",type:\"标准-单标题\",content_name:this.getValueNotNullish(content.name,\"\"),content_id:this.getValueNotNullish(content.subTitle,\"无\"),element_index:element_index,});},doCallNativeTrack(eventId,properties,identifier,once){try{BestpayCardBridge.callNativeEvent({name:BestpayCardBridgeEventTrack,parameters:{eventId:eventId,properties:properties,identifier:identifier,once:once,},callback:(result)=>{console.log(\"[Test] BestpayCardBridgeEventTrack callback \"+result.code);},});}catch(error){console.log(\"[Test] BestpayCardBridgeEventTrack error \"+error);}},doOpenRouter(url){try{BestpayCardBridge.callNativeEvent({name:BestpayCardBridgeEventOpenRouter,parameters:{router:url,},callback:(result)=>{console.log(\"[Test] doOpenRouter callback \"+result.code+\" \"+result.message);}})}catch(error){console.log(\"[Test] doOpenRouter error \"+error);}},doShowNext(param){let animated=param&&param.hasOwnProperty(\"animated\")?param.animated:false;if(this.data.first_content_list.length>1&&this.data.first_carousel_play==\"true\"){this.data.first_carousel_animated=animated?\"true\":\"false\";this.data.first_carousel_index=(this.data.first_carousel_index+1)%this.data.first_content_list.length;let first_content=this.data.first_content_list[this.data.first_carousel_index];this.data.first_carousel_img=first_content.backgroundUrl;this.getFirstCarouselPrice(first_content);let i=this.data.first_carousel_index+1;let element_index=this.data.track_element_index+\".1.\"+i;this.doTrackExposureEvent(first_content,element_index);}\nif(this.data.second_content_list.length>1&&this.data.second_carousel_play==\"true\"){this.data.second_carousel_animated=animated?\"true\":\"false\";this.data.second_carousel_index=(this.data.second_carousel_index+1)%this.data.second_content_list.length;let second_content=this.data.second_content_list[this.data.second_carousel_index];this.data.second_carousel_img=second_content.backgroundUrl;this.getSecondCarouselPrice(second_content);let i=this.data.second_carousel_index+1;let element_index=this.data.track_element_index+\".2.\"+i;this.doTrackExposureEvent(second_content,element_index);}\nthis.wk.setState({});}};;return main;})()"}