#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翼支付智能调试助手
一键完成环境检查、设置和调试启动
"""

import os
import sys
import subprocess
import time
import urllib.request
import zipfile
from pathlib import Path

class SmartAssistant:
    def __init__(self):
        self.adb_cmd = None
        self.setup_complete = False
        
    def print_banner(self):
        """显示欢迎横幅"""
        print("\n" + "="*60)
        print("📱 翼支付智能调试助手")
        print("🎯 自动检测环境并指引您完成真机调试设置")
        print("="*60)
    
    def print_step(self, step_num, title):
        """显示步骤标题"""
        print(f"\n📋 第{step_num}步: {title}")
        print("-" * 40)
    
    def run_command(self, cmd, capture_output=True, timeout=30):
        """执行命令"""
        try:
            if capture_output:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
                return result.returncode == 0, result.stdout, result.stderr
            else:
                result = subprocess.run(cmd, shell=True, timeout=timeout)
                return result.returncode == 0, "", ""
        except subprocess.TimeoutExpired:
            return False, "", "命令执行超时"
        except Exception as e:
            return False, "", str(e)
    
    def check_python(self):
        """检查Python环境"""
        self.print_step(1, "检查Python环境")
        
        success, stdout, stderr = self.run_command("python --version")
        if success:
            version = stdout.strip()
            print(f"   ✅ {version}")
            return True
        else:
            print("   ❌ Python未安装")
            print("\n💡 解决方案:")
            print("   1. 访问 https://www.python.org/downloads/")
            print("   2. 下载并安装最新版Python")
            print("   3. 安装时勾选 'Add Python to PATH'")
            print("   4. 重新运行此脚本")
            return False
    
    def install_frida(self):
        """安装Frida"""
        self.print_step(2, "检查和安装Frida")
        
        success, stdout, stderr = self.run_command("frida --version")
        if success:
            version = stdout.strip()
            print(f"   ✅ Frida {version} 已安装")
            return True
        
        print("   ⚠️  Frida未安装，正在自动安装...")
        print("   💡 这可能需要几分钟时间...")
        
        success, stdout, stderr = self.run_command("pip install frida-tools", timeout=300)
        if success:
            print("   ✅ Frida安装成功")
            return True
        else:
            print("   ❌ Frida安装失败")
            print(f"   错误: {stderr}")
            print("\n💡 手动安装方法:")
            print("   1. 运行: pip install --upgrade pip")
            print("   2. 运行: pip install frida-tools")
            return False
    
    def setup_adb(self):
        """设置ADB工具"""
        self.print_step(3, "检查ADB工具")
        
        # 检查便携版ADB
        portable_adb = Path("android-tools/platform-tools/adb.exe")
        if portable_adb.exists():
            print("   ✅ 便携版ADB已存在")
            self.adb_cmd = str(portable_adb)
            return True
        
        # 检查系统ADB
        success, stdout, stderr = self.run_command("adb version")
        if success:
            print("   ✅ 系统ADB可用")
            self.adb_cmd = "adb"
            return True
        
        print("   ❌ ADB工具不存在")
        print("   🔧 正在为您下载ADB工具...")
        
        return self.download_adb()
    
    def download_adb(self):
        """下载ADB工具"""
        try:
            # 创建目录
            tools_dir = Path("android-tools")
            tools_dir.mkdir(exist_ok=True)
            
            # 下载URL
            url = "https://dl.google.com/android/repository/platform-tools-latest-windows.zip"
            zip_path = tools_dir / "platform-tools.zip"
            
            print("   📦 正在下载... (这可能需要几分钟)")
            urllib.request.urlretrieve(url, zip_path)
            print("   ✅ 下载完成")
            
            # 解压
            print("   📂 正在解压...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(tools_dir)
            
            # 检查ADB是否存在
            adb_path = tools_dir / "platform-tools" / "adb.exe"
            if adb_path.exists():
                print("   ✅ ADB工具下载并解压成功")
                self.adb_cmd = str(adb_path)
                return True
            else:
                print("   ❌ 解压失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 下载失败: {e}")
            print("\n💡 手动下载方法:")
            print("   1. 访问: https://developer.android.com/studio/releases/platform-tools")
            print("   2. 下载 'SDK Platform-Tools for Windows'")
            print("   3. 解压到当前目录的 android-tools 文件夹")
            return False
    
    def guide_phone_setup(self):
        """指导手机设置"""
        self.print_step(4, "手机连接指导")
        
        print("\n📱 请按照以下步骤设置您的手机:")
        print("\n🔧 开启开发者选项:")
        print("   1. 打开手机'设置'")
        print("   2. 找到'关于手机'或'关于设备'")
        print("   3. 连续点击'版本号'7次")
        print("   4. 返回设置，找到'开发者选项'")
        
        print("\n🔧 开启USB调试:")
        print("   1. 进入'开发者选项'")
        print("   2. 开启'USB调试'")
        print("   3. 开启'USB安装'(如果有)")
        
        print("\n🔧 连接手机:")
        print("   1. 用USB数据线连接手机到电脑")
        print("   2. 手机会弹出'允许USB调试'对话框")
        print("   3. 勾选'始终允许来自这台计算机'")
        print("   4. 点击'确定'")
        
        while True:
            response = input("\n手机已按上述步骤设置完成了吗? (y/n): ").strip().lower()
            if response == 'y':
                return True
            elif response == 'n':
                print("💡 请完成手机设置后继续")
            else:
                print("请输入 y 或 n")
    
    def check_device_connection(self):
        """检查设备连接"""
        self.print_step(5, "检测设备连接")
        
        if not self.adb_cmd:
            print("   ❌ ADB工具不可用")
            return False
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} devices")
        if not success:
            print("   ❌ 无法执行adb devices")
            return False
        
        print("   📱 设备列表:")
        print(stdout)
        
        lines = stdout.strip().split('\n')[1:]  # 跳过标题行
        devices = [line for line in lines if line.strip() and '\t' in line]
        
        if not devices:
            print("   ❌ 没有检测到设备")
            print("\n💡 请检查:")
            print("   - USB线连接是否正常")
            print("   - 手机是否开启USB调试")
            print("   - 是否授权此计算机")
            return False
        
        # 检查设备状态
        for device in devices:
            device_id, status = device.split('\t')
            print(f"   设备 {device_id}: {status}")
            
            if status == "unauthorized":
                print("   ⚠️ 设备未授权，请在手机上点击'始终允许'")
                return False
            elif status == "device":
                print("   ✅ 设备连接正常")
                return True
        
        return False
    
    def check_target_app(self):
        """检查翼支付应用"""
        self.print_step(6, "检查翼支付应用")
        
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell pm list packages | findstr bestpay")
        if success and stdout.strip():
            print("   ✅ 翼支付应用已安装")
            packages = stdout.strip().split('\n')
            for pkg in packages:
                print(f"      {pkg}")
            return True
        else:
            print("   ❌ 翼支付应用未安装")
            print("\n📱 请在手机上安装翼支付:")
            print("   - 华为手机: 华为应用市场")
            print("   - 小米手机: 小米应用商店")
            print("   - OPPO/vivo: 软件商店")
            print("   - 其他: Google Play 或官网下载")
            return False
    
    def setup_debugging(self):
        """设置调试环境"""
        self.print_step(7, "设置调试环境")
        
        # 设置端口转发
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} forward tcp:27042 tcp:27042")
        if success:
            print("   ✅ 端口转发已设置")
        else:
            print("   ❌ 端口转发设置失败")
            return False
        
        # 测试Frida连接
        print("   🔧 测试Frida连接...")
        success, stdout, stderr = self.run_command("frida-ps -U", timeout=10)
        if success:
            lines = stdout.strip().split('\n')
            process_count = len(lines) - 1  # 减去标题行
            print(f"   ✅ Frida连接成功，检测到 {process_count} 个进程")
        else:
            print("   ⚠️ Frida连接测试失败，但可能仍可调试")
        
        return True
    
    def start_hook_debug(self):
        """启动Hook调试"""
        print("\n" + "="*60)
        print("🎯 Hook调试启动")
        print("="*60)
        
        print("\n🎯 选择调试模式:")
        print("   1. 启动应用并Hook (推荐)")
        print("   2. 附加到运行中的应用")
        print("   3. 使用反调试绕过模式")
        
        while True:
            choice = input("\n请选择模式 (1-3): ").strip()
            if choice in ['1', '2', '3']:
                break
            print("请输入 1、2 或 3")
        
        # 构建Frida命令
        if choice == '1':
            frida_cmd = "frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause"
        elif choice == '2':
            frida_cmd = "frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js"
            print("\n💡 请先在手机上打开翼支付应用")
            input("按回车键继续...")
        else:
            frida_cmd = "frida -U -f com.chinatelecom.bestpayclient -l bypass_detection.js -l bestpay_crypto_hook.js --no-pause"
        
        print("\n🔥 Hook脚本正在启动...")
        print("\n📱 重要操作指南:")
        print("   1. 保持此窗口打开")
        print("   2. 在手机上操作翼支付应用")
        print("   3. 进入'权益商城'并刷新页面")
        print("   4. 观察此窗口的输出")
        print("   5. 看到包含AES密钥的输出时，立即复制保存!")
        
        print("\n🎯 寻找类似输出:")
        print("   🔑 [AES_decrypt] 调用")
        print("      密钥: 1234567890abcdef...")
        print("      解密结果: {\"activityId\":\"12345\",...}")
        
        print("\n" + "="*60)
        
        # 启动Frida
        success, stdout, stderr = self.run_command(frida_cmd, capture_output=False, timeout=3600)
        
        print("\n" + "="*60)
        print("🎉 Hook会话已结束")
        print("\n📊 如果成功捕获到数据，请运行数据收集工具:")
        print("   python data_collector.py")
        print("="*60)
    
    def run_full_setup(self):
        """运行完整设置流程"""
        self.print_banner()
        
        # 检查Python
        if not self.check_python():
            return False
        
        # 安装Frida
        if not self.install_frida():
            return False
        
        # 设置ADB
        if not self.setup_adb():
            return False
        
        # 指导手机设置
        if not self.guide_phone_setup():
            return False
        
        # 检查设备连接
        if not self.check_device_connection():
            return False
        
        # 检查翼支付应用
        if not self.check_target_app():
            return False
        
        # 设置调试环境
        if not self.setup_debugging():
            return False
        
        print("\n" + "="*60)
        print("🎉 环境配置完成!")
        print("="*60)
        
        response = input("\n是否立即开始Hook调试? (y/n): ").strip().lower()
        if response == 'y':
            self.start_hook_debug()
        
        return True

def main():
    assistant = SmartAssistant()
    
    print("🚀 选择操作:")
    print("   1. 完整环境检查和设置 (推荐新手)")
    print("   2. 快速启动调试 (环境已配置)")
    
    while True:
        choice = input("\n请选择 (1-2): ").strip()
        if choice in ['1', '2']:
            break
        print("请输入 1 或 2")
    
    if choice == '1':
        assistant.run_full_setup()
    else:
        assistant.start_hook_debug()

if __name__ == "__main__":
    main()
