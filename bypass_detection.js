// 翼支付反调试绕过脚本
// 绕过常见的调试检测和保护机制

console.log("🛡️ 启动反调试绕过脚本...");

// 绕过Frida检测
function bypassFridaDetection() {
    console.log("🔍 绕过Frida检测...");
    
    // Hook常见的Frida检测函数
    try {
        var fopen = Module.findExportByName("libc.so", "fopen");
        if (fopen) {
            Interceptor.attach(fopen, {
                onEnter: function(args) {
                    var filename = Memory.readUtf8String(args[0]);
                    
                    // 阻止读取Frida相关文件
                    if (filename.includes("frida") || 
                        filename.includes("gum-js-loop") ||
                        filename.includes("linjector")) {
                        console.log("🚫 阻止读取Frida文件: " + filename);
                        args[0] = Memory.allocUtf8String("/dev/null");
                    }
                }
            });
        }
        
        // Hook strstr函数，防止字符串检测
        var strstr = Module.findExportByName("libc.so", "strstr");
        if (strstr) {
            Interceptor.attach(strstr, {
                onEnter: function(args) {
                    var haystack = Memory.readUtf8String(args[0]);
                    var needle = Memory.readUtf8String(args[1]);
                    
                    if (needle.includes("frida") || 
                        needle.includes("gum") ||
                        needle.includes("xposed")) {
                        console.log("🚫 阻止字符串检测: " + needle);
                        this.replace();
                    }
                },
                onLeave: function(retval) {
                    if (this.replace) {
                        retval.replace(ptr(0));
                    }
                }
            });
        }
        
    } catch(e) {
        console.log("⚠️ Frida检测绕过失败: " + e);
    }
}

// 绕过Root检测
function bypassRootDetection() {
    console.log("🔍 绕过Root检测...");
    
    try {
        // Hook su命令检测
        var system = Module.findExportByName("libc.so", "system");
        if (system) {
            Interceptor.attach(system, {
                onEnter: function(args) {
                    var command = Memory.readUtf8String(args[0]);
                    if (command.includes("su") || 
                        command.includes("which su") ||
                        command.includes("/system/bin/su")) {
                        console.log("🚫 阻止su命令检测: " + command);
                        args[0] = Memory.allocUtf8String("echo 'not found'");
                    }
                }
            });
        }
        
        // Hook文件存在性检查
        var access = Module.findExportByName("libc.so", "access");
        if (access) {
            Interceptor.attach(access, {
                onEnter: function(args) {
                    var path = Memory.readUtf8String(args[0]);
                    
                    // 常见的Root检测路径
                    var rootPaths = [
                        "/system/bin/su",
                        "/system/xbin/su", 
                        "/sbin/su",
                        "/system/app/Superuser.apk",
                        "/system/app/SuperSU.apk"
                    ];
                    
                    if (rootPaths.some(p => path.includes(p))) {
                        console.log("🚫 阻止Root路径检测: " + path);
                        this.deny = true;
                    }
                },
                onLeave: function(retval) {
                    if (this.deny) {
                        retval.replace(-1); // 返回文件不存在
                    }
                }
            });
        }
        
    } catch(e) {
        console.log("⚠️ Root检测绕过失败: " + e);
    }
}

// 绕过调试器检测
function bypassDebuggerDetection() {
    console.log("🔍 绕过调试器检测...");
    
    try {
        // Hook ptrace函数
        var ptrace = Module.findExportByName("libc.so", "ptrace");
        if (ptrace) {
            Interceptor.attach(ptrace, {
                onEnter: function(args) {
                    var request = args[0].toInt32();
                    if (request === 0) { // PTRACE_TRACEME
                        console.log("🚫 阻止ptrace自我跟踪");
                        this.deny = true;
                    }
                },
                onLeave: function(retval) {
                    if (this.deny) {
                        retval.replace(0); // 返回成功
                    }
                }
            });
        }
        
        // Hook TracerPid检测
        var fgets = Module.findExportByName("libc.so", "fgets");
        if (fgets) {
            Interceptor.attach(fgets, {
                onLeave: function(retval) {
                    if (retval.isNull()) return;
                    
                    var content = Memory.readUtf8String(retval);
                    if (content.includes("TracerPid:")) {
                        // 将TracerPid设置为0（未被调试）
                        var newContent = content.replace(/TracerPid:\s*\d+/, "TracerPid:\t0");
                        Memory.writeUtf8String(retval, newContent);
                        console.log("🚫 修改TracerPid检测结果");
                    }
                }
            });
        }
        
    } catch(e) {
        console.log("⚠️ 调试器检测绕过失败: " + e);
    }
}

// 绕过模拟器检测
function bypassEmulatorDetection() {
    console.log("🔍 绕过模拟器检测...");
    
    try {
        Java.perform(function() {
            // Hook Build类属性
            var Build = Java.use("android.os.Build");
            
            // 修改常见的模拟器标识
            Build.MANUFACTURER.value = "samsung";
            Build.BRAND.value = "samsung"; 
            Build.MODEL.value = "SM-G973F";
            Build.PRODUCT.value = "beyond1lte";
            Build.DEVICE.value = "beyond1";
            Build.HARDWARE.value = "exynos9820";
            Build.FINGERPRINT.value = "samsung/beyond1ltexx/beyond1:10/QP1A.190711.020/G973FXXU3BSKC:user/release-keys";
            
            console.log("✅ 修改设备信息以绕过模拟器检测");
            
            // Hook SystemProperties
            try {
                var SystemProperties = Java.use("android.os.SystemProperties");
                SystemProperties.get.overload('java.lang.String').implementation = function(key) {
                    var result = this.get(key);
                    
                    // 修改模拟器相关属性
                    if (key.includes("ro.kernel.qemu") || 
                        key.includes("ro.hardware") ||
                        key.includes("ro.product.model")) {
                        
                        if (key === "ro.hardware") {
                            result = "exynos9820";
                        } else if (key === "ro.product.model") {
                            result = "SM-G973F";
                        } else {
                            result = "";
                        }
                        
                        console.log("🚫 修改系统属性: " + key + " = " + result);
                    }
                    
                    return result;
                };
            } catch(e) {
                console.log("⚠️ SystemProperties Hook失败: " + e);
            }
        });
        
    } catch(e) {
        console.log("⚠️ 模拟器检测绕过失败: " + e);
    }
}

// 绕过SSL Pinning
function bypassSSLPinning() {
    console.log("🔍 绕过SSL Pinning...");
    
    try {
        Java.perform(function() {
            // Hook OkHttp3 CertificatePinner
            try {
                var CertificatePinner = Java.use("okhttp3.CertificatePinner");
                CertificatePinner.check.overload('java.lang.String', 'java.util.List').implementation = function(hostname, peerCertificates) {
                    console.log("🚫 绕过OkHttp3 SSL Pinning: " + hostname);
                    return;
                };
            } catch(e) {}
            
            // Hook HttpsURLConnection
            try {
                var HttpsURLConnection = Java.use("javax.net.ssl.HttpsURLConnection");
                HttpsURLConnection.setDefaultHostnameVerifier.implementation = function(hostnameVerifier) {
                    console.log("🚫 绕过HttpsURLConnection主机名验证");
                    return;
                };
            } catch(e) {}
            
            // Hook X509TrustManager
            try {
                var X509TrustManager = Java.use("javax.net.ssl.X509TrustManager");
                var TrustManager = Java.registerClass({
                    name: "com.frida.TrustManager",
                    implements: [X509TrustManager],
                    methods: {
                        checkClientTrusted: function(chain, authType) {},
                        checkServerTrusted: function(chain, authType) {},
                        getAcceptedIssuers: function() { return []; }
                    }
                });
                
                var SSLContext = Java.use("javax.net.ssl.SSLContext");
                var TrustManagerArray = Java.array("javax.net.ssl.TrustManager", [TrustManager.$new()]);
                
                SSLContext.init.overload('[Ljavax.net.ssl.KeyManager;', '[Ljavax.net.ssl.TrustManager;', 'java.security.SecureRandom').implementation = function(keyManagers, trustManagers, secureRandom) {
                    console.log("🚫 绕过SSL证书验证");
                    this.init(keyManagers, TrustManagerArray, secureRandom);
                };
                
            } catch(e) {}
        });
        
    } catch(e) {
        console.log("⚠️ SSL Pinning绕过失败: " + e);
    }
}

// 绕过反Hook检测
function bypassAntiHook() {
    console.log("🔍 绕过反Hook检测...");
    
    try {
        // Hook dlopen函数，防止动态加载反调试库
        var dlopen = Module.findExportByName("libdl.so", "dlopen");
        if (dlopen) {
            Interceptor.attach(dlopen, {
                onEnter: function(args) {
                    var library = Memory.readUtf8String(args[0]);
                    
                    // 阻止加载已知的反调试库
                    if (library.includes("libijm") || 
                        library.includes("libshield") ||
                        library.includes("libprotect")) {
                        console.log("🚫 阻止加载反调试库: " + library);
                        args[0] = Memory.allocUtf8String("/dev/null");
                    }
                }
            });
        }
        
        // Hook mprotect函数，防止内存保护
        var mprotect = Module.findExportByName("libc.so", "mprotect");
        if (mprotect) {
            Interceptor.attach(mprotect, {
                onEnter: function(args) {
                    var addr = args[0];
                    var len = args[1].toInt32();
                    var prot = args[2].toInt32();
                    
                    // 如果试图设置为不可执行，改为可读写执行
                    if ((prot & 0x4) === 0) { // 没有执行权限
                        args[2] = ptr(0x7); // PROT_READ | PROT_WRITE | PROT_EXEC
                        console.log("🚫 修改内存保护权限");
                    }
                }
            });
        }
        
    } catch(e) {
        console.log("⚠️ 反Hook检测绕过失败: " + e);
    }
}

// 主函数
function main() {
    console.log("🚀 开始绕过各种检测机制...");
    
    // 延迟执行，等待应用初始化
    setTimeout(function() {
        bypassFridaDetection();
        bypassRootDetection();
        bypassDebuggerDetection();
        bypassEmulatorDetection();
        bypassSSLPinning();
        bypassAntiHook();
        
        console.log("✅ 所有绕过脚本已加载完成!");
        console.log("📱 现在可以安全地进行Hook调试了");
        
    }, 1000);
}

// 启动绕过脚本
main();
