{"BestPayPageList": [{"pageName": "riseStar", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.GradePromoteUnifiedEntranceActivity"}, {"pageName": "bindCard", "type": 1, "className": "com.bestpay.android.bindcard.activity.upgrade.RouterBridge"}, {"pageName": "recharge", "type": 1, "className": "com.bestpay.android.bestwallet.recharge.RechargeMainActivity"}, {"pageName": "withdraw", "type": 1, "className": "com.bestpay.android.bestwallet.withdraw.ui.WithdrawCashActivity"}, {"pageName": "forgetLoginPassword", "type": 1, "className": "com.bestpay.android.account.pwdmanager.AccountForgetLoginPwdActivity"}, {"pageName": "forgetPayPassword", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.AccountForgetPasswordAnim"}, {"pageName": "voucher", "type": 1, "className": "com.chinatelecom.bestpayclient.mvp.voucher.VoucherActivity"}, {"pageName": "payCode", "type": 1, "className": "com.chinatelecom.bestpayclient.ui.activity.BestPaymentCodeActivity"}, {"pageName": "transfer", "type": 1, "className": "com.bestpay.android.bestwallet.transfer.activity.TransferMainActivity"}, {"pageName": "transfertoaccount", "type": 1, "className": "com.bestpay.android.bestwallet.transfer.activity.TransferToBestPayActivity"}, {"pageName": "transfertobankcard", "type": 1, "className": "com.bestpay.android.bestwallet.transfer.activity.TransferToBankCardActivity"}, {"pageName": "news", "type": 1, "className": "com.chinatelecom.bestpayclient.ui.activity.MsgCenterActivity"}, {"pageName": "userInfo", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.AccountPersonalInfoActivity"}, {"pageName": "userStar", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.AccountLevelActivity"}, {"pageName": "editEmail", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.AccountPersonalInputMailBoxActivity"}, {"pageName": "editNickname", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.AccountPersonalNickNameActivity"}, {"pageName": "InfoAuth", "type": 1, "className": "com.bestpay.android.account.idcardupload.ui.other.SdkAccountConfirmIdentityActivity"}, {"pageName": "accountBalance", "type": 1, "className": "com.chinatelecom.bestpayclient.ui.activity.AccountBalanceDetailsActivity"}, {"pageName": "orderList", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.order.AccountUnifiedOrderActivity"}, {"pageName": "frozen", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.AccountFrozenDetailsActivity"}, {"pageName": "bankManager", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.AccountBankCardManagerActivity"}, {"pageName": "withdrawRecords", "type": 1, "className": "com.bestpay.android.bestwallet.withdraw.ui.WithdrawCashRecordActivity"}, {"pageName": "myInvitation", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.InviteActivity"}, {"pageName": "settings", "type": 1, "className": "com.chinatelecom.bestpayclient.router.AccountSettingRouterActivity"}, {"pageName": "generalSetting", "type": 1, "className": "com.bestpay.android.setting.general.ui.AccountGeneralSettings1Activity"}, {"pageName": "pwdSetting", "type": 1, "className": "com.bestpay.android.password.PwdManagerActivity"}, {"pageName": "security", "type": 1, "className": "com.bestpay.android.setting.security.activity.AccountSecurityCenterActivity"}, {"pageName": "about", "type": 1, "className": "com.bestpay.android.setting.about.ui.AccountAboutActivity"}, {"pageName": "closingAccount", "type": 1, "className": "com.chinatelecom.bestpayclient.router.CancelAccountRouterActivity"}, {"pageName": "uploadIDCard", "type": 1, "className": "com.bestpay.android.account.idcardupload.ui.other.SdkAccountIdCardInfoActivity"}, {"pageName": "thaw", "type": 1, "className": "com.bestpay.account.ui.activity.AccountSelfFreezeActivity"}, {"pageName": "billdiscount", "type": 1, "className": "com.chinatelecom.bestpayclient.account.activity.order.MonthDiscountBill"}]}