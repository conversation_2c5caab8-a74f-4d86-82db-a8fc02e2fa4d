
// 加密函数跟踪脚本
console.log("🔐 加密函数跟踪启动");

// 跟踪OpenSSL函数
var openssl_funcs = [
    "AES_encrypt", "AES_decrypt", "AES_set_encrypt_key", "AES_set_decrypt_key",
    "RSA_public_encrypt", "RSA_private_decrypt",
    "EVP_EncryptInit", "EVP_DecryptInit", "EVP_CipherUpdate", "EVP_CipherFinal",
    "MD5_Init", "MD5_Update", "MD5_Final",
    "SHA1_Init", "SHA1_Update", "SHA1_Final"
];

openssl_funcs.forEach(function(func) {
    try {
        var funcPtr = Module.findExportByName("libssl.so", func) || 
                     Module.findExportByName("libcrypto.so", func) ||
                     Module.findExportByName("libopenssl.so", func);
        
        if (funcPtr) {
            Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                    console.log("🔑 [" + func + "] 调用");
                    
                    // 记录密钥和数据
                    if (func.includes("encrypt") || func.includes("decrypt")) {
                        try {
                            var data = Memory.readByteArray(args[1], Math.min(32, 16));
                            console.log("  数据: " + hexdump(data, {ansi: true}));
                        } catch(e) {}
                    }
                },
                onLeave: function(retval) {
                    console.log("  返回值: " + retval);
                }
            });
            console.log("✅ " + func + " 跟踪已设置");
        }
    } catch(e) {
        console.log("❌ " + func + " 跟踪失败: " + e);
    }
});

// 跟踪国密算法
var sm_funcs = ["sm2_encrypt", "sm2_decrypt", "sm3_hash", "sm4_encrypt", "sm4_decrypt"];

sm_funcs.forEach(function(func) {
    try {
        var funcPtr = Module.findExportByName("libantssm.so", func) ||
                     Module.findExportByName("libTencentSM.so", func);
        
        if (funcPtr) {
            Interceptor.attach(funcPtr, {
                onEnter: function(args) {
                    console.log("🇨🇳 [" + func + "] 国密算法调用");
                },
                onLeave: function(retval) {
                    console.log("  返回值: " + retval);
                }
            });
            console.log("✅ " + func + " 国密跟踪已设置");
        }
    } catch(e) {
        console.log("❌ " + func + " 国密跟踪失败: " + e);
    }
});
