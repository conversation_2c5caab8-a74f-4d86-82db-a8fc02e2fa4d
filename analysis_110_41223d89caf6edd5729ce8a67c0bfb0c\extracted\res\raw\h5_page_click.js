 (function(){
   if (window.NEBULALOGHASADDEDTOUCHEVENT) {
     return;
   };
   function onDOMReady(callback){
     var readyRE = /complete|loaded|interactive/;
     if(readyRE.test(document.readyState)) {
       setTimeout(function() {
         callback();
       }, 1);
     } else {
       document.defaultView.addEventListener('DOMContentLoaded', function () {
         callback();
       }, false);
     }
   }
   
   function getClickParam(el) {
     var res = {};
     res.element_id = el.id;
     res.element_content = el.textContent
     res.element_name = el.getAttribute('name') || undefined;
     res.element_class_name = el.getAttribute('class') || undefined;
     res.element_type = el.nodeName;
     res.element_selector = fullPath(el);
     res.element_target_url = el.nodeName === 'A' ? el.getAttribute('href') : undefined;
     const pageParams = getPageParam();
     res.viewport_height = pageParams.viewport_height;
     res.viewport_width = pageParams.viewport_width;
     res.url = pageParams.url;
     res.title = pageParams.title;
     res.url_path = pageParams.url_path;
     
     return res;
   }
   
   function fullPath(el){
     var names = [];
     while (el.parentNode){
       if (el.id){
         names.unshift('#'+el.id);
         break;
       }else{
         if (el==el.ownerDocument.documentElement) names.unshift(el.tagName);
         else{
           for (var c=1,e=el;e.previousElementSibling;e=e.previousElementSibling,c++);
           names.unshift(el.tagName+":nth-child("+c+")");
         }
         el=el.parentNode;
       }
     }
     return names.join(" > ");
   }
   
   function getPageParam(currentTime) {
     return {
       viewport_width: window.innerWidth,
       viewport_position: window.scrollY,
       viewport_height: window.innerHeight,
       url: window.location.href,
       title: sessionStorage.getItem('H5_PAGE_TITLE') || document.title,
       url_path: window.location.pathname,
//       eventDuration: currentTime || new Date().valueOf() - window_stay_timestamp
     };
   }
   
   onDOMReady(function(){
     document.addEventListener("touchstart",function(event){
       AlipayJSBridge.call("h5PageClick", getClickParam(event.target));
     }, false);
   });
   window.NEBULALOGHASADDEDTOUCHEVENT = true;
 })();
