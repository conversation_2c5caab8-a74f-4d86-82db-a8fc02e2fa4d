#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化Hook分析工具
快速诊断Hook失败原因
"""

import subprocess
import time
import os

class SimpleHookAnalyzer:
    def __init__(self):
        self.adb_cmd = r".\android-tools\platform-tools\adb.exe"
        print("🔍 简化Hook失败分析工具")
        print("=" * 50)
    
    def run_command(self, cmd, timeout=15):
        """执行命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout, encoding='utf-8', errors='ignore')
            return result.returncode == 0, result.stdout, result.stderr
        except Exception as e:
            return False, "", str(e)
    
    def check_basic_environment(self):
        """检查基础环境"""
        print("\n🔧 检查基础环境...")
        
        # 检查ADB连接
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} devices")
        if success and "device" in stdout:
            print("   ✅ ADB连接正常")
        else:
            print("   ❌ ADB连接失败")
            return False
        
        # 检查Root权限
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell su -c 'id'")
        if success and "uid=0" in stdout:
            print("   ✅ Root权限正常")
        else:
            print("   ❌ Root权限不可用")
            return False
        
        # 检查Frida Server
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell ps | findstr frida")
        if success and stdout.strip():
            print("   ✅ Frida Server正在运行")
        else:
            print("   ❌ Frida Server未运行")
            return False
        
        return True
    
    def check_frida_connection(self):
        """检查Frida连接"""
        print("\n🔗 检查Frida连接...")
        
        success, stdout, stderr = self.run_command("frida-ps -U")
        if success:
            lines = stdout.strip().split('\n')
            process_count = len(lines) - 1
            print(f"   ✅ Frida连接成功，检测到 {process_count} 个进程")
            return True
        else:
            print(f"   ❌ Frida连接失败: {stderr}")
            return False
    
    def check_target_app(self):
        """检查目标应用"""
        print("\n📱 检查翼支付应用...")
        
        # 检查应用是否安装
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell pm list packages | findstr bestpay")
        if success and "com.chinatelecom.bestpayclient" in stdout:
            print("   ✅ 翼支付应用已安装")
        else:
            print("   ❌ 翼支付应用未安装")
            return False
        
        # 检查应用是否在运行
        success, stdout, stderr = self.run_command("frida-ps -U | findstr -i bestpay")
        if success and stdout.strip():
            print("   ✅ 翼支付应用正在运行")
            for line in stdout.strip().split('\n'):
                print(f"      {line}")
            return True
        else:
            print("   ❌ 翼支付应用未运行")
            return False
    
    def test_simple_hook(self):
        """测试简单Hook"""
        print("\n🧪 测试简单Hook...")
        
        # 创建最简单的测试脚本
        test_script = '''
console.log("🧪 开始Hook测试");

setTimeout(function() {
    console.log("⏰ 延迟执行测试");
    
    Java.perform(function() {
        console.log("✅ Java环境可用");
        
        try {
            var System = Java.use("java.lang.System");
            console.log("✅ 系统类Hook成功");
            
            console.log("🎉 Hook测试完成 - 环境正常");
        } catch(e) {
            console.log("❌ Hook测试失败: " + e);
        }
    });
}, 2000);
'''
        
        with open("simple_test.js", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        print("   🚀 执行Hook测试...")
        
        # 先检查应用是否在运行
        success, stdout, stderr = self.run_command("frida-ps -U | findstr -i bestpay")
        if not success or not stdout.strip():
            print("   ⚠️ 翼支付应用未运行，尝试启动...")
            
            # 尝试启动应用
            success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell am start -n com.chinatelecom.bestpayclient/.ui.activity.MainActivity")
            if success:
                print("   📱 应用启动命令已发送，等待5秒...")
                time.sleep(5)
            else:
                print("   ❌ 应用启动失败")
                return False
        
        # 执行Hook测试
        try:
            process = subprocess.Popen(
                "frida -U com.chinatelecom.bestpayclient -l simple_test.js",
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            # 等待10秒
            try:
                stdout, stderr = process.communicate(timeout=10)
                
                print("   📊 Hook测试结果:")
                if "Hook测试完成" in stdout:
                    print("      ✅ Hook测试成功")
                    return True
                elif "Process terminated" in stdout:
                    print("      ❌ 应用进程被终止 (反调试保护)")
                    return False
                elif "Failed to attach" in stderr:
                    print("      ❌ 无法附加到进程")
                    return False
                else:
                    print("      ⚠️ Hook测试结果不明确")
                    print(f"      输出: {stdout[:200]}...")
                    print(f"      错误: {stderr[:200]}...")
                    return False
                    
            except subprocess.TimeoutExpired:
                process.kill()
                print("      ⏰ Hook测试超时")
                return False
                
        except Exception as e:
            print(f"      ❌ Hook测试执行失败: {e}")
            return False
    
    def analyze_failure_reasons(self):
        """分析失败原因"""
        print("\n🔍 分析可能的失败原因...")
        
        reasons = []
        
        # 检查应用状态
        success, stdout, stderr = self.run_command("frida-ps -U | findstr -i bestpay")
        if not success or not stdout.strip():
            reasons.append("翼支付应用未运行")
        
        # 检查应用保护
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell pm dump com.chinatelecom.bestpayclient | findstr debuggable")
        if success and "debuggable=false" in stdout:
            reasons.append("应用为Release版本，有反调试保护")
        
        # 检查SELinux
        success, stdout, stderr = self.run_command(f"{self.adb_cmd} shell getenforce")
        if success and "Enforcing" in stdout:
            reasons.append("SELinux处于强制模式")
        
        if reasons:
            print("   🎯 发现的问题:")
            for i, reason in enumerate(reasons, 1):
                print(f"      {i}. {reason}")
        else:
            print("   ✅ 未发现明显问题")
        
        return reasons
    
    def provide_solutions(self, reasons):
        """提供解决方案"""
        print("\n💡 解决方案建议...")
        
        solutions = []
        
        if "翼支付应用未运行" in reasons:
            solutions.extend([
                "手动在手机上打开翼支付应用",
                "使用adb启动应用: adb shell am start -n com.chinatelecom.bestpayclient/.ui.activity.MainActivity",
                "确保应用保持在前台运行"
            ])
        
        if "应用为Release版本，有反调试保护" in reasons:
            solutions.extend([
                "使用Charles代理抓包 (推荐)",
                "在模拟器环境中调试",
                "使用Xposed框架绕过保护",
                "分析APK中的反调试机制"
            ])
        
        if "SELinux处于强制模式" in reasons:
            solutions.extend([
                "尝试临时设置SELinux为宽松模式",
                "使用Magisk模块管理SELinux"
            ])
        
        # 通用解决方案
        solutions.extend([
            "使用Charles代理抓包 (成功率最高)",
            "静态分析APK文件",
            "网络层抓包分析",
            "使用模拟器环境调试"
        ])
        
        print("   📋 推荐方案 (按优先级排序):")
        for i, solution in enumerate(solutions[:8], 1):
            print(f"      {i}. {solution}")
    
    def run_analysis(self):
        """运行分析"""
        print("🎯 开始Hook失败分析...")
        
        # 基础环境检查
        if not self.check_basic_environment():
            print("\n❌ 基础环境有问题，请先解决环境配置")
            return
        
        # Frida连接检查
        if not self.check_frida_connection():
            print("\n❌ Frida连接有问题")
            return
        
        # 目标应用检查
        app_running = self.check_target_app()
        
        # Hook测试
        hook_success = self.test_simple_hook()
        
        # 分析失败原因
        reasons = self.analyze_failure_reasons()
        
        # 提供解决方案
        self.provide_solutions(reasons)
        
        print("\n" + "=" * 50)
        print("🎉 Hook分析完成!")
        
        if hook_success:
            print("✅ Hook环境正常，可以进行调试")
            print("💡 建议使用增强版Hook脚本: debug_hook_with_logs.js")
        else:
            print("❌ Hook环境有问题")
            print("💡 强烈建议使用Charles代理抓包方案")
        
        print("\n📁 生成的测试文件:")
        print("   - simple_test.js (Hook测试脚本)")
        print("=" * 50)

def main():
    analyzer = SimpleHookAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
