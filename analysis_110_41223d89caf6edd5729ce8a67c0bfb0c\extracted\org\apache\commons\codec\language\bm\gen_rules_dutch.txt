/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// CONSONANTS
"ssj" "" "" "S"
"sj" "" "" "S"
"ch" "" "" "x"
"c" "" "[eiy]" "ts"   
"ck" "" "" "k"     // German
"pf" "" "" "(pf|p|f)" // German
"ph" "" "" "(ph|f)"
"qu" "" "" "kv"
"th" "^" "" "t" // German
"th" "" "[äöüaeiou]" "(t|th)" // German
"th" "" "" "t" // German
"ss" "" "" "s"
"h" "[aeiouy]" "" ""

// VOWELS
"aue" "" "" "aue" 
"ou" "" "" "au" 
"ie" "" "" "(Q|i)" 
"uu" "" "" "(Q|u)"   
"ee" "" "" "e"   
"eu" "" "" "(Y|Yj)" // Dutch Y  
"aa" "" "" "a"   
"oo" "" "" "o"   
"oe" "" "" "u"   
"ij" "" "" "ej"
"ui" "" "" "(Y|uj)"
"ei" "" "" "(ej|aj)" // Dutch ej

"i" "" "[aou]" "j"
"y" "" "[aeou]" "j"
"i" "[aou]" "" "j"
"y" "[aeou]" "" "j"

// LATIN ALPHABET     
"a" "" "" "a"
"b" "" "" "b"
"c" "" "" "k"
"d" "" "" "d"
"e" "" "" "e"
"f" "" "" "f"
"g" "" "" "(g|x)"
"h" "" "" "h"
"i" "" "" "(i|Q)"   
"j" "" "" "j"
"k" "" "" "k"
"l" "" "" "l"
"m" "" "" "m"
"n" "" "" "n"
"o" "" "" "o"
"p" "" "" "p"
"q" "" "" "k"
"r" "" "" "r"
"s" "" "" "s"
"t" "" "" "t"
"u" "" "" "(u|Q)"   
"v" "" "" "v"
"w" "" "" "(w|v)"
"x" "" "" "ks"
"y" "" "" "i"
"z" "" "" "z"
