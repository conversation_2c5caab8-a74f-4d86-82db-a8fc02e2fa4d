window.AlipayJSBridge||function(){console.log("begin load AlipayJSBridge");var messenger=window.__alipayConsole__||window.console,log=messenger.log,e=function(e){log.call(messenger,"{bridge_token}h5container.message: "+JSON.stringify(e))},a=null,n=null;window.addEventListener("message",function(e){if(e&&"__RENDER_WORKER_IPC_MP__"===e.data&&null===n&&e.ports&&1===e.ports.length){(n=e.ports[0]).onmessage=function(e){try{var a=JSON.parse(e.data),n='postMessage'===a.handlerName?'message':a.handlerName;if(a.responseId){var l=t[a.responseId];'boolean'==typeof a.keepCallback&&a.keepCallback||delete t[a.responseId];'function'==typeof l&&setTimeout(function(){l(a.responseData)},0)}else n&&AlipayJSBridge.trigger(n,a,a.callbackId)}catch(a){log.call(messenger,"failed to process message: "+e.data);log.call(messenger,a)}};a=function(e){e.__FastPath__=1;var a=JSON.stringify(e);n.postMessage(a)}}});var t={};window.AlipayJSBridge={call:function(n,l,i){if('string'==typeof n){if('function'==typeof l){i=l;l=null}else'object'!=typeof l&&(l=null);var o=''+(new Date).getTime()+Math.random();'function'==typeof i&&(t[o]=i);var c={func:n,param:l,msgType:'call',clientId:o};null!==a&&'postMessage'===n?a(c):e(c)}},callback:function(n,t){var l={clientId:n,param:t};null!==a&&'string'==typeof n&&'native_'!==n.substring(0,7)?a(l):e(l)},trigger:function(e,a,n){if(e){var t=document.createEvent('Events');t.initEvent(e,!1,!0);t.syncJsApis=[];if('object'==typeof a)for(var l in a)t[l]=a[l];t.clientId=n;var i=!document.dispatchEvent(t);n&&'back'===e&&AlipayJSBridge.callback(n,{prevent:i});n&&'closeWindow'===e&&AlipayJSBridge.callback(n,{prevent:i});n&&'firePullToRefresh'===e&&AlipayJSBridge.callback(n,{prevent:i});n&&'onShare'===e&&AlipayJSBridge.callback(n,{prevent:i});n&&'collectDestroyJsApi'===e&&AlipayJSBridge.callback(n,{syncJsApis:t.syncJsApis});n&&'pullIntercept'===e&&AlipayJSBridge.callback(n,{prevent:i});n&&'segControlClick'===e&&AlipayJSBridge.callback(n,{prevent:i})}},_invokeJS:function(e){e=JSON.parse(e);console.log("invokeJS msgType "+e.msgType+" func "+e.func);if('callback'===e.msgType){var a=t[e.clientId];'boolean'==typeof e.keepCallback&&e.keepCallback||delete t[e.clientId];'function'==typeof a&&setTimeout(function(){a(e.param)},1)}else'call'===e.msgType&&e.func&&this.trigger(e.func,e.param,e.clientId)},devPerformance4Test:""};AlipayJSBridge.startupParams='{startupParams}';window.APVIEWID='{APVIEWID}';var l=document.createEvent('Events');l.initEvent('AlipayJSBridgeReady',!1,!1);var i=document.addEventListener;document.addEventListener=function(e,a){e===l.type&&setTimeout(function(){a(l)},1);i.apply(document,arguments)};document.dispatchEvent(l);console.log("load AlipayJSBridge dispatchEvent AlipayJSBridgeReady")}();
// do not modify
