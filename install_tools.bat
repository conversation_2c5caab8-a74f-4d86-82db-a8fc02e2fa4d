@echo off
chcp 65001 >nul
echo 🛠️ 翼支付调试工具自动安装脚本
echo ========================================

echo.
echo 📦 第1步: 安装Frida...
echo.
pip install frida-tools
if %errorlevel% neq 0 (
    echo ❌ Frida安装失败，尝试升级pip...
    pip install --upgrade pip
    echo 重新安装Frida...
    pip install frida-tools
    if %errorlevel% neq 0 (
        echo ❌ Frida安装仍然失败
        echo 💡 请检查网络连接或尝试手动安装
        pause
        exit /b 1
    )
)
echo ✅ Frida安装成功

echo.
echo 📦 第2步: 下载ADB工具...
echo.

REM 检查是否已有ADB
where adb >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ ADB已存在，跳过下载
    goto :check_device
)

echo 正在下载Android Platform Tools...
echo 💡 这可能需要几分钟时间...

REM 创建tools目录
if not exist "android-tools" mkdir android-tools
cd android-tools

REM 下载platform-tools
echo 下载中...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://dl.google.com/android/repository/platform-tools-latest-windows.zip' -OutFile 'platform-tools.zip'}"

if not exist "platform-tools.zip" (
    echo ❌ 下载失败，请手动下载ADB
    echo 💡 下载地址: https://developer.android.com/studio/releases/platform-tools
    echo 💡 下载后解压到当前目录的android-tools文件夹
    pause
    exit /b 1
)

echo 解压中...
powershell -Command "Expand-Archive -Path 'platform-tools.zip' -DestinationPath '.' -Force"

if not exist "platform-tools\adb.exe" (
    echo ❌ 解压失败
    pause
    exit /b 1
)

echo ✅ ADB下载完成

REM 添加到PATH
set "CURRENT_DIR=%CD%"
set "ADB_PATH=%CURRENT_DIR%\platform-tools"
echo.
echo 📝 添加ADB到系统PATH...
echo 当前ADB路径: %ADB_PATH%

REM 临时添加到当前会话的PATH
set "PATH=%PATH%;%ADB_PATH%"

echo ✅ ADB已添加到当前会话PATH

cd ..

:check_device
echo.
echo 📱 第3步: 检查设备连接...
echo.
echo 💡 请确保：
echo    1. 手机已通过USB连接到电脑
echo    2. 手机已开启"开发者选项"中的"USB调试"
echo    3. 手机屏幕已解锁
echo.
pause

echo 检查设备连接...
adb devices
echo.
echo ❓ 能看到您的设备吗？
echo    - 如果显示 "device"，说明连接成功
echo    - 如果显示 "unauthorized"，请在手机上授权此计算机
echo    - 如果没有设备，请检查USB连接和驱动
echo.
pause

echo.
echo 📦 第4步: 设置端口转发...
adb forward tcp:27042 tcp:27042
if %errorlevel% equ 0 (
    echo ✅ 端口转发设置成功
) else (
    echo ❌ 端口转发设置失败
    echo 💡 请确保设备已正确连接
)

echo.
echo 📦 第5步: 验证Frida安装...
frida --version
if %errorlevel% equ 0 (
    echo ✅ Frida验证成功
) else (
    echo ❌ Frida验证失败
)

echo.
echo 📦 第6步: 测试Frida连接...
frida-ps -U
if %errorlevel% equ 0 (
    echo ✅ Frida连接测试成功
) else (
    echo ❌ Frida连接测试失败
    echo 💡 这可能是正常的，如果设备未Root
)

echo.
echo ========================================
echo 🎉 安装脚本执行完成！
echo.
echo 📋 下一步：
echo    1. 确保翼支付应用已安装在手机上
echo    2. 运行 python diagnose_issues.py 重新检查
echo    3. 如果一切正常，运行 auto_debug.bat 开始调试
echo.
echo ⚠️  重要提醒：
echo    - 如果这是第一次运行，可能需要重启命令行窗口
echo    - 确保ADB路径已正确添加到系统PATH
echo.
pause
