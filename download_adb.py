#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ADB工具下载脚本
"""

import os
import urllib.request
import zipfile
import sys

def download_adb():
    print("📦 下载Android Platform Tools...")
    
    # 创建目录
    tools_dir = "android-tools"
    if not os.path.exists(tools_dir):
        os.makedirs(tools_dir)
    
    # 下载URL
    url = "https://dl.google.com/android/repository/platform-tools-latest-windows.zip"
    zip_path = os.path.join(tools_dir, "platform-tools.zip")
    
    try:
        print("正在下载... (这可能需要几分钟)")
        urllib.request.urlretrieve(url, zip_path)
        print("✅ 下载完成")
        
        # 解压
        print("正在解压...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(tools_dir)
        
        # 检查ADB是否存在
        adb_path = os.path.join(tools_dir, "platform-tools", "adb.exe")
        if os.path.exists(adb_path):
            print("✅ ADB解压成功")
            
            # 添加到PATH
            current_dir = os.path.abspath(tools_dir)
            adb_dir = os.path.join(current_dir, "platform-tools")
            
            print(f"📝 ADB路径: {adb_dir}")
            print("💡 请手动将此路径添加到系统PATH，或使用完整路径调用ADB")
            
            # 测试ADB
            print("测试ADB...")
            os.system(f'"{adb_path}" version')
            
            return True
        else:
            print("❌ ADB文件未找到")
            return False
            
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print("💡 请手动下载:")
        print("   1. 访问: https://developer.android.com/studio/releases/platform-tools")
        print("   2. 下载Windows版本")
        print("   3. 解压到android-tools文件夹")
        return False

if __name__ == "__main__":
    download_adb()
