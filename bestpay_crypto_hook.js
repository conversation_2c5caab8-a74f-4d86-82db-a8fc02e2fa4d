// 翼支付加密Hook脚本
// 用于捕获data字段的加密过程和密钥

console.log("🔐 翼支付加密Hook脚本启动...");

// 全局变量存储捕获的数据
var captured_keys = [];
var captured_data = [];

// Hook AES相关函数
function hookAESFunctions() {
    console.log("🔍 开始Hook AES函数...");
    
    // 尝试Hook不同库中的AES函数
    var libraries = [
        "libmpaas_crypto.so",
        "libantssm.so", 
        "libopenssl.so",
        "libqcOpenSSL.so",
        "libCryptoOperAd.so"
    ];
    
    var aes_functions = [
        "AES_encrypt",
        "AES_decrypt", 
        "AES_set_encrypt_key",
        "AES_set_decrypt_key",
        "AES_cbc_encrypt",
        "EVP_EncryptInit_ex",
        "EVP_DecryptInit_ex",
        "EVP_CipherInit_ex"
    ];
    
    libraries.forEach(function(lib) {
        aes_functions.forEach(function(func) {
            try {
                var addr = Module.findExportByName(lib, func);
                if (addr) {
                    console.log("✅ 找到函数: " + lib + "!" + func);
                    
                    Interceptor.attach(addr, {
                        onEnter: function(args) {
                            console.log("\n🔑 [" + func + "] 调用");
                            
                            if (func.includes("encrypt") || func.includes("decrypt")) {
                                // 打印输入数据
                                if (args[0] && args[0].readByteArray) {
                                    try {
                                        var input = args[0].readByteArray(Math.min(64, 32));
                                        console.log("   输入数据: " + hexdump(input, {ansi: true}));
                                    } catch(e) {}
                                }
                                
                                // 打印密钥
                                if (args[1] && args[1].readByteArray) {
                                    try {
                                        var key = args[1].readByteArray(32);
                                        console.log("   密钥: " + hexdump(key, {ansi: true}));
                                        
                                        // 保存密钥
                                        captured_keys.push({
                                            function: func,
                                            library: lib,
                                            key: key,
                                            timestamp: new Date().toISOString()
                                        });
                                    } catch(e) {}
                                }
                                
                                // 打印IV (如果是CBC模式)
                                if (func.includes("cbc") && args[2]) {
                                    try {
                                        var iv = args[2].readByteArray(16);
                                        console.log("   IV: " + hexdump(iv, {ansi: true}));
                                    } catch(e) {}
                                }
                            }
                        },
                        onLeave: function(retval) {
                            console.log("   返回值: " + retval);
                        }
                    });
                }
            } catch(e) {
                // 忽略错误，继续尝试其他函数
            }
        });
    });
}

// Hook Base64编码函数
function hookBase64Functions() {
    console.log("🔍 开始Hook Base64函数...");
    
    var base64_functions = [
        "Base64_encode",
        "Base64_decode",
        "base64_encode", 
        "base64_decode",
        "EVP_EncodeBlock",
        "EVP_DecodeBlock"
    ];
    
    var libraries = [
        "libmpaas_crypto.so",
        "libopenssl.so",
        "libqcOpenSSL.so"
    ];
    
    libraries.forEach(function(lib) {
        base64_functions.forEach(function(func) {
            try {
                var addr = Module.findExportByName(lib, func);
                if (addr) {
                    console.log("✅ 找到Base64函数: " + lib + "!" + func);
                    
                    Interceptor.attach(addr, {
                        onEnter: function(args) {
                            console.log("\n📝 [" + func + "] 调用");
                            
                            // 打印输入数据
                            if (args[0] && args[0].readByteArray) {
                                try {
                                    var input = args[0].readByteArray(Math.min(64, 32));
                                    console.log("   输入: " + hexdump(input, {ansi: true}));
                                } catch(e) {}
                            }
                        },
                        onLeave: function(retval) {
                            // 打印输出数据
                            if (retval && retval.readByteArray) {
                                try {
                                    var output = retval.readByteArray(Math.min(64, 32));
                                    console.log("   输出: " + hexdump(output, {ansi: true}));
                                } catch(e) {}
                            }
                        }
                    });
                }
            } catch(e) {}
        });
    });
}

// Hook MD5函数
function hookMD5Functions() {
    console.log("🔍 开始Hook MD5函数...");
    
    var md5_functions = [
        "MD5_Init",
        "MD5_Update", 
        "MD5_Final",
        "EVP_md5"
    ];
    
    var libraries = [
        "libmpaas_crypto.so",
        "libopenssl.so",
        "libqcOpenSSL.so"
    ];
    
    libraries.forEach(function(lib) {
        md5_functions.forEach(function(func) {
            try {
                var addr = Module.findExportByName(lib, func);
                if (addr) {
                    console.log("✅ 找到MD5函数: " + lib + "!" + func);
                    
                    Interceptor.attach(addr, {
                        onEnter: function(args) {
                            console.log("\n🔐 [" + func + "] 调用");
                            
                            if (func === "MD5_Update" && args[1]) {
                                try {
                                    var data = args[1].readByteArray(Math.min(64, args[2].toInt32()));
                                    console.log("   MD5输入: " + hexdump(data, {ansi: true}));
                                } catch(e) {}
                            }
                        },
                        onLeave: function(retval) {
                            if (func === "MD5_Final" && retval) {
                                try {
                                    var hash = retval.readByteArray(16);
                                    console.log("   MD5结果: " + hexdump(hash, {ansi: true}));
                                } catch(e) {}
                            }
                        }
                    });
                }
            } catch(e) {}
        });
    });
}

// Hook网络请求函数
function hookNetworkFunctions() {
    console.log("🔍 开始Hook网络函数...");
    
    // Hook HTTP请求
    try {
        var okhttp_classes = [
            "okhttp3.Request$Builder",
            "okhttp3.RequestBody", 
            "okhttp3.Call"
        ];
        
        Java.perform(function() {
            okhttp_classes.forEach(function(className) {
                try {
                    var clazz = Java.use(className);
                    console.log("✅ 找到OkHttp类: " + className);
                    
                    if (className === "okhttp3.RequestBody") {
                        clazz.create.overload('okhttp3.MediaType', 'java.lang.String').implementation = function(mediaType, content) {
                            console.log("\n🌐 [HTTP请求体] ");
                            console.log("   Content-Type: " + mediaType);
                            console.log("   内容: " + content);
                            
                            // 检查是否包含我们关心的加密数据
                            if (content.includes('"data":') || content.includes('"key":')) {
                                console.log("   🎯 发现加密请求!");
                                captured_data.push({
                                    type: "http_request",
                                    content: content,
                                    timestamp: new Date().toISOString()
                                });
                            }
                            
                            return this.create(mediaType, content);
                        };
                    }
                } catch(e) {
                    console.log("   跳过类: " + className + " (" + e + ")");
                }
            });
        });
    } catch(e) {
        console.log("   Java Hook失败: " + e);
    }
}

// Hook JSON解析函数
function hookJSONFunctions() {
    console.log("🔍 开始Hook JSON函数...");
    
    try {
        Java.perform(function() {
            // Hook JSONObject
            var JSONObject = Java.use("org.json.JSONObject");
            
            JSONObject.$init.overload('java.lang.String').implementation = function(json) {
                console.log("\n📋 [JSON解析] ");
                console.log("   JSON内容: " + json);
                
                // 检查是否是解密后的数据
                if (json.length < 1000) {  // 只打印较短的JSON
                    try {
                        var parsed = JSON.parse(json);
                        if (parsed.data || parsed.key || parsed.sign) {
                            console.log("   🎯 发现加密相关JSON!");
                        }
                    } catch(e) {}
                }
                
                return this.$init(json);
            };
        });
    } catch(e) {
        console.log("   JSON Hook失败: " + e);
    }
}

// 主函数
function main() {
    console.log("🚀 开始Hook翼支付加密函数...");
    
    // 等待应用加载
    setTimeout(function() {
        hookAESFunctions();
        hookBase64Functions(); 
        hookMD5Functions();
        hookNetworkFunctions();
        hookJSONFunctions();
        
        console.log("\n✅ 所有Hook已设置完成!");
        console.log("📱 请在应用中触发网络请求...");
        
        // 定期输出捕获的数据统计
        setInterval(function() {
            if (captured_keys.length > 0 || captured_data.length > 0) {
                console.log("\n📊 捕获统计:");
                console.log("   密钥数量: " + captured_keys.length);
                console.log("   数据数量: " + captured_data.length);
            }
        }, 30000);  // 每30秒输出一次
        
    }, 2000);
}

// 启动Hook
main();
