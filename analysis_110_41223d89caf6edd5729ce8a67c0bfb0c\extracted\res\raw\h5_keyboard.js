window.AlipayH5Keyboad||function(){var v,w=function(t){if(t&&t.nodeType){for(var e=t;e;){if("fixed"==window.getComputedStyle(e).position)return!0;if(e===document||e===document.documentElement||e===document.body)break;e=e.parentNode}return!1}},A='data-kb-placeholder';function u(t){'undefined'!=typeof AlipayJSBridge?t():document.addEventListener("AlipayJSBridgeReady",function(){t()})}var S=-1!=navigator.userAgent.search(/iPhone OS 10_(1|2)/gi);window.SHOULDH5CUSTOMKEYBOARDRUNSPVERSION&&(S=!1);var E=-1!==navigator.userAgent.search("Nebula WK");function t(){if(document.getElementById('newembedbase'))console.log('newembedbase exists');else{console.log('new newembedbase');var t=document.createElement('param');t.setAttribute('name','type');t.setAttribute('value','newembedbase');var e=document.createElement('param');e.setAttribute('name','zindex');e.setAttribute('value','9999');var a=document.createElement('object');a.setAttribute('type','application/view');a.setAttribute('id','newembedbase');a.setAttribute('style','z-index:-9999;position:absolute;left:0px;top:0px;width:100%;height:100%');a.appendChild(t);a.appendChild(e);document.body.appendChild(a)}document.addEventListener("focus",function(t){var e=t.target,a=e.getAttribute("data-keyboard"),n=e.type;v=null;if(e&&("input"==e.tagName.toLowerCase()||"textarea"==e.tagName.toLowerCase())&&a&&""!=a&&"text"==a){v=e;var r=!1;S&&!w(e)?r=!0:S||(r=!0);"password"==n&&(r=!1);if(r&&e._sourceBlur){e._sourceBlur();u(function(){var t=l.getInputParamWithElement(v,!1);if(t){var e=JSON.parse(t);AlipayJSBridge.call("inputBlurEvent",e)}})}}},!0);var n=null,r=250;-1!=navigator.userAgent.search(/iPhone\sOS\s(10|9|8)_(1|2)/gi)&&(r=300);window.addEventListener('resize',function(t){if(v){var e=v.getAttribute("data-keyboard");e&&""!=e&&"text"==e&&u(function(){clearTimeout(n);n=setTimeout(function(){var t=l.getInputParamWithElement(v,!0);if(t){var e=JSON.parse(t);AlipayJSBridge.call("resizeNativeKeyBoardInput",e)}},r)})}});var o=HTMLInputElement.prototype.blur;HTMLInputElement.prototype.blur=function(){var t=this.getAttribute('data-keyboard');t&&""!=t&&u(function(){AlipayJSBridge.call("hideCustomKeyBoard")});return o.apply(this,arguments)};HTMLInputElement.prototype._sourceBlur=o;var i=HTMLTextAreaElement.prototype.blur;HTMLTextAreaElement.prototype.blur=function(){var t=this.getAttribute('data-keyboard');t&&""!=t&&u(function(){AlipayJSBridge.call("hideCustomKeyBoard")});return i.apply(this,arguments)};HTMLTextAreaElement.prototype._sourceBlur=i}var C=1,l={_getOffset:function(t){var e=document.getElementsByClassName('h5numInput')[t];return this._getEleOffset(e)},getInputOffset:function(t){return JSON.stringify(this._getOffset(t))},_getEleOffset:function(t){var e=document.querySelector('meta[name=viewport]').getAttribute('content').match(/initial-scale=\s*([0-9.]+)/)[1],a=parseFloat(e),n=window.getComputedStyle(t).paddingLeft;'center'==window.getComputedStyle(t).textAlign&&(n=0);return{x:(parseFloat(n)+t.getBoundingClientRect().left)*a,y:(t.getBoundingClientRect().top+document.body.scrollTop)*a,yt:t.getBoundingClientRect().top*a,w:(t.offsetWidth-parseFloat(n))*a,h:t.offsetHeight*a}},getInputParam:function(t){var e=document.getElementsByClassName('h5numInput')[t];e.setAttribute('readonly','true');e.setAttribute('placeholder','');var a,n=e.getAttribute('h5kb-color'),r=window.getComputedStyle(e).color;if(n&&n.length)a=n;else{a=r;e.setAttribute('h5kb-color',r)}var o=parseFloat(window.getComputedStyle(e).fontSize);e.style.color=window.getComputedStyle(e).backgroundColor;var i=document.querySelector('meta[name=viewport]').getAttribute('content').match(/initial-scale=\s*([0-9.]+)/)[1],u=parseFloat(i);return{offset:this._getOffset(t),placeholder:this.getPlaceholder(t),type:e.getAttribute('type')||'text',kbType:e.getAttribute('kb-type')||e.getAttribute('data-kb-type')||0,value:e.value||'',color:a,fontSize:o*u,fontWeight:window.getComputedStyle(e).fontWeight,maxlength:parseInt(e.getAttribute('maxlength'))||-1,canPaste:!('false'==e.getAttribute('data-paste')),textAlign:window.getComputedStyle(e).textAlign}},dispatchEventWithElement:function(t,e,a,n,r){if(t){console.log("xxx dispatchEventWithElement eventName "+e);if("blur"==e){var o=new FocusEvent("blur");o.initEvent("blur",!1,!1);o.simulated=!0;t.dispatchEvent(o);if(n&&"r"==n){var i=function(t,e){if(t&&t.nodeType){for(var a=t;a;){if(a.tagName&&a.tagName.toLowerCase()==e)return a;if(a===document||a===document.documentElement||a===document.body)break;a=a.parentNode}return null}}(t,"form");i&&i.submit&&i.submit()}window._currentInput=null}else if("keydown"==e){(u=new KeyboardEvent("keydown",{keyCode:a||0})).data={keyCode:a||0};u.initEvent("keydown",!1,!1);t.dispatchEvent(u)}else if("keyup"==e){var u;(u=new KeyboardEvent("keyup",{keyCode:a||0})).data={keyCode:a||0};u.initEvent("keyup",!1,!1);t.dispatchEvent(u)}else if("input"==e){if(n&&""!=n||46==a){var l=new Event("input",{bubbles:!0,cancelable:!1});l.data=n;l.simulated=!0;t.dispatchEvent(l)}}else if("change"==e){var d=new Event("change",{bubbles:!0,cancelable:!1});t.dispatchEvent(d)}else if("complete"==e){var s=new Event("complete",{bubbles:!0,cancelable:!1});r&&(r=JSON.parse(r))&&r.value&&(s.data={value:r.value});t.dispatchEvent(s)}else if("linechange"==e){var c=new Event("linechange",{bubbles:!0,cancelable:!1});r&&(r=JSON.parse(r))&&r.lineCount&&r.height&&(c.data={lineCount:r.lineCount,height:r.height});t.dispatchEvent(c)}}},getTagName:function(t){var e;if(!(!(e=v||t)||e&&e.tagName&&"input"!=e.tagName.toLowerCase()&&"textarea"!=e.tagName.toLowerCase()))return JSON.stringify({tagName:e.tagName.toLowerCase()})},getInputParamWithElement:function(t,e){var a;if(!(!(a=v||t)||a&&a.tagName&&"input"!=a.tagName.toLowerCase()&&"textarea"!=a.tagName.toLowerCase()||w(a)&&S&&E)){var n=a.getAttribute('data-keyboard');if(!n){window._currentInput=null;return"input"==a.tagName.toLowerCase()||"textarea"==a.tagName.toLowerCase()?JSON.stringify({isNoNeedKeyBoardInput:"YES"}):void 0}if(a&&"password"==a.type)return JSON.stringify({isNoNeedKeyBoardInput:"YES"});"idcard"!=n&&"digit"!=n||setTimeout(function(){e||a.setAttribute("type","text")},0);var r=(window._currentInput=a).getAttribute('h5kb-color'),o=a.getAttribute('placeholder');if(o&&""!=o&&!e){a.setAttribute(A,o);a.placeholder=""}var i,u=window.getComputedStyle(a).color,l=a.style.color;if(r&&r.length)i=r;else{i=u;setTimeout(function(){if(!e){a.setAttribute('h5kb-color',u);if(l&&""!=l&&null!=l){a.setAttribute('isStyleColor',!0);a.setAttribute('isStyleColorVal',l)}}},0)}var d=parseFloat(window.getComputedStyle(a).fontSize),s=window.getComputedStyle(a).fontFamily;e||(a.style.color=window.getComputedStyle(a).backgroundColor);var c=a.getAttribute("data-kbel-id");if(!c){c=C++;setTimeout(function(){a.setAttribute("data-kbel-id",c)},0)}var p=document.querySelector('meta[name=viewport]').getAttribute('content').match(/initial-scale=\s*([0-9.]+)/)[1],g=parseFloat(p),m=parseFloat(window.getComputedStyle(a).borderWidth),b=(parseFloat(window.getComputedStyle(_currentInput).borderLeftWidth),parseFloat(window.getComputedStyle(_currentInput).borderRightWidth),parseFloat(window.getComputedStyle(_currentInput).borderTopWidth)),f=this._getEleOffset(a);m&&0<m&&(f.yt=f.yt+b||m);var y=o;y||(y=a.getAttribute(A)?a.getAttribute(A):"");var h={tagName:a.tagName.toLowerCase(),position:w(a)?"fixed":"static",offset:f,placeholder:y,type:a.getAttribute('type')||'text',keyboard:a.getAttribute('data-keyboard'),value:a.value||'',color:i,fontSize:d*g,fontFamily:s,fontWeight:window.getComputedStyle(a).fontWeight,maxlength:parseInt(a.getAttribute('maxlength'))||-1,canPaste:!('false'==a.getAttribute('data-paste')),textAlign:window.getComputedStyle(a).textAlign,selectionStart:parseFloat(a.getAttribute("data-selection-start")?a.getAttribute("data-selection-start"):"-1"),selectionEnd:parseFloat(a.getAttribute("data-selection-end")?a.getAttribute("data-selection-end"):"-1"),returnType:a.getAttribute("data-return-type")?a.getAttribute("data-return-type"):"",canReturn:a.getAttribute("data-return")?a.getAttribute("data-return"):"Y",cursor:parseFloat(a.getAttribute("data-cursor")?a.getAttribute("data-cursor"):"-1"),kbElId:parseInt(c),controlled:a.getAttribute("data-controlled")?a.getAttribute("data-controlled"):"N"};if('textarea'==h.tagName){h.autoHeight=!!a.getAttribute('data-auto-height')&&!('false'==a.getAttribute('data-auto-height'));h.cursorSpacing=parseFloat(a.getAttribute("data-cursor-spacing")?a.getAttribute("data-cursor-spacing"):"0")*g;h.position=w(a)&&'true'==a.getAttribute('data-fixed')?"fixed":"static";h.adjustPosition=!!a.getAttribute("data-adjust-position")&&!('false'==a.getAttribute("data-adjust-position"));h.showConfirmBar=!a.getAttribute("data-show-confirm-bar")||!('false'==a.getAttribute("data-show-confirm-bar"))}return JSON.stringify(h)}},setTextareaValue:function(t){window._currentInput.value=t.replace(/\\\n/,"\n")},restoreInputElement:function(t){console.log("restoreInputElement "+t);var e=document.querySelector("[data-kbel-id='"+t+"']");if(e){1==e.getAttribute('isStyleColor')?e.style.color=e.getAttribute('h5kb-color'):e.style.color=null;var a=e.getAttribute(A);if(a&&""!=a){e.removeAttribute(A);e.placeholder=a}e.removeAttribute('h5kb-color');e.removeAttribute('isStyleColor')}else console.error("::data-kbel-id::",t);v=null},getPlaceholder:function(t){return document.getElementsByClassName('h5numInput')[t].getAttribute('data-placeholder')||''},setInputValue:function(t,e){document.getElementsByClassName('h5numInput')[t].value=e},setInputValue4Android:function(t){window._currentInput&&(window._currentInput.value=t)},fixWKFixElementScrollBug:function(){document.addEventListener("touchmove",function(t){u(function(){w(window._currentInput)&&AlipayJSBridge.call("hideCustomKeyBoard")})},!0)},init:function(){for(var t=[],e=document.getElementsByClassName('h5numInput'),a=0;a<e.length;a++)t.push(this.getInputParam(a));0<t.length&&('undefined'!=typeof AlipayJSBridge?AlipayJSBridge.call('createInput',{param:t}):document.addEventListener("AlipayJSBridgeReady",function(){AlipayJSBridge.call('createInput',{param:t})}));document.addEventListener('getNewOffset',function(t,e){var a=this._getOffset(index);AlipayJSBridge.callback&&AlipayJSBridge.callback({clientId:e,offset:a})});if(window.SHOULDUESH5KEYBOARD){-1!==navigator.userAgent.search("Nebula WK")&&l.fixWKFixElementScrollBug()}},_getInputJsonWithElement:function(t){var e=l.getInputParamWithElement(t,!0);try{e&&(e=JSON.parse(e))}catch(t){console.error('json transform error:',t,e)}return e}};document.addEventListener("AlipayJSBridgeReady",function(){});/complete|loaded|interactive/.test(document.readyState)?setTimeout(function(){t()},1):document.addEventListener('DOMContentLoaded',function(){t()},!1);l.version='1.5.1';window.AlipayH5Keyboad=l}();
// do not modify
