#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SO库深度分析工具
分析翼支付中的加密相关SO库
"""

import os
import struct
import re
from pathlib import Path

class SOLibraryAnalyzer:
    def __init__(self, lib_path="lib/arm64-v8a"):
        self.lib_path = lib_path
        self.crypto_keywords = [
            b"AES", b"aes", b"DES", b"des", b"RSA", b"rsa",
            b"MD5", b"md5", b"SHA", b"sha", b"HMAC", b"hmac",
            b"encrypt", b"decrypt", b"cipher", b"crypto",
            b"SSL", b"ssl", b"TLS", b"tls", b"SM2", b"sm2",
            b"SM3", b"sm3", b"SM4", b"sm4", b"ECC", b"ecc",
            b"key", b"KEY", b"iv", b"IV", b"salt", b"SALT"
        ]
        
        print("🔍 SO库深度分析工具")
        print("=" * 50)
    
    def analyze_all_libraries(self):
        """分析所有SO库"""
        if not os.path.exists(self.lib_path):
            print(f"❌ 路径不存在: {self.lib_path}")
            return
        
        so_files = list(Path(self.lib_path).glob("*.so"))
        print(f"📚 找到 {len(so_files)} 个SO库文件")
        
        crypto_libs = []
        
        for so_file in so_files:
            print(f"\n🔍 分析: {so_file.name}")
            analysis = self.analyze_single_library(so_file)
            
            if analysis['crypto_score'] > 0:
                crypto_libs.append((so_file.name, analysis))
                print(f"   🔐 加密相关度: {analysis['crypto_score']}/10")
        
        # 按加密相关度排序
        crypto_libs.sort(key=lambda x: x[1]['crypto_score'], reverse=True)
        
        print(f"\n🎯 加密相关库排名:")
        for i, (lib_name, analysis) in enumerate(crypto_libs[:10]):
            print(f"   {i+1}. {lib_name} (评分: {analysis['crypto_score']}/10)")
        
        return crypto_libs
    
    def analyze_single_library(self, so_file):
        """分析单个SO库"""
        analysis = {
            'size': 0,
            'crypto_score': 0,
            'crypto_strings': [],
            'exported_functions': [],
            'crypto_functions': [],
            'has_elf_header': False,
            'architecture': 'unknown'
        }
        
        try:
            with open(so_file, 'rb') as f:
                data = f.read()
                analysis['size'] = len(data)
                
                # 检查ELF头
                if data.startswith(b'\x7fELF'):
                    analysis['has_elf_header'] = True
                    analysis['architecture'] = self.get_elf_architecture(data)
                
                # 搜索加密相关字符串
                analysis['crypto_strings'] = self.find_crypto_strings(data)
                analysis['crypto_score'] = len(analysis['crypto_strings'])
                
                # 尝试提取导出函数
                analysis['exported_functions'] = self.extract_exported_functions(data)
                analysis['crypto_functions'] = self.filter_crypto_functions(analysis['exported_functions'])
                
                # 根据加密函数数量调整评分
                analysis['crypto_score'] += len(analysis['crypto_functions']) * 2
                
                # 特殊库的额外评分
                if 'crypto' in so_file.name.lower():
                    analysis['crypto_score'] += 5
                if 'ssl' in so_file.name.lower():
                    analysis['crypto_score'] += 5
                if 'sm' in so_file.name.lower():
                    analysis['crypto_score'] += 3
                
        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
        
        return analysis
    
    def get_elf_architecture(self, data):
        """获取ELF架构信息"""
        try:
            # ELF头结构
            if len(data) < 20:
                return "unknown"
            
            ei_class = data[4]  # 32位或64位
            ei_data = data[5]   # 字节序
            e_machine = struct.unpack('<H' if ei_data == 1 else '>H', data[18:20])[0]
            
            arch_map = {
                0x3E: "x86_64",
                0xB7: "AArch64", 
                0x28: "ARM",
                0x3: "x86"
            }
            
            arch = arch_map.get(e_machine, f"unknown({e_machine})")
            bits = "64-bit" if ei_class == 2 else "32-bit"
            
            return f"{arch} {bits}"
        except:
            return "unknown"
    
    def find_crypto_strings(self, data):
        """查找加密相关字符串"""
        found_strings = []
        
        for keyword in self.crypto_keywords:
            positions = []
            start = 0
            while True:
                pos = data.find(keyword, start)
                if pos == -1:
                    break
                positions.append(pos)
                start = pos + 1
            
            if positions:
                found_strings.append({
                    'keyword': keyword.decode('utf-8', errors='ignore'),
                    'count': len(positions),
                    'positions': positions[:5]  # 只保留前5个位置
                })
        
        return found_strings
    
    def extract_exported_functions(self, data):
        """提取导出函数名"""
        functions = []
        
        try:
            # 简单的字符串提取，查找可能的函数名
            # 这是一个简化的实现，真正的ELF解析会更复杂
            
            # 查找以常见函数前缀开头的字符串
            function_patterns = [
                rb'[A-Za-z_][A-Za-z0-9_]{3,30}\x00',  # 一般函数名模式
            ]
            
            for pattern in function_patterns:
                matches = re.finditer(pattern, data)
                for match in matches:
                    func_name = match.group().rstrip(b'\x00').decode('utf-8', errors='ignore')
                    if self.is_likely_function_name(func_name):
                        functions.append(func_name)
            
            # 去重并排序
            functions = sorted(list(set(functions)))
            
        except Exception as e:
            pass
        
        return functions[:100]  # 限制数量
    
    def is_likely_function_name(self, name):
        """判断是否可能是函数名"""
        if len(name) < 3 or len(name) > 50:
            return False
        
        # 检查是否包含常见的函数名特征
        function_indicators = [
            'init', 'create', 'destroy', 'get', 'set', 'encrypt', 'decrypt',
            'encode', 'decode', 'hash', 'sign', 'verify', 'generate', 'derive'
        ]
        
        name_lower = name.lower()
        for indicator in function_indicators:
            if indicator in name_lower:
                return True
        
        # 检查是否是合理的标识符
        if name.replace('_', '').replace('$', '').isalnum():
            return True
        
        return False
    
    def filter_crypto_functions(self, functions):
        """过滤出加密相关函数"""
        crypto_functions = []
        
        crypto_patterns = [
            'aes', 'des', 'rsa', 'ecc', 'md5', 'sha', 'hmac',
            'encrypt', 'decrypt', 'cipher', 'crypto', 'ssl', 'tls',
            'sm2', 'sm3', 'sm4', 'key', 'hash', 'sign', 'verify'
        ]
        
        for func in functions:
            func_lower = func.lower()
            for pattern in crypto_patterns:
                if pattern in func_lower:
                    crypto_functions.append(func)
                    break
        
        return crypto_functions
    
    def analyze_key_libraries(self):
        """重点分析关键加密库"""
        key_libraries = [
            "libmpaas_crypto.so",
            "libantssm.so", 
            "libTencentSM.so",
            "libopenssl.so",
            "libqcOpenSSL.so",
            "libCryptoOperAd.so",
            "libdatabase_sqlcrypto.so"
        ]
        
        print(f"\n🎯 重点分析关键加密库:")
        
        for lib_name in key_libraries:
            lib_path = Path(self.lib_path) / lib_name
            if lib_path.exists():
                print(f"\n📚 {lib_name}:")
                analysis = self.analyze_single_library(lib_path)
                
                print(f"   大小: {analysis['size'] / 1024:.1f} KB")
                print(f"   架构: {analysis['architecture']}")
                print(f"   加密评分: {analysis['crypto_score']}/10")
                
                if analysis['crypto_strings']:
                    print(f"   加密关键词:")
                    for item in analysis['crypto_strings'][:5]:
                        print(f"     - {item['keyword']}: {item['count']}次")
                
                if analysis['crypto_functions']:
                    print(f"   加密函数 (前10个):")
                    for func in analysis['crypto_functions'][:10]:
                        print(f"     - {func}")
                
                # 特殊分析
                self.special_analysis(lib_name, lib_path)
            else:
                print(f"   ❌ {lib_name} 不存在")
    
    def special_analysis(self, lib_name, lib_path):
        """对特定库进行特殊分析"""
        try:
            with open(lib_path, 'rb') as f:
                data = f.read()
            
            if lib_name == "libmpaas_crypto.so":
                print(f"   🔍 mPaaS加密库特殊分析:")
                # 查找mPaaS特有的字符串
                mpaas_patterns = [b"mpaas", b"MPAAS", b"alipay", b"ALIPAY"]
                for pattern in mpaas_patterns:
                    count = data.count(pattern)
                    if count > 0:
                        print(f"     - 找到'{pattern.decode()}': {count}次")
            
            elif lib_name == "libantssm.so":
                print(f"   🔍 蚂蚁国密库特殊分析:")
                # 查找国密相关字符串
                sm_patterns = [b"SM2", b"SM3", b"SM4", b"sm2", b"sm3", b"sm4"]
                for pattern in sm_patterns:
                    count = data.count(pattern)
                    if count > 0:
                        print(f"     - 找到'{pattern.decode()}': {count}次")
            
            elif "openssl" in lib_name.lower():
                print(f"   🔍 OpenSSL库特殊分析:")
                # 查找OpenSSL版本信息
                version_pattern = rb"OpenSSL [0-9]+\.[0-9]+\.[0-9]+[a-z]?"
                matches = re.findall(version_pattern, data)
                if matches:
                    print(f"     - OpenSSL版本: {matches[0].decode()}")
                
                # 查找支持的算法
                algorithms = [b"AES-256-CBC", b"AES-128-CBC", b"RSA", b"ECDSA"]
                for algo in algorithms:
                    if algo in data:
                        print(f"     - 支持算法: {algo.decode()}")
        
        except Exception as e:
            print(f"   ❌ 特殊分析失败: {e}")
    
    def generate_analysis_report(self):
        """生成分析报告"""
        print(f"\n📊 生成详细分析报告...")
        
        crypto_libs = self.analyze_all_libraries()
        self.analyze_key_libraries()
        
        print(f"\n" + "=" * 50)
        print(f"📋 分析总结:")
        print(f"   总SO库数量: {len(list(Path(self.lib_path).glob('*.so')))}")
        print(f"   加密相关库: {len(crypto_libs)}")
        
        if crypto_libs:
            top_lib = crypto_libs[0]
            print(f"   最重要的加密库: {top_lib[0]} (评分: {top_lib[1]['crypto_score']})")
        
        print(f"\n💡 逆向建议:")
        print(f"   1. 重点分析 libmpaas_crypto.so 和 libantssm.so")
        print(f"   2. 使用IDA Pro或Ghidra进行静态分析")
        print(f"   3. 使用Frida进行动态Hook")
        print(f"   4. 关注AES加密和SM2国密算法的实现")
        print(f"=" * 50)

def main():
    analyzer = SOLibraryAnalyzer()
    analyzer.generate_analysis_report()

if __name__ == "__main__":
    main()
