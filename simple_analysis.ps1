# Simple Android Project Analysis Script

Write-Host "Starting Android Project Analysis..." -ForegroundColor Green
Write-Host "============================================================"

# Basic Project Information
Write-Host "`nApp Basic Information:" -ForegroundColor Cyan

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    if ($manifest -match 'package="([^"]+)"') {
        Write-Host "   Package Name: $($matches[1])"
    }
    
    if ($manifest -match 'android:versionName="([^"]+)"') {
        Write-Host "   Version Name: $($matches[1])"
    }
    
    if ($manifest -match 'android:versionCode="([^"]+)"') {
        Write-Host "   Version Code: $($matches[1])"
    }
    
    # Count components
    $activities = [regex]::Matches($manifest, '<activity[^>]*').Count
    $services = [regex]::Matches($manifest, '<service[^>]*').Count
    
    Write-Host "   Activities: $activities"
    Write-Host "   Services: $services"
}

# File Structure Analysis
Write-Host "`nFile Structure Analysis:" -ForegroundColor Cyan

$allFiles = Get-ChildItem -Recurse -File -ErrorAction SilentlyContinue
$totalFiles = $allFiles.Count
$totalSize = ($allFiles | Measure-Object -Property Length -Sum).Sum
$totalSizeMB = [math]::Round($totalSize / 1MB, 2)

Write-Host "   Total Files: $totalFiles"
Write-Host "   Total Size: $totalSizeMB MB"

# File type statistics
$fileTypes = $allFiles | Group-Object Extension | Sort-Object Count -Descending | Select-Object -First 10
Write-Host "   Main File Types:"
foreach ($type in $fileTypes) {
    $ext = if ($type.Name) { $type.Name } else { "No Extension" }
    Write-Host "     $ext : $($type.Count)"
}

# Dependencies Analysis
Write-Host "`nDependencies Analysis:" -ForegroundColor Cyan

# Native libraries
if (Test-Path "lib\arm64-v8a") {
    $nativeLibs = Get-ChildItem "lib\arm64-v8a" -Filter "*.so" -ErrorAction SilentlyContinue
    Write-Host "   Native Libraries: $($nativeLibs.Count)"
}

# Framework dependencies
if (Test-Path "META-INF") {
    $versionFiles = Get-ChildItem "META-INF" -Filter "*.version" -ErrorAction SilentlyContinue
    Write-Host "   Framework Dependencies: $($versionFiles.Count)"
}

# Third-party SDKs
Write-Host "`nThird-party SDKs:" -ForegroundColor Cyan

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    $sdks = @()
    
    if ($manifest -match "huawei|hms") { $sdks += "Huawei HMS Core" }
    if ($manifest -match "alipay") { $sdks += "Alipay SDK" }
    if ($manifest -match "tencent") { $sdks += "Tencent SDK" }
    if ($manifest -match "bytedance") { $sdks += "ByteDance SDK" }
    if ($manifest -match "mpaas") { $sdks += "Ant mPaaS" }
    if ($manifest -match "agora") { $sdks += "Agora SDK" }
    
    foreach ($sdk in $sdks) {
        Write-Host "   - $sdk"
    }
}

# Features Analysis
Write-Host "`nFeatures Analysis:" -ForegroundColor Cyan

if (Test-Path "AndroidManifest.xml") {
    $manifest = Get-Content "AndroidManifest.xml" -Raw
    
    $features = @()
    
    if ($manifest -match "CAMERA") { $features += "Camera" }
    if ($manifest -match "RECORD_AUDIO") { $features += "Audio Recording" }
    if ($manifest -match "LOCATION") { $features += "Location Services" }
    if ($manifest -match "NFC") { $features += "NFC Payment" }
    if ($manifest -match "FINGERPRINT|BIOMETRIC") { $features += "Biometric Authentication" }
    if ($manifest -match "BLUETOOTH") { $features += "Bluetooth" }
    
    foreach ($feature in $features) {
        Write-Host "   - $feature"
    }
}

# Assets Analysis
if (Test-Path "assets") {
    $assetFiles = Get-ChildItem "assets" -Recurse -File -ErrorAction SilentlyContinue
    
    $assetFeatures = @()
    
    if ($assetFiles | Where-Object { $_.Name -match "face|liveness" }) {
        $assetFeatures += "Face Recognition"
    }
    if ($assetFiles | Where-Object { $_.Name -match "ocr|idcard" }) {
        $assetFeatures += "OCR Recognition"
    }
    if ($assetFiles | Where-Object { $_.Name -match "bankcard" }) {
        $assetFeatures += "Bank Card Recognition"
    }
    
    foreach ($feature in $assetFeatures) {
        Write-Host "   - $feature"
    }
}

# Architecture Analysis
Write-Host "`nArchitecture Analysis:" -ForegroundColor Cyan

if (Test-Path "assets\flutter_assets") {
    Write-Host "   - Flutter Hybrid Development"
}

if (Test-Path "assets\h5_bridge.js") {
    Write-Host "   - H5 Bridge Technology"
}

if (Test-Path "assets\tiny") {
    Write-Host "   - Mini Program Technology"
}

if (Test-Path "assets\mpaas.properties") {
    Write-Host "   - Ant mPaaS Mobile Platform"
}

# Resource Analysis
Write-Host "`nResource Analysis:" -ForegroundColor Cyan

if (Test-Path "res") {
    $resFiles = Get-ChildItem "res" -Recurse -File -ErrorAction SilentlyContinue
    $drawables = $resFiles | Where-Object { $_.Directory.Name -match "drawable" }
    $layouts = $resFiles | Where-Object { $_.Directory.Name -match "layout" }
    
    Write-Host "   Image Resources: $($drawables.Count)"
    Write-Host "   Layout Files: $($layouts.Count)"
    
    # Multi-language support
    $langDirs = Get-ChildItem "res" -Directory -ErrorAction SilentlyContinue | Where-Object { $_.Name -match "values-[a-z]{2}" }
    if ($langDirs.Count -gt 0) {
        Write-Host "   Multi-language Support: $($langDirs.Count) languages"
    }
}

Write-Host "`n============================================================"
Write-Host "Analysis Complete!" -ForegroundColor Green
Write-Host "============================================================"
