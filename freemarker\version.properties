# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

# Version info for the builds.

# Version number
# --------------
#
# The version number format is (since FreeMarker 2.3.22):
#
#   Version ::= major '.' minor '.' micro ('-' Qualifier)?
#   Qualifier :: = NightlyQualifier
#                  |
#                  ( ('pre'|'rc') twoDigitPositiveInteger ('-' NightlyQualifier)? )
#   NightlyQualifier :: = 'nightly_20190809T222208Z'
#
# This format is compatible both with Maven and JSR 277, and it must
# remain so. Stable versions must not have a qualifier.
# Note that qualifiers are compared with String.compareTo,
# thus "nightly" < "pre" < "rc", etc.
#
# Examples:
#   Version number        Means
#   3.0.0                 3.0.0 stable release
#   3.3.12                3.3.12 stable release
#   3.3.13-nightly_20190809T222208Z
#                         Modified version after 3.3.12, which will
#                         become to 3.3.13 one day.
#   3.4.0-pre03           The 3rd preview of version 3.4.0
#   3.4.0-pre04-nightly_20190809T222208Z
#                         Unreleased nightly version of the yet unfinished
#                         3.4.0-pre04.
#   3.4.0-rc01            1st release candidate of 3.4.0
#
# Backward-compatibility policy (since FreeMarker 2.3.20):
# - When only the micro version number is increased, full backward
#   compatibility is expected (ignoring extreme cases where the user
#   code or template breaks if an exception is *not* thrown anymore
#   as the FreeMarker bug causing it was fixed).
# - When the minor version number is increased, some minor backward
#   compatibility violations are allowed. Most dependent code should
#   continue working without modification or recompilation.
# - When the major version number is increased, major backward
#   compatibility violations are allowed, but still should be avoided.
version=2.3.29
# This exists as for Maven we use "-SNAPSHOT" for nightly releases,
# and no _nightly_20190809T222208Z. For final releases it's the
# same as "version".
mavenVersion=2.3.29

# Version string that conforms to OSGi
# ------------------------------------
#
# This is different from the plain version string:
# - "." is used instead of a "-" before the qualifier.
# - The stable releases must use "stable" qualifier.
# - Examples:
#   2.4.0.stable
#   2.4.0.rc01
#   2.4.0.pre01
#   2.4.0.nightly_20190809T222208Z
versionForOSGi=2.3.29.stable

# Version string that conforms to legacy MF
# -----------------------------------------
#
# Examples:
# version        -> versionForMf
# 2.2.5          -> 2.2.5
# 2.3.27.nightly -> 2.3.26.97
# 2.3.0          -> 2.3.0
# 2.3.0.pre13    -> 2.2.98.13
# 2.3.0.pre13-nightly -> 2.2.98.13.97
# 2.3.0.rc1      -> 2.2.99.1
# 2.3.0.nightly -> 2.2.97
# 3.0.0.pre2     -> 2.98.2
#
# "97 denotes "nightly", 98 denotes "pre", 99 denotes "rc" build.
# In general, for the nightly/preview/rc Y of version 2.X, the versionForMf is
# 2.X-1.(99|98).Y. Note the X-1.
versionForMf=2.3.29

# The date of the build.
# This should be automatically filled by the building tool (Ant).
buildTimestamp=2019-08-09T22:22:08Z

isGAECompliant=false
