// 翼支付反调试绕过脚本
// 绕过ptrace、字符串检测、文件检测等反调试机制

console.log("🛡️ 启动反调试绕过脚本...");
console.log("🎯 目标: 绕过翼支付的反调试保护");

// 全局绕过状态
var bypassStatus = {
    ptrace: false,
    strstr: false,
    openat: false,
    fopen: false,
    access: false,
    stat: false,
    readlink: false,
    gettimeofday: false
};

// 1. 绕过ptrace反调试
function bypassPtrace() {
    console.log("\n🔧 [反调试绕过] 绕过ptrace检测...");
    
    try {
        // Hook ptrace函数
        var ptracePtr = Module.findExportByName(null, "ptrace");
        if (ptracePtr) {
            Interceptor.replace(ptracePtr, new NativeCallback(function(request, pid, addr, data) {
                console.log("🎯 [ptrace] 拦截ptrace调用: request=" + request + ", pid=" + pid);
                
                // PTRACE_TRACEME = 0, 返回失败阻止反调试
                if (request == 0) {
                    console.log("   ✅ 阻止PTRACE_TRACEME调用");
                    return -1;
                }
                
                // 其他ptrace调用也返回失败
                return -1;
            }, "int", ["int", "int", "pointer", "pointer"]));
            
            bypassStatus.ptrace = true;
            console.log("   ✅ ptrace绕过成功");
        } else {
            console.log("   ⚠️ 未找到ptrace函数");
        }
    } catch(e) {
        console.log("   ❌ ptrace绕过失败: " + e);
    }
}

// 2. 绕过字符串检测
function bypassStringDetection() {
    console.log("\n🔧 [反调试绕过] 绕过字符串检测...");
    
    try {
        // Hook strstr函数
        var strstrPtr = Module.findExportByName(null, "strstr");
        if (strstrPtr) {
            Interceptor.replace(strstrPtr, new NativeCallback(function(haystack, needle) {
                var haystackStr = haystack.readCString();
                var needleStr = needle.readCString();
                
                // 检查是否在搜索Frida相关字符串
                var fridaKeywords = [
                    "frida", "FRIDA", "Frida",
                    "gum-js-loop", "gmain",
                    "linjector", "gdbus",
                    "frida-agent", "frida-server",
                    "re.frida.server"
                ];
                
                for (var i = 0; i < fridaKeywords.length; i++) {
                    if (needleStr && needleStr.indexOf(fridaKeywords[i]) !== -1) {
                        console.log("🎯 [strstr] 拦截Frida检测: " + needleStr);
                        return ptr(0); // 返回NULL，表示未找到
                    }
                }
                
                // 正常调用原函数
                return this.strstr(haystack, needle);
            }.bind({strstr: new NativeFunction(strstrPtr, "pointer", ["pointer", "pointer"])}), "pointer", ["pointer", "pointer"]));
            
            bypassStatus.strstr = true;
            console.log("   ✅ strstr绕过成功");
        } else {
            console.log("   ⚠️ 未找到strstr函数");
        }
        
        // Hook strcmp函数
        var strcmpPtr = Module.findExportByName(null, "strcmp");
        if (strcmpPtr) {
            Interceptor.replace(strcmpPtr, new NativeCallback(function(str1, str2) {
                var str1Val = str1.readCString();
                var str2Val = str2.readCString();
                
                // 检查Frida相关比较
                var fridaKeywords = ["frida", "FRIDA", "Frida", "gum", "linjector"];
                
                for (var i = 0; i < fridaKeywords.length; i++) {
                    if ((str1Val && str1Val.indexOf(fridaKeywords[i]) !== -1) ||
                        (str2Val && str2Val.indexOf(fridaKeywords[i]) !== -1)) {
                        console.log("🎯 [strcmp] 拦截Frida检测: " + str1Val + " vs " + str2Val);
                        return 1; // 返回不相等
                    }
                }
                
                // 正常调用
                return this.strcmp(str1, str2);
            }.bind({strcmp: new NativeFunction(strcmpPtr, "int", ["pointer", "pointer"])}), "int", ["pointer", "pointer"]));
            
            console.log("   ✅ strcmp绕过成功");
        }
        
    } catch(e) {
        console.log("   ❌ 字符串检测绕过失败: " + e);
    }
}

// 3. 绕过文件系统检测
function bypassFileSystemDetection() {
    console.log("\n🔧 [反调试绕过] 绕过文件系统检测...");
    
    try {
        // Hook openat函数
        var openatPtr = Module.findExportByName(null, "openat");
        if (openatPtr) {
            Interceptor.replace(openatPtr, new NativeCallback(function(dirfd, pathname, flags, mode) {
                var pathnameStr = pathname.readCString();
                
                // 检查是否在访问Frida相关文件
                var fridaPaths = [
                    "/proc/self/maps",
                    "/proc/self/task",
                    "/proc/self/status",
                    "/data/local/tmp/frida-server",
                    "/data/local/tmp/re.frida.server"
                ];
                
                for (var i = 0; i < fridaPaths.length; i++) {
                    if (pathnameStr && pathnameStr.indexOf(fridaPaths[i]) !== -1) {
                        console.log("🎯 [openat] 拦截文件访问: " + pathnameStr);
                        return -1; // 返回失败
                    }
                }
                
                // 正常调用
                return this.openat(dirfd, pathname, flags, mode);
            }.bind({openat: new NativeFunction(openatPtr, "int", ["int", "pointer", "int", "int"])}), "int", ["int", "pointer", "int", "int"]));
            
            bypassStatus.openat = true;
            console.log("   ✅ openat绕过成功");
        }
        
        // Hook fopen函数
        var fopenPtr = Module.findExportByName(null, "fopen");
        if (fopenPtr) {
            Interceptor.replace(fopenPtr, new NativeCallback(function(filename, mode) {
                var filenameStr = filename.readCString();
                
                // 检查敏感文件访问
                var sensitiveFiles = [
                    "/proc/self/maps",
                    "/proc/self/status",
                    "/proc/self/task"
                ];
                
                for (var i = 0; i < sensitiveFiles.length; i++) {
                    if (filenameStr && filenameStr.indexOf(sensitiveFiles[i]) !== -1) {
                        console.log("🎯 [fopen] 拦截文件打开: " + filenameStr);
                        return ptr(0); // 返回NULL
                    }
                }
                
                return this.fopen(filename, mode);
            }.bind({fopen: new NativeFunction(fopenPtr, "pointer", ["pointer", "pointer"])}), "pointer", ["pointer", "pointer"]));
            
            bypassStatus.fopen = true;
            console.log("   ✅ fopen绕过成功");
        }
        
        // Hook access函数
        var accessPtr = Module.findExportByName(null, "access");
        if (accessPtr) {
            Interceptor.replace(accessPtr, new NativeCallback(function(pathname, mode) {
                var pathnameStr = pathname.readCString();
                
                if (pathnameStr && (pathnameStr.indexOf("frida") !== -1 || 
                                   pathnameStr.indexOf("/data/local/tmp/") !== -1)) {
                    console.log("🎯 [access] 拦截文件检查: " + pathnameStr);
                    return -1; // 文件不存在
                }
                
                return this.access(pathname, mode);
            }.bind({access: new NativeFunction(accessPtr, "int", ["pointer", "int"])}), "int", ["pointer", "int"]));
            
            bypassStatus.access = true;
            console.log("   ✅ access绕过成功");
        }
        
    } catch(e) {
        console.log("   ❌ 文件系统检测绕过失败: " + e);
    }
}

// 4. 绕过进程检测
function bypassProcessDetection() {
    console.log("\n🔧 [反调试绕过] 绕过进程检测...");
    
    try {
        // Hook readlink函数 (检查/proc/self/exe)
        var readlinkPtr = Module.findExportByName(null, "readlink");
        if (readlinkPtr) {
            Interceptor.replace(readlinkPtr, new NativeCallback(function(pathname, buf, bufsiz) {
                var pathnameStr = pathname.readCString();
                
                if (pathnameStr && pathnameStr.indexOf("/proc/self/exe") !== -1) {
                    console.log("🎯 [readlink] 拦截进程路径检查");
                    // 返回一个正常的应用路径
                    var fakePath = "/system/bin/app_process64";
                    buf.writeUtf8String(fakePath);
                    return fakePath.length;
                }
                
                return this.readlink(pathname, buf, bufsiz);
            }.bind({readlink: new NativeFunction(readlinkPtr, "int", ["pointer", "pointer", "int"])}), "int", ["pointer", "pointer", "int"]));
            
            bypassStatus.readlink = true;
            console.log("   ✅ readlink绕过成功");
        }
        
    } catch(e) {
        console.log("   ❌ 进程检测绕过失败: " + e);
    }
}

// 5. 最小化测试 - Hook无关函数验证基础功能
function minimumTest() {
    console.log("\n🧪 [最小化测试] Hook无关函数验证基础功能...");
    
    try {
        // Hook gettimeofday函数作为测试
        var gettimeofdayPtr = Module.findExportByName(null, "gettimeofday");
        if (gettimeofdayPtr) {
            Interceptor.attach(gettimeofdayPtr, {
                onEnter: function(args) {
                    // 不做任何修改，只是验证Hook功能
                },
                onLeave: function(retval) {
                    // 静默记录
                }
            });
            
            bypassStatus.gettimeofday = true;
            console.log("   ✅ gettimeofday Hook成功 - 基础功能正常");
        }
        
        // 测试简单的Java Hook
        Java.perform(function() {
            try {
                var System = Java.use("java.lang.System");
                console.log("   ✅ Java环境可用 - 基础功能正常");
            } catch(e) {
                console.log("   ❌ Java环境不可用: " + e);
            }
        });
        
    } catch(e) {
        console.log("   ❌ 最小化测试失败: " + e);
    }
}

// 6. 绕过调试器检测
function bypassDebuggerDetection() {
    console.log("\n🔧 [反调试绕过] 绕过调试器检测...");
    
    try {
        // Hook kill函数 (检测调试器进程)
        var killPtr = Module.findExportByName(null, "kill");
        if (killPtr) {
            Interceptor.replace(killPtr, new NativeCallback(function(pid, sig) {
                console.log("🎯 [kill] 拦截kill调用: pid=" + pid + ", sig=" + sig);
                
                // 如果是发送信号给自己，可能是反调试检测
                if (pid == Process.id || pid == 0) {
                    console.log("   ✅ 阻止自杀信号");
                    return 0; // 假装成功
                }
                
                return this.kill(pid, sig);
            }.bind({kill: new NativeFunction(killPtr, "int", ["int", "int"])}), "int", ["int", "int"]));
            
            console.log("   ✅ kill绕过成功");
        }
        
    } catch(e) {
        console.log("   ❌ 调试器检测绕过失败: " + e);
    }
}

// 7. 输出绕过状态
function printBypassStatus() {
    console.log("\n📊 [绕过状态] 反调试绕过状态报告:");
    console.log("=" * 50);
    
    var successCount = 0;
    var totalCount = 0;
    
    for (var key in bypassStatus) {
        totalCount++;
        if (bypassStatus[key]) {
            successCount++;
            console.log("   ✅ " + key + ": 绕过成功");
        } else {
            console.log("   ❌ " + key + ": 绕过失败");
        }
    }
    
    console.log("\n📈 绕过成功率: " + successCount + "/" + totalCount + " (" + 
                Math.round(successCount / totalCount * 100) + "%)");
    
    if (successCount >= totalCount * 0.7) {
        console.log("🎉 绕过效果良好，可以尝试正常Hook");
    } else {
        console.log("⚠️ 绕过效果一般，可能仍有检测机制");
    }
}

// 8. 延迟执行主要Hook
function delayedMainHook() {
    console.log("\n⏰ [延迟Hook] 3秒后开始主要Hook功能...");
    
    setTimeout(function() {
        console.log("🚀 [主要Hook] 开始执行主要Hook功能...");
        
        Java.perform(function() {
            try {
                // Hook Base64编码
                var Base64 = Java.use("android.util.Base64");
                var originalEncode = Base64.encodeToString.overload('[B', 'int');
                
                originalEncode.implementation = function(input, flags) {
                    var result = originalEncode.call(this, input, flags);
                    
                    if (input.length > 32) {
                        console.log("\n📝 [Base64编码] " + new Date().toLocaleTimeString());
                        console.log("   长度: " + input.length + " -> " + result.length);
                        console.log("   数据: " + result.substring(0, 80) + "...");
                        
                        if (result.length > 200) {
                            console.log("   🎯 可能的加密数据!");
                        }
                    }
                    
                    return result;
                };
                
                console.log("✅ [主要Hook] Base64 Hook成功");
                
                // Hook网络请求
                try {
                    var RequestBody = Java.use("okhttp3.RequestBody");
                    var originalCreate = RequestBody.create.overload('okhttp3.MediaType', 'java.lang.String');
                    
                    originalCreate.implementation = function(mediaType, content) {
                        if (content.includes('"data":') || content.includes('"key":') || content.includes('"sign":')) {
                            console.log("\n🌐 [网络请求] " + new Date().toLocaleTimeString());
                            console.log("🎯 发现加密请求!");
                            console.log("Content-Type: " + mediaType);
                            console.log("请求内容: " + content);
                        }
                        
                        return originalCreate.call(this, mediaType, content);
                    };
                    
                    console.log("✅ [主要Hook] 网络请求Hook成功");
                } catch(e) {
                    console.log("⚠️ [主要Hook] 网络请求Hook失败: " + e);
                }
                
            } catch(e) {
                console.log("❌ [主要Hook] 主要Hook失败: " + e);
            }
        });
        
    }, 3000);
}

// 主执行流程
console.log("🎯 开始执行反调试绕过...");

// 立即执行绕过
bypassPtrace();
bypassStringDetection();
bypassFileSystemDetection();
bypassProcessDetection();
bypassDebuggerDetection();

// 最小化测试
minimumTest();

// 输出状态
printBypassStatus();

// 延迟执行主要Hook
delayedMainHook();

console.log("\n🎉 反调试绕过脚本加载完成!");
console.log("💡 请在手机上操作翼支付应用...");
console.log("📱 进入权益商城并刷新页面");

// 定期输出心跳
setInterval(function() {
    console.log("💓 [心跳] 反调试绕过脚本运行中... " + new Date().toLocaleTimeString());
}, 30000);
