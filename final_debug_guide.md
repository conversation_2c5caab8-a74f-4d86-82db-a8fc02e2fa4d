# 翼支付完整调试指南

## 🎯 当前状态总结

### ✅ 已完成的配置
- Python 3.12.4 环境 ✅
- Frida 17.0.7 安装 ✅
- ADB工具配置 ✅
- 设备连接 (94a5e1fa) ✅
- Frida Server运行 (进程ID: 21669) ✅
- APK完整分析 ✅
- 加密配置提取 ✅

### 🔍 关键发现
- **mPaaS公钥**: SM2椭圆曲线加密
- **加密类型**: C005 (翼支付自定义)
- **API端点**: `/gapi/equitymall/client/Activity/queryActivityInfo`
- **关键SO库**: libmpaas_crypto.so, libantssm.so

### ⚠️ 遇到的挑战
- 翼支付有强反调试保护
- Frida Hook被检测并导致应用终止
- 需要使用替代方案

## 🚀 推荐的最终解决方案

### 方案1: Charles代理抓包 (成功率最高 ⭐⭐⭐⭐⭐)

#### 步骤1: 下载和安装Charles
```bash
# 1. 访问 https://www.charlesproxy.com/download/
# 2. 下载Windows版本并安装
# 3. 启动Charles
```

#### 步骤2: 配置Charles
```bash
# 1. 菜单: Proxy → Proxy Settings
# 2. 确认端口为8080
# 3. 菜单: Proxy → SSL Proxying Settings
# 4. 勾选"Enable SSL Proxying"
# 5. 添加域名: *.bestpay.com.cn:443
```

#### 步骤3: 配置手机代理
```bash
# 1. 获取电脑IP: ipconfig
# 2. 手机WiFi设置 → 高级 → 代理 → 手动
# 3. 服务器: [电脑IP]
# 4. 端口: 8080
# 5. 保存设置
```

#### 步骤4: 安装证书
```bash
# 1. 手机浏览器访问: http://chls.pro/ssl
# 2. 下载证书文件
# 3. 安装证书到系统
```

#### 步骤5: 开始抓包
```bash
# 1. Charles中点击"Start Recording"
# 2. 打开翼支付应用
# 3. 进入权益商城
# 4. 刷新页面或点击活动
# 5. 在Charles中查找包含data/key/sign的请求
```

### 方案2: 网络层抓包 (成功率中等 ⭐⭐⭐)

#### 使用tcpdump抓包
```bash
# 在设备上执行
adb shell su -c "tcpdump -i any -s 0 -w /sdcard/bestpay.pcap host mapi-h5.bestpay.com.cn"

# 然后使用Wireshark分析pcap文件
```

### 方案3: 静态分析 (技术要求高 ⭐⭐⭐⭐)

#### 分析关键SO库
```bash
# 使用IDA Pro分析
# 重点文件: libmpaas_crypto.so (41.8 KB)
# 查找: AES加密函数、SM2实现、密钥生成逻辑
```

#### 分析配置文件
```bash
# 已提取的关键配置:
# - mpaas_netconfig.properties (SM2公钥)
# - grouplist.json (完整加密配置)
# - bankinfo.json (RSA/SM2配置)
```

### 方案4: 模拟器调试 (成功率高 ⭐⭐⭐⭐)

#### 使用模拟器环境
```bash
# 1. 下载夜神模拟器或Genymotion
# 2. 安装翼支付APK
# 3. 安装Xposed框架
# 4. 使用JustTrustMe绕过SSL Pinning
# 5. 配合Frida进行Hook
```

## 🛠️ 可用的调试工具

### 已生成的工具文件
- `mpaas_hook.js` - Frida Hook脚本
- `simple_hook.js` - 简化Hook脚本
- `bestpay_decryptor_template.py` - 解密工具模板
- `charles_proxy_guide.md` - Charles配置指南

### 使用方法
```bash
# 1. Charles抓包 (推荐)
# 按照charles_proxy_guide.md配置

# 2. Frida Hook (如果应用不闪退)
frida -U com.chinatelecom.bestpayclient -l simple_hook.js

# 3. 数据分析
python bestpay_decryptor_template.py
```

## 🎯 预期获得的数据

### 目标请求格式
```json
{
  "data": "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA2...",
  "key": "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOb...",
  "sign": "751C0392CAB814248DBB7A4B1BCE34CD",
  "productNo": "13341293407",
  "encyType": "C005",
  "fromChannelId": "yzf_app"
}
```

### 加密流程分析
```
1. 生成随机AES密钥 (256位)
2. 使用AES加密请求数据 → data字段
3. 使用SM2公钥加密AES密钥 → key字段
4. 计算MD5签名 → sign字段
5. 设置encyType: C005
6. 发送到API端点
```

## 📊 成功标志

### 抓包成功的标志
- 看到包含data/key/sign字段的POST请求
- 请求URL包含queryActivityInfo
- data字段是Base64编码的长字符串
- key字段是Base64编码的加密密钥

### Hook成功的标志
- 看到AES加密函数被调用
- 捕获到AES密钥
- 看到Base64编码的长数据

## 🎉 下一步行动

### 立即执行 (推荐顺序)
1. **配置Charles代理** - 最简单有效
2. **在翼支付中操作** - 进入权益商城
3. **分析抓包结果** - 查找加密请求
4. **使用解密工具** - 分析数据结构

### 如果Charles成功
- 收集多个请求样本
- 分析加密数据规律
- 尝试解密data字段
- 分析密钥生成机制

### 如果需要深入分析
- 使用IDA分析libmpaas_crypto.so
- 查找AES密钥生成函数
- 分析SM2私钥获取方法
- 实现完整解密工具

## 💡 故障排除

### Charles抓包问题
- 确保手机和电脑在同一网络
- 检查代理设置是否正确
- 重新安装证书
- 尝试不同的代理端口

### Frida Hook问题
- 应用有反调试保护
- 尝试使用模拟器环境
- 使用Xposed绕过检测
- 分析反调试机制

### 网络请求问题
- 应用可能使用证书绑定
- 尝试使用JustTrustMe
- 分析网络库实现
- 查找证书绕过方法

---

**总结**: Charles代理抓包是目前最可行的方案，建议优先尝试！
