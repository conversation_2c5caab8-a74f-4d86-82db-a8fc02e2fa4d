package s.h.e.l.l;

import android.app.Application;
import android.content.pm.ApplicationInfo;

/* loaded from: classes.dex */
public final class N {
    static boolean la = true;

    /* JADX WARN: Removed duplicated region for block: B:11:0x001d A[Catch: Throwable -> 0x0083, TryCatch #6 {Throwable -> 0x0083, blocks: (B:9:0x0019, B:11:0x001d, B:13:0x0039, B:31:0x006e, B:33:0x0074, B:35:0x007d, B:44:0x0095, B:46:0x009e, B:39:0x0085, B:41:0x008e), top: B:70:0x0019, inners: #8 }] */
    /* JADX WARN: Removed duplicated region for block: B:31:0x006e A[Catch: Throwable -> 0x0083, TRY_ENTER, TryCatch #6 {Throwable -> 0x0083, blocks: (B:9:0x0019, B:11:0x001d, B:13:0x0039, B:31:0x006e, B:33:0x0074, B:35:0x007d, B:44:0x0095, B:46:0x009e, B:39:0x0085, B:41:0x008e), top: B:70:0x0019, inners: #8 }] */
    static {
        /*
            r1 = 0
            r0 = 1
            s.h.e.l.l.N.la = r0
            r3 = 0
            java.lang.Object r0 = new java.lang.Object     // Catch: java.lang.Exception -> L52 java.lang.Throwable -> L61
            r0.<init>()     // Catch: java.lang.Exception -> L52 java.lang.Throwable -> L61
            java.lang.Object r2 = new java.lang.Object     // Catch: java.lang.Throwable -> Lb0 java.lang.Exception -> Lb5
            r2.<init>()     // Catch: java.lang.Throwable -> Lb0 java.lang.Exception -> Lb5
            if (r0 == 0) goto L14
            r0.hashCode()     // Catch: java.lang.Exception -> Laa
        L14:
            if (r2 == 0) goto L19
            r2.hashCode()     // Catch: java.lang.Exception -> Lad
        L19:
            boolean r0 = s.h.e.l.l.N.la     // Catch: java.lang.Throwable -> L83
            if (r0 == 0) goto L6e
            java.lang.StringBuilder r0 = new java.lang.StringBuilder     // Catch: java.lang.Throwable -> L83
            java.lang.String r1 = s.h.e.l.l.S.p     // Catch: java.lang.Throwable -> L83
            java.lang.String r1 = java.lang.String.valueOf(r1)     // Catch: java.lang.Throwable -> L83
            r0.<init>(r1)     // Catch: java.lang.Throwable -> L83
            java.lang.String r1 = "/libexec.so"
            java.lang.StringBuilder r0 = r0.append(r1)     // Catch: java.lang.Throwable -> L83
            java.lang.String r0 = r0.toString()     // Catch: java.lang.Throwable -> L83
            java.lang.System.load(r0)     // Catch: java.lang.Throwable -> L83
            boolean r0 = s.h.e.l.l.S.m     // Catch: java.lang.Throwable -> L83
            if (r0 == 0) goto L51
            java.lang.StringBuilder r0 = new java.lang.StringBuilder     // Catch: java.lang.Throwable -> L83
            java.lang.String r1 = s.h.e.l.l.S.p     // Catch: java.lang.Throwable -> L83
            java.lang.String r1 = java.lang.String.valueOf(r1)     // Catch: java.lang.Throwable -> L83
            r0.<init>(r1)     // Catch: java.lang.Throwable -> L83
            java.lang.String r1 = "/libexecmain.so"
            java.lang.StringBuilder r0 = r0.append(r1)     // Catch: java.lang.Throwable -> L83
            java.lang.String r0 = r0.toString()     // Catch: java.lang.Throwable -> L83
            java.lang.System.load(r0)     // Catch: java.lang.Throwable -> L83
        L51:
            return
        L52:
            r0 = move-exception
            r0 = r1
        L54:
            if (r0 == 0) goto L59
            r0.hashCode()     // Catch: java.lang.Exception -> La4
        L59:
            if (r1 == 0) goto L19
            r3.hashCode()     // Catch: java.lang.Exception -> L5f
            goto L19
        L5f:
            r0 = move-exception
            goto L19
        L61:
            r0 = move-exception
            r2 = r1
        L63:
            if (r2 == 0) goto L68
            r2.hashCode()     // Catch: java.lang.Exception -> La6
        L68:
            if (r1 == 0) goto L6d
            r3.hashCode()     // Catch: java.lang.Exception -> La8
        L6d:
            throw r0
        L6e:
            boolean r0 = x()     // Catch: java.lang.Throwable -> L83
            if (r0 != 0) goto L85
            java.lang.String r0 = "exec"
            java.lang.System.loadLibrary(r0)     // Catch: java.lang.Throwable -> L83
            boolean r0 = s.h.e.l.l.S.m     // Catch: java.lang.Throwable -> L83
            if (r0 == 0) goto L51
            java.lang.String r0 = "execmain"
            java.lang.System.loadLibrary(r0)     // Catch: java.lang.Throwable -> L83
            goto L51
        L83:
            r0 = move-exception
            goto L51
        L85:
            java.lang.String r0 = "exec_x86"
            java.lang.System.loadLibrary(r0)     // Catch: java.lang.Throwable -> L94
            boolean r0 = s.h.e.l.l.S.m     // Catch: java.lang.Throwable -> L94
            if (r0 == 0) goto L51
            java.lang.String r0 = "execmain_x86"
            java.lang.System.loadLibrary(r0)     // Catch: java.lang.Throwable -> L94
            goto L51
        L94:
            r0 = move-exception
            java.lang.String r0 = "exec"
            java.lang.System.loadLibrary(r0)     // Catch: java.lang.Throwable -> L83
            boolean r0 = s.h.e.l.l.S.m     // Catch: java.lang.Throwable -> L83
            if (r0 == 0) goto L51
            java.lang.String r0 = "execmain"
            java.lang.System.loadLibrary(r0)     // Catch: java.lang.Throwable -> L83
            goto L51
        La4:
            r0 = move-exception
            goto L59
        La6:
            r2 = move-exception
            goto L68
        La8:
            r1 = move-exception
            goto L6d
        Laa:
            r0 = move-exception
            goto L14
        Lad:
            r0 = move-exception
            goto L19
        Lb0:
            r2 = move-exception
            r4 = r2
            r2 = r0
            r0 = r4
            goto L63
        Lb5:
            r2 = move-exception
            goto L54
        */
        throw new UnsupportedOperationException("Method not decompiled: s.h.e.l.l.N.<clinit>():void");
    }

    public static native ClassLoader al(ClassLoader classLoader, ApplicationInfo applicationInfo, String str, String str2);

    public static native byte[] b2b(byte[] bArr, int i);

    public static native boolean l(Application application, String str);

    public static native void m(String str, int i);

    public static native boolean r(Application application, String str);

    public static native boolean ra(Application application, String str);

    public static native void sa(String str, String str2);

    /* JADX WARN: Removed duplicated region for block: B:13:0x002c A[Catch: Exception -> 0x0074, TRY_LEAVE, TryCatch #9 {Exception -> 0x0074, blocks: (B:11:0x001c, B:13:0x002c), top: B:67:0x001c }] */
    /* JADX WARN: Removed duplicated region for block: B:16:0x003b A[Catch: Exception -> 0x0067, TRY_ENTER, TRY_LEAVE, TryCatch #7 {Exception -> 0x0067, blocks: (B:9:0x0016, B:16:0x003b), top: B:65:0x0016 }] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    public static boolean x() throws java.lang.Throwable {
        /*
            r1 = 0
            r3 = 0
            java.lang.Object r0 = new java.lang.Object     // Catch: java.lang.Exception -> L45 java.lang.Throwable -> L54
            r0.<init>()     // Catch: java.lang.Exception -> L45 java.lang.Throwable -> L54
            java.lang.Object r2 = new java.lang.Object     // Catch: java.lang.Throwable -> L76 java.lang.Exception -> L7b
            r2.<init>()     // Catch: java.lang.Throwable -> L76 java.lang.Exception -> L7b
            if (r0 == 0) goto L11
            r0.hashCode()     // Catch: java.lang.Exception -> L70
        L11:
            if (r2 == 0) goto L16
            r2.hashCode()     // Catch: java.lang.Exception -> L72
        L16:
            java.lang.String r0 = s.h.e.l.l.S.a()     // Catch: java.lang.Exception -> L67
            r1 = 20
            byte[] r1 = new byte[r1]     // Catch: java.lang.Exception -> L74
            java.io.FileInputStream r2 = new java.io.FileInputStream     // Catch: java.lang.Exception -> L74
            java.io.File r3 = new java.io.File     // Catch: java.lang.Exception -> L74
            java.lang.String r4 = "/system/bin/linker"
            r3.<init>(r4)     // Catch: java.lang.Exception -> L74
            r2.<init>(r3)     // Catch: java.lang.Exception -> L74
            if (r2 == 0) goto L39
            r2.read(r1)     // Catch: java.lang.Exception -> L74
            r2.close()     // Catch: java.lang.Exception -> L74
            r2 = 18
            r1 = r1[r2]     // Catch: java.lang.Exception -> L74
            switch(r1) {
                case 3: goto L61;
                case 40: goto L64;
                default: goto L39;
            }
        L39:
            if (r0 == 0) goto L68
            java.lang.String r1 = "x86"
            boolean r0 = r0.contains(r1)     // Catch: java.lang.Exception -> L67
            if (r0 == 0) goto L68
            r0 = 1
        L44:
            return r0
        L45:
            r0 = move-exception
            r0 = r1
        L47:
            if (r0 == 0) goto L4c
            r0.hashCode()     // Catch: java.lang.Exception -> L6a
        L4c:
            if (r1 == 0) goto L16
            r3.hashCode()     // Catch: java.lang.Exception -> L52
            goto L16
        L52:
            r0 = move-exception
            goto L16
        L54:
            r0 = move-exception
            r2 = r1
        L56:
            if (r2 == 0) goto L5b
            r2.hashCode()     // Catch: java.lang.Exception -> L6c
        L5b:
            if (r1 == 0) goto L60
            r3.hashCode()     // Catch: java.lang.Exception -> L6e
        L60:
            throw r0
        L61:
            java.lang.String r0 = "x86"
            goto L39
        L64:
            java.lang.String r0 = "armeabi"
            goto L39
        L67:
            r0 = move-exception
        L68:
            r0 = 0
            goto L44
        L6a:
            r0 = move-exception
            goto L4c
        L6c:
            r2 = move-exception
            goto L5b
        L6e:
            r1 = move-exception
            goto L60
        L70:
            r0 = move-exception
            goto L11
        L72:
            r0 = move-exception
            goto L16
        L74:
            r1 = move-exception
            goto L39
        L76:
            r2 = move-exception
            r5 = r2
            r2 = r0
            r0 = r5
            goto L56
        L7b:
            r2 = move-exception
            goto L47
        */
        throw new UnsupportedOperationException("Method not decompiled: s.h.e.l.l.N.x():boolean");
    }
}
