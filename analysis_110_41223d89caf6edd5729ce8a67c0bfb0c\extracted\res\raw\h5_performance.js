window.AlipayH5Performance||function(){var n=window.__alipayConsole__||window.console,t=n.log,a=function(e){t.call(n,"{bridge_token}h5container.message: "+e)},o={},i={};function r(e){var n=navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);return 49<=(n?parseInt(n[2],10):0)?(new Date).getTime():e.timeStamp?e.timeStamp:(new Date).getTime()}function s(e){return Object.prototype.toString.call(e).replace(/\[object (\w+)\]/,"$1").toLowerCase()}var d={init:function(){this.init=null,this.monitorDOMReady(),this.monitorPageLoad(),this.monitorNavigationStart(),this.monitorJSErrors(),this.monitorDNSTime(),this.monitorCacheRate(),this.monitorMixedContent()},monitorDOMReady:function(){var n=this;/complete|loaded|interactive/.test(document.readyState)?(n.pushMessage("monitor",{name:"domReady",value:(new Date).getTime(),extra:"completed"}),n.sendSignal()):document.addEventListener("DOMContentLoaded",function(e){n.pushMessage("monitor",{name:"domReady",value:r(e),extra:"complete"}),n.sendSignal()},!0)},monitorPageLoad:function(){var n=function(e){var n=""+(new Date).getTime()+Math.random();invokeMsg=JSON.stringify({func:"monitorH5Performance",param:{data:[{name:"pageLoad",value:e,extra:"load"}]},msgType:"call",clientId:n}),a(invokeMsg)};/complete|loaded|interactive/.test(document.readyState)?n((new Date).getTime()):window.addEventListener("load",function(e){n(r(e))},!0)},monitorNavigationStart:function(){var n=this;/complete|loaded|interactive/.test(document.readyState)?window.performance&&window.performance.timing&&(n.pushMessage("monitor",{name:"navigationStart",value:window.performance.timing.navigationStart}),n.sendSignal()):window.addEventListener("load",function(e){window.performance&&window.performance.timing&&(n.pushMessage("monitor",{name:"navigationStart",value:window.performance.timing.navigationStart}),n.sendSignal())},!0)},monitorJSErrors:function(){var t=this;window.addEventListener("error",function(e){e.message&&(t.pushMessage("monitor",{name:"jsErrors",value:e.message,filename:e.filename,lineno:e.lineno,colno:e.colno,date:e.timeStamp}),t.sendSignal()),e.error&&e.error.native_details&&console.log("error native_details: "+e.error.native_details.substring(0,5e4))},!0);var n=function(e){if(e.reason){var n=e.reason;if("object"==typeof e.reason)try{n=JSON.stringify(e.reason)}catch(e){}t.pushMessage("monitor",{name:"jsErrors",value:"Unhandled Promise Rejection:"+n,filename:location.href.split("?")[0]}),t.sendSignal()}};if("undefined"!=typeof PromiseRejectionEvent)window.addEventListener("unhandledrejection",function(e){n(e)},!1);else{var o=window.onunhandledrejection;window.onunhandledrejection=function(e){try{o(e)}catch(e){}n(e)}}},monitorDNSTime:function(){var n=this;window.addEventListener("load",function(e){window.performance&&window.performance.timing?n.pushMessage("monitor",{name:"dns",value:window.performance.timing.domainLookupEnd-window.performance.timing.domainLookupStart,extra:"support"}):n.pushMessage("monitor",{name:"dns",value:"",extra:"notsupport"}),n.sendSignal()},!0)},monitorMixedContent:function(){var n=this,t=[];/complete|loaded|interactive/.test(document.readyState)?("https:"==window.location.protocol&&[].slice.call(document.querySelectorAll('link[rel=stylesheet][href^="http:"], script[src^="http:"]')).forEach(function(e){t.push(e.tagName+":"+(e.src||e.href))}),0<t.length&&(n.pushMessage("monitor",{name:"mixedContent",value:t.join("@|@")}),n.sendSignal())):document.addEventListener("DOMContentLoaded",function(e){"https:"==window.location.protocol&&[].slice.call(document.querySelectorAll('link[rel=stylesheet][href^="http:"], script[src^="http:"]')).forEach(function(e){t.push(e.tagName+":"+(e.src||e.href))}),0<t.length&&(n.pushMessage("monitor",{name:"mixedContent",value:t.join("@|@")}),n.sendSignal())},!0)},monitorCacheRate:function(){var o,a=this,i={name:"cacheRate"};window.addEventListener("load",function(e){if(window.performance&&"function"==typeof window.performance.getEntriesByType&&(o=window.performance.getEntriesByType("resource"))){if(0<o.length){for(var n=0,t=0;t<o.length;t++)0===o[t].duration&&n++;i.value=(n/o.length).toFixed(4)}else i.value=0;i.extra="support"}else i.value="",i.extra="notsupport";a.pushMessage("monitor",i),a.sendSignal()},!0)},sendSignal:function(e,t){var o=this;e="number"==typeof e&&0<=e?e:500,t=t||"monitor",clearTimeout(i[t]),i[t]=setTimeout(function(){var e=""+(new Date).getTime()+Math.random(),n=JSON.stringify({func:"report"==t?t+"H5Abnormal":t+"H5Performance",param:{data:o.getMessage(t)},msgType:"call",clientId:e});a(n)},e)},pushMessage:function(e,n){e=e||"monitor",o&&"array"==s(o[e])||(o[e]=[]),o[e].push(n)},getMessage:function(e){e=e||"monitor",o&&"array"==s(o[e])||(o[e]=[]);var n=o[e];return o[e]=[],n}};d.init(),window.AlipayH5Performance={addTrackData:function(e,n){n=n||"monitor",e.value&&(e.value+="|time="+(new Date).getTime()),d.pushMessage(n,e),d.sendSignal(0)},reportBizReady:function(e){var n="number"==typeof e?e:Date.now();d.pushMessage("monitor",{name:"availableTime",value:""+n}),d.sendSignal(0)},addTimeReport:function(e){var n="";for(var t in e)n+=(""==n?"":"&")+t+"="+e[t];this.addTrackData({name:"timeReport",value:n})},pushMessage:function(e,n){d.pushMessage(e,n)},sendSignal:function(e){d.sendSignal(0,e)},init:function(){var e=window.PERF_BIZ_READY;e&&this.reportBizReady(e)},version:"1.2"},window.AlipayH5Performance.init()}();
// do not modify
