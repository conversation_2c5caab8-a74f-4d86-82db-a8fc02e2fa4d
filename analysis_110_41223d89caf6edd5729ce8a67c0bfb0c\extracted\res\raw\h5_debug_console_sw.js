["log","info","error","debug","warn"].forEach(function(type,i){var MAX_LENGTH=4096;var _type="_"+type+"_";if(console[_type]){return}console[_type]=console[type];var toString=function(content){var result=content;if(content instanceof Error){const tmpArr=[];Object.getOwnPropertyNames(content).forEach(function(key){tmpArr.push("Error "+key+":"+content[key])});result=tmpArr.join("\\n")}else{if(typeof content==="object"){try{result=JSON.stringify(content)}catch(e){}}}if(result&&typeof result!=="string"&&result.toString){result=result.toString()}if(result&&result.slice){result=result.slice(0,MAX_LENGTH)}else{result=""}return result};console[type]=(function(innerType){return function(){console[innerType].apply(console,arguments);result=[];for(var i=0;i<arguments.length;i++){result.push(toString(arguments[i]))}var actionData={data:{method:"tinyDebugConsole",param:{content:result.join(" "),type:type}},action:"internalAPI"};var apiQueryString=encodeURIComponent(JSON.stringify(actionData));var url="https://alipay.kylinBridge/?data="+apiQueryString; fetch(url,{'mode':'no-cors'}).then(() => {}).catch(error => {})}})(_type)});