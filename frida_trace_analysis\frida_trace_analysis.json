{"timestamp": "2025-06-05T18:42:49.667333", "target_package": "com.chinatelecom.bestpayclient", "trace_results": [{"name": "系统调用跟踪", "success": true, "output": "Failed to spawn: unable to connect to remote frida-server: closed\n", "error": ""}, {"name": "加密函数跟踪", "success": true, "output": "Failed to spawn: unable to connect to remote frida-server: closed\n", "error": ""}, {"name": "Java方法跟踪", "success": true, "output": "Failed to spawn: unable to connect to remote frida-server: closed\n", "error": ""}], "findings": []}