"use strict";!function(n){function t(){var t=arguments,i=function(){n.AlipayJSBridge.call.apply(null,t)};n.AlipayJSBridge?i():document.addEventListener("AlipayJSBridgeReady",i,!1)}n.navigator.geolocation.getCurrentPosition=function(n){t("getLocation",{requestType:2},function(t){var i={coords:{accuracy:50,altitude:null,altitudeAccuracy:null,heading:null,latitude:null,longitude:null,speed:null},timestamp:+new Date};for(var a in t)t.hasOwnProperty(a)&&(i.coords[a]=t[a]);n&&n(i)})}}(window);