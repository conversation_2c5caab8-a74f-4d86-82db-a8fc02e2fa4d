var AlipayJSBridge;!function(){var a=self.__nativeLog__,o=self.__nativeCreateTimer__,t=self.__nativeDeleteTimer__,s=self.__nativeFlushQueue__;function e(){}function n(){return[]}function r(){try{for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];0<t&&(e+=" "),n instanceof Error?e+=n.message+"\n"+n.stack:e+="object"==typeof n?JSON.stringify(n):n}a(e)}catch(e){}}function i(e,t){var n=e[0];if("string"==typeof n)n=new Function(n);else if("function"!=typeof n)return-1;var a=0|e[1],r=Array.prototype.slice.call(e,2);return o(function(){try{n.apply(self,r)}catch(e){console.error("###\n"+e.stack+"\n###"),"function"==typeof self.onerror&&self.onerror(e)}},a,t)}self.__nativeLog__=null,self.__nativeCreateTimer__=null,self.__nativeDeleteTimer__=null,self.__nativeFlushQueue__=null,self.setTimeout=function(){return i(arguments,!1)},self.setInterval=function(){return i(arguments,!0)},self.clearTimeout=self.clearInterval=function(e){t(0|e)},self.close=e,self.postMessage=e,self.performance={clearMarks:e,clearMeasures:e,clearResourceTimings:e,getEntries:n,getEntriesByName:n,getEntriesByType:n,setResourceTimingBufferSize:e,mark:e,measure:e,now:function(){return Date.now()},toJSON:function(){return{}}},self.console={log:r,info:r,debug:r,warn:r,error:r,exception:r,clear:e,count:e,countReset:e,dir:e,dirxml:e,group:e,groupCollapsed:e,groupEnd:e,profile:e,profileEnd:e,table:e,time:e,timeEnd:e,timeLog:e,timeStamp:e,trace:e,assert:function(){arguments[0]||console.warn("Assertion failed:",Array.prototype.slice.call(arguments).slice(1))}};var l={},c={};function f(e){this.name=e}function d(e){return new f(e)}function u(e,t){var n=d(e),a=!1;if(l[e])for(var r=0;r<l[e].length;r++)n.data=t,l[e][r].fn(n),n.isDefaultPrevented()&&(a=!0);return!a}f.prototype={preventDefault:function(){this._preventDefault=!0},initEvent:function(e){this.name=e},isDefaultPrevented:function(){return!!this._preventDefault}};var p="https://appx/",g={addEventListener:function(e,t){l[e]||(l[e]=[]);var n={};n.fn=t,l[e].push(n)},removeEventListener:function(e,t){if(l[e])for(var n=0;n<l[e].length;n++)if(t===l[e][n].fn){l[e].splice(n,1),0===l[e].length&&delete l[e];break}},trigger:function(e,t){var n=!u(e,t);if(t.callbackId){var a=t;a.callbackId=t.callbackId,a[e+"EventCanceled"]=n}},sendMessageQueue:[],createEvent:d,dispatchEvent:u,location:{_href:p,search:"",hash:"",host:"appx",hostname:"appx",origin:p,pathname:"/",port:"",protocol:"https:",reload:function(){},replace:function(){},assign:function(){}},title:"",origin:p};self.addEventListener=function(e,t){c[e]||(c[e]=[]);var n={};n.fn=t,c[e].push(n)},self.removeEventListener=function(e,t){if(c[e])for(var n=0;n<c[e].length;n++)if(t===c[e][n].fn){c[e].splice(n,1),0===c[e].length&&delete c[e];break}};var v={};g.addEventListener("push",function(e){var t=e.data.data;if(console.log("onMessage push"),c.push)for(var n=0;n<c.push.length;n++)e.data=t,c.push[n].fn(e)}),g.addEventListener("message",function(e){if(e.data&&"messagePort"!=e.data.type&&!e.data.beforeunload){var t=e.data.data,n=e.data.eventPorts,a=e.data.viewId,r=e.data.pageId||0;if(n&&n[0]&&(n[0]=(s=a,l=r,(f=n[0]).postMessage=function(e){var t={data:e,type:"messagePort",msgPortId:f.id,viewId:s,pageId:l};AlipayJSBridge.call("postMessage",t)},v["m_"+s]||(v["m_"+s]={}),v["m_"+s]["p_"+l]||(v["m_"+s]["p_"+l]={}),v["m_"+s]["p_"+l][f.id]=f)),console.log("onMessage eventPorts"),c.message)for(var o=0;o<c.message.length;o++)e.data=t,e.ports=n,c.message[o].fn(e)}else if(e.data&&"messagePort"==e.data.type&&e.data.msgPortId){a=e.data.viewId,r=e.data.pageId||0;if(!v["m_"+a]||!v["m_"+a]["p_"+r])return void console.error("unknown view",e,v);var i=v["m_"+a]["p_"+r][e.data.msgPortId];i&&i.onmessage&&i.onmessage({data:e.data.data})}else console.log("unknown event",e);var s,l,f}),Object.defineProperty(g.location,"href",{get:function(){return g.location._href},set:function(e){if("string"==typeof e){if(e=e.trim(),AlipayJSBridge._trimLocationHref){var t=AlipayJSBridge._trimLocationHref(e);if(t)for(var n in t)"href"!=n&&"function"!=typeof t[n]&&(g.location[n]=t[n])}g.location._href=e}}});for(var h="NEBULATYPEINFO",_="ArrayBuffer",m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",y=new Uint8Array(256),I=0;I<m.length;I++)y[m.charCodeAt(I)]=I;function b(e){this.message=e}function k(e){var t,n=new Uint8Array(e),a=n.length,r="";for(t=0;t<a;t+=3)r+=m[n[t]>>2],r+=m[(3&n[t])<<4|n[t+1]>>4],r+=m[(15&n[t+1])<<2|n[t+2]>>6],r+=m[63&n[t+2]];return a%3==2?r=r.substring(0,r.length-1)+"=":a%3==1&&(r=r.substring(0,r.length-2)+"=="),r}function w(e){var t,n,a,r,o,i=.75*e.length,s=e.length,l=0;"="===e[e.length-1]&&(i--,"="===e[e.length-2]&&i--);var f=new ArrayBuffer(i),c=new Uint8Array(f);for(t=0;t<s;t+=4)n=y[e.charCodeAt(t)],a=y[e.charCodeAt(t+1)],r=y[e.charCodeAt(t+2)],o=y[e.charCodeAt(t+3)],c[l++]=n<<2|a>>4,c[l++]=(15&a)<<4|r>>2,c[l++]=(3&r)<<6|63&o;return f}function A(e){if(e&&e[h]){var t=e[h];if(t)for(var n in t)if(t.hasOwnProperty(n)){var a=t[n];if(a.type)a.type===_&&(e[n]=w(e[n]))}}return e}(b.prototype=new Error).name="InvalidCharacterError",self.btoa=function(e){for(var t,n,a=String(e),r=0,o=m,i="";a.charAt(0|r)||(o="=",r%1);i+=o.charAt(63&t>>8-r%1*8)){if(255<(n=a.charCodeAt(r+=.75)))throw new b("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");t=t<<8|n}return i},self.atob=function(e){var t=String(e).replace(/=+$/,"");if(t.length%4==1)throw new b("'atob' failed: The string to be decoded is not correctly encoded.");for(var n,a,r=0,o=0,i="";a=t.charAt(o++);~a&&(n=r%4?64*n+a:a,r++%4)?i+=String.fromCharCode(255&n>>(-2*r&6)):0)a=m.indexOf(a);return i};var E={},C=1;AlipayJSBridge={call:function(e,t,n){if("string"==typeof e){"function"==typeof t?(n=t,t=null):"object"!=typeof t&&(t=null);var a=e+"##"+C++;if("function"==typeof n&&(E[a]=n),t&&t.callbackId){var r={responseId:t.callbackId,responseData:t};delete t.callbackId}else{try{var o=function(e){var t=e;for(var n in e)if(e.hasOwnProperty(n)){var a=e[n];a instanceof ArrayBuffer&&(e[n]=k(a),t[h]||(t[h]={}),t[h][n]={type:_})}return t}(t)}catch(e){console.error(e),o={}}r={callbackId:a,handlerName:e,data:o}}var i=t&&"viewId"in t?0|t.viewId:-1;s(e,i,JSON.stringify(r))}},_invokeJS:function(e,t,n){var a="string"==typeof e?JSON.parse(e):e;if(1!==a.__FastPath__)if(a.responseId){r=E[a.responseId];"boolean"==typeof a.keepCallback&&a.keepCallback||delete E[a.responseId],"function"==typeof r&&r(A(a.responseData))}else a.handlerName&&(a.callbackId&&(a.data=a.data||{},a.data.callbackId=a.callbackId),g.trigger(a.handlerName,a.data));else if(delete a.__FastPath__,"call"===a.msgType){var r="postMessage"===a.func?"message":a.func,o=a.param||{};a.clientId&&(o.callbackId=a.clientId),t&&(o.pageId=t),n&&(o.viewId=n),g.trigger(r,o)}else{var r=E[a.clientId];"boolean"==typeof a.keepCallback&&a.keepCallback||delete E[a.clientId],"function"==typeof r&&r(A(a.param))}}},self.history={length:1},self.origin=g.origin,self.location=g.location,self.document=g,self.window=self}();