#!/usr/bin/env python3
# 翼支付解密工具模板

import base64

class BestPayDecryptor:
    def __init__(self):
        # mPaaS公钥
        self.public_key = """-----BEGIN PUBLIC KEY-----
MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEg3wUFJxlGBKFpE11weQETZ8F1X4RAmJ9GGrVZU6oVXybVUcARKAhZmnR1jcOAzcJKSJf/6jy31TL2huHIqo/3w==
-----E<PERSON> PUBLIC KEY-----"""
    
    def analyze_request(self, data, key, sign):
        """分析加密请求"""
        print(f"🔍 分析加密请求:")
        print(f"data长度: {len(data)}")
        print(f"key长度: {len(key)}")
        print(f"sign: {sign}")
        
        try:
            data_bytes = base64.b64decode(data)
            key_bytes = base64.b64decode(key)
            
            print(f"解码后data长度: {len(data_bytes)}")
            print(f"解码后key长度: {len(key_bytes)}")
            print(f"data前16字节: {data_bytes[:16].hex()}")
            print(f"key前16字节: {key_bytes[:16].hex()}")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")

# 使用示例
if __name__ == "__main__":
    decryptor = BestPayDecryptor()
    
    # 从抓包获取的示例数据
    sample_data = "iAJJA3SPbCu6Gmip2ZfICySO1kjyExaHZoQygDJY0dLFt4LyuaeHdMHRA29SYDA2"
    sample_key = "gT3XNxhMnlFJafK+H9PaaRqlmHiQlcvS3+7clgLrowf9b6gArxGtxvu7qaazvkOb"
    sample_sign = "751C0392CAB814248DBB7A4B1BCE34CD"
    
    decryptor.analyze_request(sample_data, sample_key, sample_sign)
