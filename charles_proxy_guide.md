# Charles代理抓包完整指南

## 🎯 目标
通过Charles代理抓包获取翼支付API的加密请求数据

## 📋 准备工作

### 1. 下载Charles
- 访问：https://www.charlesproxy.com/download/
- 下载Windows版本并安装
- 启动Charles

### 2. 获取电脑IP地址
```cmd
ipconfig
```
记录您的电脑IP地址，例如：*************

## 🔧 Charles配置

### 1. 启动Charles代理
- 打开Charles
- 菜单：Proxy → Proxy Settings
- 确认端口为8080 (默认)
- 勾选"Enable transparent HTTP proxying"

### 2. 配置SSL代理
- 菜单：Proxy → SSL Proxying Settings
- 勾选"Enable SSL Proxying"
- 点击"Add"添加域名：
  - Host: *.bestpay.com.cn
  - Port: 443

### 3. 安装证书
- 菜单：Help → SSL Proxying → Install Charles Root Certificate
- 按提示安装到"受信任的根证书颁发机构"

## 📱 手机配置

### 1. 连接同一WiFi
确保手机和电脑在同一WiFi网络

### 2. 配置手机代理
- 打开手机WiFi设置
- 长按当前连接的WiFi
- 选择"修改网络"或"高级设置"
- 代理设置：手动
- 代理服务器：[您的电脑IP] (如*************)
- 端口：8080
- 保存设置

### 3. 安装手机证书
- 在手机浏览器访问：http://chls.pro/ssl
- 下载证书文件
- 安装证书：
  - Android: 设置 → 安全 → 加密与凭据 → 从存储设备安装
  - 选择下载的证书文件
  - 设置证书名称并确认

## 🎯 抓包操作

### 1. 开始抓包
- 在Charles中点击"Start Recording"
- 清空当前会话：Session → Clear

### 2. 操作翼支付
- 打开翼支付应用
- 登录账户
- 进入"权益商城"
- 刷新页面或点击活动
- 浏览商品

### 3. 观察抓包结果
在Charles中查找：
- 域名：mapi-h5.bestpay.com.cn
- 路径：/gapi/equitymall/client/Activity/queryActivityInfo
- 方法：POST

### 4. 分析请求数据
找到目标请求后：
- 点击请求查看详情
- 在"Request"标签查看请求体
- 寻找包含以下字段的JSON：
  ```json
  {
    "data": "iAJJA3SPbCu6Gmip...",
    "key": "gT3XNxhMnlFJafK...",
    "sign": "751C0392CAB814248DBB7A4B1BCE34CD",
    "productNo": "13341293407",
    "encyType": "C005",
    "fromChannelId": "yzf_app"
  }
  ```

## 🔍 数据分析

### 1. 保存抓包数据
- 右键目标请求 → Copy cURL
- 或导出为HAR文件：File → Export Session

### 2. 提取关键信息
从抓包结果中提取：
- **data字段**: Base64编码的加密数据
- **key字段**: Base64编码的加密密钥
- **sign字段**: MD5签名
- **其他参数**: productNo, encyType等

### 3. 使用解密工具
```bash
python verify_decryption.py --data [data字段] --key [需要解密key字段获得AES密钥]
```

## ⚠️ 故障排除

### 问题1: 手机无法上网
- 检查电脑IP地址是否正确
- 确认Charles代理正在运行
- 尝试重启Charles

### 问题2: 证书安装失败
- 确保从 http://chls.pro/ssl 下载证书
- Android可能需要设置屏幕锁定
- 尝试手动导入证书文件

### 问题3: 看不到HTTPS流量
- 确认SSL Proxying已启用
- 检查域名配置是否正确
- 重新安装证书

### 问题4: 翼支付检测到代理
- 尝试使用不同的代理端口
- 关闭Charles的"Enable transparent HTTP proxying"
- 使用手机热点而非WiFi

## 🎉 成功标志

当您看到以下内容时说明成功：
1. Charles中出现 mapi-h5.bestpay.com.cn 的请求
2. 请求体包含 data、key、sign 字段
3. 可以看到完整的加密请求数据

## 📊 后续分析

获得加密数据后：
1. 使用我们提供的解密工具分析
2. 对比多个请求找出规律
3. 分析密钥生成机制
4. 构建自动化解密工具
