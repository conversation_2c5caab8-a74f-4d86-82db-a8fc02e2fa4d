// 翼支付简化Hook脚本
console.log("🔐 启动翼支付简化Hook...");

Java.perform(function() {
    console.log("✅ Java环境已就绪");
    
    // Hook Base64编码 - 最基础的Hook
    try {
        var Base64 = Java.use("android.util.Base64");
        var originalEncode = Base64.encodeToString.overload('[B', 'int');
        
        originalEncode.implementation = function(input, flags) {
            var result = originalEncode.call(this, input, flags);
            
            // 只记录较长的编码数据
            if (input.length > 50) {
                console.log("\n📝 [Base64编码] " + new Date().toLocaleTimeString());
                console.log("原始长度: " + input.length + " 字节");
                console.log("编码长度: " + result.length + " 字符");
                console.log("编码结果: " + result.substring(0, 80) + "...");
                
                // 检查是否包含关键特征
                if (result.length > 200) {
                    console.log("🎯 可能的加密数据 (长度: " + result.length + ")");
                }
            }
            
            return result;
        };
        
        console.log("✅ Base64 Hook成功");
    } catch(e) {
        console.log("❌ Base64 Hook失败: " + e);
    }
    
    // Hook String构造 - 捕获字符串创建
    try {
        var String = Java.use("java.lang.String");
        var StringClass = Java.use("java.lang.String");
        
        // Hook字节数组构造函数
        StringClass.$init.overload('[B').implementation = function(bytes) {
            var result = this.$init(bytes);
            var str = this.toString();
            
            // 检查是否包含关键字段
            if (str.includes('"data":') || str.includes('"key":') || str.includes('"sign":')) {
                console.log("\n🎯 [关键字符串] " + new Date().toLocaleTimeString());
                console.log("发现加密请求数据!");
                console.log("内容: " + str);
            }
            
            return result;
        };
        
        console.log("✅ String Hook成功");
    } catch(e) {
        console.log("❌ String Hook失败: " + e);
    }
    
    console.log("🎉 Hook脚本加载完成!");
    console.log("💡 请在手机上操作翼支付应用...");
    console.log("📱 进入权益商城并刷新页面");
});
