# 📱 真机调试完整配置指南

## 🎯 环境状态
✅ Python 3.12.4 已安装  
✅ Frida 17.0.7 已安装  
✅ pycryptodome 已安装  
❌ ADB 需要配置  
❌ 设备连接待确认  

## 📋 真机调试步骤

### 第一步：配置ADB (5分钟)

#### 方法1：使用便携版ADB (推荐)
```bash
# 下载便携版ADB到当前目录
# 无需安装，直接使用
```

#### 方法2：手动下载配置
1. 访问：https://developer.android.com/studio/releases/platform-tools
2. 下载 "SDK Platform-Tools for Windows"
3. 解压到 `C:\adb\` 目录
4. 添加 `C:\adb\platform-tools` 到系统PATH

### 第二步：手机设置 (3分钟)

#### 启用开发者选项
1. 打开手机"设置"
2. 找到"关于手机"或"关于设备"
3. 连续点击"版本号"7次
4. 返回设置，找到"开发者选项"

#### 开启USB调试
1. 进入"开发者选项"
2. 开启"USB调试"
3. 开启"USB安装"(如果有)
4. 关闭"USB验证应用"(如果有)

#### 连接设置
1. 使用数据线连接手机到电脑
2. 手机会弹出"允许USB调试"对话框
3. 勾选"始终允许来自这台计算机"
4. 点击"确定"

### 第三步：安装翼支付应用

#### 从应用商店安装
- 华为手机：华为应用市场
- 小米手机：小米应用商店  
- OPPO/vivo：软件商店
- 其他：Google Play 或官网下载

#### 确保应用版本
- 建议使用最新版本
- 登录您的翼支付账户
- 确保可以正常使用

### 第四步：测试连接

#### 检查设备连接
```bash
# 使用便携版ADB
.\android-tools\platform-tools\adb.exe devices

# 或系统ADB (如果已配置PATH)
adb devices
```

期望输出：
```
List of devices attached
XXXXXXXXXX    device
```

#### 检查翼支付应用
```bash
# 检查应用是否安装
adb shell pm list packages | findstr bestpay

# 获取应用信息
adb shell dumpsys package com.chinatelecom.bestpayclient | findstr version
```

### 第五步：启动Frida调试

#### 测试Frida连接
```bash
# 列出设备上的进程
frida-ps -U

# 如果成功，会显示手机上运行的应用列表
```

#### 启动Hook调试
```bash
# 方法1：启动应用并Hook
frida -U -f com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js --no-pause

# 方法2：附加到运行中的应用
frida -U com.chinatelecom.bestpayclient -l bestpay_crypto_hook.js
```

## 🛠️ 便携版工具包

我为您准备了便携版工具，无需复杂配置：
